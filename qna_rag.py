#!/usr/bin/env python3
"""
Enhanced Universal Document QnA System with RAG and Vector Storage

Features:
- RAG (Retrieval-Augmented Generation) for intelligent document retrieval
- Vector storage for persistent document embeddings
- Duplicate document detection and reuse
- Semantic search across all processed documents
- Image analysis with GPT-4 Vision
- Support for all file types (PDF, Excel, Word, PowerPoint, etc.)

Usage:
    python3 qna_rag.py "file_url" "your question"
    python3 qna_rag.py "file_url"  # Interactive mode
    python3 qna_rag.py --search "query"  # Search across all documents
"""

import argparse
import hashlib
import json
import os
import subprocess
import tempfile
import requests
from pathlib import Path
from urllib.parse import urlparse
from datetime import datetime
from typing import List, Dict, Optional

try:
    import pandas as pd
    import openai
    import numpy as np
    from PIL import Image
    import base64
    import io
    import faiss
    LIBS_AVAILABLE = True
except ImportError:
    LIBS_AVAILABLE = False

# Try to import additional libraries for image extraction
try:
    import fitz  # PyMuPDF for PDF image extraction
    PDF_LIBS_AVAILABLE = True
except ImportError:
    PDF_LIBS_AVAILABLE = False


class DocumentVectorStore:
    """Vector storage system for documents with RAG capabilities."""
    
    def __init__(self, storage_path: str = "./data/rag_storage"):
        """Initialize vector storage."""
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.index_path = self.storage_path / "faiss_index"
        self.metadata_path = self.storage_path / "metadata.json"
        self.documents_path = self.storage_path / "documents.json"
        
        # Initialize FAISS index
        self.dimension = 1536  # OpenAI text-embedding-3-small dimension
        self.index = None
        self.metadata = []
        self.documents = {}
        
        self._load_or_create_index()
        self._load_metadata()
        self._load_documents()
    
    def _load_or_create_index(self):
        """Load existing FAISS index or create new one."""
        try:
            if self.index_path.exists():
                self.index = faiss.read_index(str(self.index_path))
                print(f"✅ Loaded existing FAISS index with {self.index.ntotal} vectors")
            else:
                self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
                print("🆕 Created new FAISS index")
        except Exception as e:
            print(f"⚠️  Error loading FAISS index: {e}")
            self.index = faiss.IndexFlatIP(self.dimension)
    
    def _load_metadata(self):
        """Load metadata for stored vectors."""
        try:
            if self.metadata_path.exists():
                with open(self.metadata_path, 'r') as f:
                    self.metadata = json.load(f)
                print(f"✅ Loaded metadata for {len(self.metadata)} vectors")
            else:
                self.metadata = []
        except Exception as e:
            print(f"⚠️  Error loading metadata: {e}")
            self.metadata = []
    
    def _load_documents(self):
        """Load document registry."""
        try:
            if self.documents_path.exists():
                with open(self.documents_path, 'r') as f:
                    self.documents = json.load(f)
                print(f"✅ Loaded {len(self.documents)} processed documents")
            else:
                self.documents = {}
        except Exception as e:
            print(f"⚠️  Error loading documents: {e}")
            self.documents = {}
    
    def _save_index(self):
        """Save FAISS index."""
        try:
            faiss.write_index(self.index, str(self.index_path))
        except Exception as e:
            print(f"⚠️  Error saving index: {e}")
    
    def _save_metadata(self):
        """Save metadata."""
        try:
            with open(self.metadata_path, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            print(f"⚠️  Error saving metadata: {e}")
    
    def _save_documents(self):
        """Save document registry."""
        try:
            with open(self.documents_path, 'w') as f:
                json.dump(self.documents, f, indent=2)
        except Exception as e:
            print(f"⚠️  Error saving documents: {e}")
    
    def get_document_hash(self, url: str, content: str = "") -> str:
        """Generate hash for document identification."""
        # Use URL and content snippet for hash
        hash_input = f"{url}:{content[:1000]}"
        return hashlib.md5(hash_input.encode()).hexdigest()
    
    def document_exists(self, doc_hash: str) -> bool:
        """Check if document already exists in storage."""
        return doc_hash in self.documents
    
    def get_document_info(self, doc_hash: str) -> Optional[Dict]:
        """Get stored document information."""
        return self.documents.get(doc_hash)
    
    def store_document(self, doc_hash: str, url: str, content: str, 
                      file_type: str, image_analyses: List[str] = None):
        """Store document information and generate embeddings."""
        try:
            print("💾 Storing document in RAG system...")
            
            # Store document info
            doc_info = {
                'url': url,
                'file_type': file_type,
                'content_preview': content[:500],
                'full_content': content,
                'image_analyses': image_analyses or [],
                'processed_at': datetime.now().isoformat(),
                'chunk_count': 0
            }
            
            # Generate embeddings for content chunks
            embeddings = self._generate_embeddings(content, doc_hash, url)
            
            # Store in FAISS index
            if embeddings:
                vectors = np.array([emb['vector'] for emb in embeddings]).astype('float32')
                # Normalize vectors for cosine similarity
                faiss.normalize_L2(vectors)
                self.index.add(vectors)
                
                # Store metadata
                for emb in embeddings:
                    self.metadata.append({
                        'doc_hash': doc_hash,
                        'url': url,
                        'file_type': file_type,
                        'chunk_text': emb['text'],
                        'chunk_index': emb['chunk_index']
                    })
                
                doc_info['chunk_count'] = len(embeddings)
            
            # Store document
            self.documents[doc_hash] = doc_info
            
            # Save everything
            self._save_index()
            self._save_metadata()
            self._save_documents()
            
            print(f"✅ Document stored with {doc_info['chunk_count']} chunks")
            return True
            
        except Exception as e:
            print(f"❌ Error storing document: {e}")
            return False
    
    def _generate_embeddings(self, content: str, doc_hash: str, url: str) -> List[Dict]:
        """Generate embeddings for content chunks."""
        try:
            # Initialize OpenAI client
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                print("⚠️  No OpenAI API key for embeddings")
                return []
            
            client = openai.OpenAI(api_key=api_key)
            
            # Split content into chunks
            chunk_size = 500
            chunks = []
            for i in range(0, len(content), chunk_size):
                chunk_text = content[i:i+chunk_size]
                if chunk_text.strip():
                    chunks.append({
                        'text': chunk_text,
                        'chunk_index': len(chunks)
                    })
            
            print(f"🔢 Generating embeddings for {len(chunks)} chunks...")
            
            # Generate embeddings
            embeddings = []
            for chunk in chunks:
                try:
                    response = client.embeddings.create(
                        model="text-embedding-3-small",
                        input=chunk['text']
                    )
                    
                    embedding = response.data[0].embedding
                    embeddings.append({
                        'vector': embedding,
                        'text': chunk['text'],
                        'chunk_index': chunk['chunk_index']
                    })
                    
                except Exception as e:
                    print(f"⚠️  Error generating embedding for chunk {chunk['chunk_index']}: {e}")
                    continue
            
            print(f"✅ Generated {len(embeddings)} embeddings")
            return embeddings
            
        except Exception as e:
            print(f"❌ Error in embedding generation: {e}")
            return []
    
    def search_similar_content(self, query: str, top_k: int = 5) -> List[Dict]:
        """Search for similar content across all stored documents."""
        try:
            if self.index.ntotal == 0:
                return []
            
            # Generate query embedding
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                return []
            
            client = openai.OpenAI(api_key=api_key)
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input=query
            )
            
            query_vector = np.array([response.data[0].embedding]).astype('float32')
            faiss.normalize_L2(query_vector)
            
            # Search in FAISS index
            scores, indices = self.index.search(query_vector, min(top_k, self.index.ntotal))
            
            # Prepare results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.metadata):
                    metadata = self.metadata[idx]
                    results.append({
                        'score': float(score),
                        'doc_hash': metadata['doc_hash'],
                        'url': metadata['url'],
                        'file_type': metadata['file_type'],
                        'chunk_text': metadata['chunk_text'],
                        'chunk_index': metadata['chunk_index']
                    })
            
            return results
            
        except Exception as e:
            print(f"❌ Error in similarity search: {e}")
            return []
    
    def get_stats(self) -> Dict:
        """Get storage statistics."""
        return {
            'total_documents': len(self.documents),
            'total_vectors': self.index.ntotal if self.index else 0,
            'storage_path': str(self.storage_path),
            'dimension': self.dimension
        }


class EnhancedUniversalQA:
    """Enhanced Universal QA with RAG and vector storage."""
    
    def __init__(self, api_key: str):
        """Initialize the enhanced system."""
        self.api_key = api_key
        self.openai_client = openai.OpenAI(api_key=api_key)
        self.vector_store = DocumentVectorStore()
        self.temp_files = []
        self.current_doc_hash = None
        self.current_content = ""
        self.current_url = ""
    
    def process_document_with_rag(self, url: str) -> bool:
        """Process document with RAG capabilities."""
        try:
            print("🔄 Processing document with RAG system...")
            print(f"🔗 URL: {url}")
            
            # Generate preliminary hash (will be updated with content)
            preliminary_hash = self.vector_store.get_document_hash(url)
            
            # Check if document already exists
            if self.vector_store.document_exists(preliminary_hash):
                doc_info = self.vector_store.get_document_info(preliminary_hash)
                print(f"♻️  Document already processed on {doc_info['processed_at']}")
                print(f"📊 Using cached content with {doc_info['chunk_count']} chunks")
                
                self.current_doc_hash = preliminary_hash
                self.current_content = doc_info['full_content']
                self.current_url = url
                return True
            
            # Process new document (using existing qna.py logic)
            success = self._process_new_document(url)
            
            if success and self.current_content:
                # Generate final hash with content
                final_hash = self.vector_store.get_document_hash(url, self.current_content)
                
                # Store in RAG system
                self.vector_store.store_document(
                    final_hash, 
                    url, 
                    self.current_content,
                    self._get_file_type_from_url(url),
                    []  # Image analyses would be added here
                )
                
                self.current_doc_hash = final_hash
            
            return success
            
        except Exception as e:
            print(f"❌ Error in RAG processing: {e}")
            return False
    
    def _process_new_document(self, url: str) -> bool:
        """Process new document (simplified version of qna.py logic)."""
        try:
            # For S3 URLs, use existing docqa.py system
            if url.startswith('s3://'):
                result = subprocess.run([
                    "python", "docqa.py", "ingest", url, "--force"
                ], capture_output=True, text=True, env={**os.environ})
                
                if result.returncode == 0:
                    self.current_content = f"S3 Document processed: {url}"
                    self.current_url = url
                    return True
                else:
                    return False
            
            # For HTTP URLs, download and process
            elif url.startswith('http'):
                return self._download_and_process(url)
            
            # For local files
            else:
                return self._process_local_file(url)
                
        except Exception as e:
            print(f"❌ Error processing new document: {e}")
            return False
    
    def _download_and_process(self, url: str) -> bool:
        """Download and process HTTP URL."""
        try:
            response = requests.get(url, timeout=60)
            response.raise_for_status()
            
            # Save to temp file
            file_ext = self._get_extension_from_url(url)
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_ext)
            temp_filename = temp_file.name
            self.temp_files.append(temp_filename)
            
            with temp_file as f:
                f.write(response.content)
            
            # Process based on file type
            return self._process_local_file(temp_filename)
            
        except Exception as e:
            print(f"❌ Download error: {e}")
            return False
    
    def _process_local_file(self, file_path: str) -> bool:
        """Process local file (simplified)."""
        try:
            file_path = Path(file_path)
            file_ext = file_path.suffix.lower()
            
            if file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.current_content = f.read()
                return True
            elif file_ext in ['.xlsx', '.xls']:
                # Basic Excel processing
                df = pd.read_excel(file_path)
                self.current_content = f"Excel file with {len(df)} rows and {len(df.columns)} columns\n{df.head().to_string()}"
                return True
            else:
                self.current_content = f"File: {file_path.name} (Type: {file_ext})"
                return True
                
        except Exception as e:
            print(f"❌ File processing error: {e}")
            return False
    
    def _get_file_type_from_url(self, url: str) -> str:
        """Get file type from URL."""
        parsed = urlparse(url)
        ext = Path(parsed.path).suffix.lower()
        return ext.lstrip('.') if ext else 'unknown'
    
    def _get_extension_from_url(self, url: str) -> str:
        """Get file extension from URL."""
        parsed = urlparse(url)
        return Path(parsed.path).suffix or '.bin'
    
    def ask_question_with_rag(self, question: str) -> str:
        """Answer question using RAG system."""
        try:
            print("🔍 Searching across all documents for relevant information...")
            
            # Search for relevant content across all documents
            similar_content = self.vector_store.search_similar_content(question, top_k=5)
            
            # Prepare context from similar content
            context_parts = []
            if similar_content:
                context_parts.append("=== RELEVANT CONTENT FROM STORED DOCUMENTS ===")
                for i, content in enumerate(similar_content):
                    context_parts.append(f"Source {i+1} (Score: {content['score']:.3f}):")
                    context_parts.append(f"URL: {content['url']}")
                    context_parts.append(f"Content: {content['chunk_text'][:200]}...")
                    context_parts.append("")
            
            # Add current document content
            if self.current_content:
                context_parts.append("=== CURRENT DOCUMENT ===")
                context_parts.append(self.current_content[:2000])
            
            context = "\n".join(context_parts)
            
            # Generate answer using RAG context
            prompt = f"""
Based on the following context from multiple documents, please answer the question accurately and comprehensively.

CONTEXT:
{context}

QUESTION: {question}

Please provide a detailed answer based on the available information. If information comes from multiple sources, mention that. If specific data or facts are available, include them.

ANSWER:"""
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert document analyst with access to multiple documents. Provide comprehensive answers based on the available context from all relevant sources."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=1500,
                temperature=0.1,
            )
            
            answer = response.choices[0].message.content.strip()
            
            # Show sources used
            if similar_content:
                answer += f"\n\n📚 Sources consulted: {len(similar_content)} relevant documents"
                for content in similar_content:
                    answer += f"\n   - {content['url']} (relevance: {content['score']:.3f})"
            
            return answer
            
        except Exception as e:
            return f"❌ Error answering question: {e}"
    
    def search_all_documents(self, query: str) -> str:
        """Search across all stored documents."""
        try:
            results = self.vector_store.search_similar_content(query, top_k=10)
            
            if not results:
                return "No relevant documents found."
            
            response_parts = [f"🔍 Found {len(results)} relevant results for: '{query}'", ""]
            
            for i, result in enumerate(results, 1):
                response_parts.extend([
                    f"📄 Result {i} (Relevance: {result['score']:.3f})",
                    f"   URL: {result['url']}",
                    f"   Type: {result['file_type']}",
                    f"   Content: {result['chunk_text'][:150]}...",
                    ""
                ])
            
            return "\n".join(response_parts)
            
        except Exception as e:
            return f"❌ Search error: {e}"
    
    def get_system_stats(self) -> str:
        """Get system statistics."""
        stats = self.vector_store.get_stats()
        return f"""
📊 RAG System Statistics:
   📚 Total Documents: {stats['total_documents']}
   🔢 Total Vectors: {stats['total_vectors']}
   📁 Storage Path: {stats['storage_path']}
   📏 Vector Dimension: {stats['dimension']}
"""
    
    def cleanup(self):
        """Clean up temporary files."""
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass


def main():
    """Main function with RAG capabilities."""
    parser = argparse.ArgumentParser(
        description="Enhanced Universal Document QnA System with RAG",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("url", nargs='?', help="Document URL (S3, HTTP, or local file path)")
    parser.add_argument("question", nargs='?', help="Question to ask (optional)")
    parser.add_argument("--search", help="Search across all stored documents")
    parser.add_argument("--stats", action="store_true", help="Show system statistics")
    
    args = parser.parse_args()
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        return
    
    # Initialize enhanced system
    qa_system = EnhancedUniversalQA(api_key)
    
    try:
        # Handle different modes
        if args.stats:
            print(qa_system.get_system_stats())
            return
        
        if args.search:
            print(qa_system.search_all_documents(args.search))
            return
        
        if not args.url:
            print("❌ Please provide a document URL")
            return
        
        # Process document with RAG
        if not qa_system.process_document_with_rag(args.url):
            return
        
        # Handle question or interactive mode
        if args.question:
            print(f"\n❓ Question: {args.question}")
            print("\n💭 Answer:")
            answer = qa_system.ask_question_with_rag(args.question)
            print(answer)
        else:
            # Interactive mode
            print("\n🤖 Interactive RAG-Enhanced Q&A Mode")
            print("💡 Ask questions about documents. Type 'quit' to exit.")
            print("💡 Type 'search: query' to search all documents.")
            print("💡 Type 'stats' to see system statistics.")
            print("=" * 60)
            
            while True:
                try:
                    user_input = input("\n❓ Your input: ").strip()
                    
                    if user_input.lower() in ['quit', 'exit', 'q']:
                        print("👋 Goodbye!")
                        break
                    
                    if user_input.lower() == 'stats':
                        print(qa_system.get_system_stats())
                        continue
                    
                    if user_input.lower().startswith('search:'):
                        query = user_input[7:].strip()
                        print(qa_system.search_all_documents(query))
                        continue
                    
                    if not user_input:
                        continue
                    
                    print("\n💭 Answer:")
                    answer = qa_system.ask_question_with_rag(user_input)
                    print(answer)
                    print("\n" + "-" * 60)
                    
                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    break
    
    finally:
        qa_system.cleanup()


if __name__ == "__main__":
    main()
