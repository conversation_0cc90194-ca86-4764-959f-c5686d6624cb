Collecting scikit-learn
  Downloading scikit_learn-1.7.0-cp311-cp311-macosx_12_0_arm64.whl.metadata (31 kB)
Requirement already satisfied: numpy>=1.22.0 in ./venv311/lib/python3.11/site-packages (from scikit-learn) (2.2.6)
Requirement already satisfied: scipy>=1.8.0 in ./venv311/lib/python3.11/site-packages (from scikit-learn) (1.16.0)
Requirement already satisfied: joblib>=1.2.0 in ./venv311/lib/python3.11/site-packages (from scikit-learn) (1.5.1)
Collecting threadpoolctl>=3.1.0 (from scikit-learn)
  Using cached threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)
Downloading scikit_learn-1.7.0-cp311-cp311-macosx_12_0_arm64.whl (10.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.7/10.7 MB 5.4 MB/s eta 0:00:00
Using cached threadpoolctl-3.6.0-py3-none-any.whl (18 kB)
Installing collected packages: threadpoolctl, scikit-learn

Successfully installed scikit-learn-1.7.0 threadpoolctl-3.6.0
