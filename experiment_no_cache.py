#!/usr/bin/env python3
"""
Experiment with different parameters without cache interference
"""

import asyncio
import sys
import json
from typing import Dict, List, Any

# Add the project root to the path
sys.path.append('.')

async def experiment_without_cache():
    """Test different parameter combinations bypassing cache"""
    try:
        from docqa.central_api import ask_question
        
        # Test questions that we want to get better answers for
        test_questions = [
            "What training does the business provide?",
            "What are the franchise fees?", 
            "What is the franchisee approval process?",
            "Describe the business model",
            "What support is provided to franchisees?"
        ]
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Parameter combinations to test
        parameter_sets = [
            {
                "name": "Default Settings",
                "top_k": 5,
                "similarity_threshold": 0.1,
                "temperature": 0.7,
                "max_tokens": 500
            },
            {
                "name": "High Retrieval + Creative",
                "top_k": 15,
                "similarity_threshold": 0.05,
                "temperature": 1.0,
                "max_tokens": 800
            },
            {
                "name": "Maximum Retrieval + Creative",
                "top_k": 30,
                "similarity_threshold": 0.0,
                "temperature": 1.2,
                "max_tokens": 1000
            },
            {
                "name": "Low Threshold + High Creativity",
                "top_k": 20,
                "similarity_threshold": 0.01,
                "temperature": 1.5,
                "max_tokens": 1200
            }
        ]
        
        results = {}
        
        print("🧪 Starting parameter experimentation without cache...")
        print(f"📝 Testing {len(test_questions)} questions with {len(parameter_sets)} parameter sets")
        print("=" * 80)
        
        for param_set in parameter_sets:
            print(f"\n🔬 Testing: {param_set['name']}")
            print(f"   Parameters: top_k={param_set['top_k']}, threshold={param_set['similarity_threshold']}, temp={param_set['temperature']}")
            
            param_results = []
            
            for i, question in enumerate(test_questions, 1):
                print(f"   {i:2d}. {question[:50]}{'...' if len(question) > 50 else ''}")
                
                request = {
                    "question": question,
                    "top_k": param_set["top_k"],
                    "similarity_threshold": param_set["similarity_threshold"],
                    "franchisor_id": franchisor_id,
                    "temperature": param_set["temperature"],
                    "max_tokens": param_set["max_tokens"],
                    "force_refresh": True  # Bypass cache
                }
                
                try:
                    result = await ask_question(request)
                    
                    if result.get('success'):
                        answer = result.get('answer', 'No answer provided')
                        sources = result.get('sources', [])
                        
                        # Analyze answer quality
                        answer_quality = analyze_answer_quality(answer, question)
                        
                        param_results.append({
                            'question': question,
                            'answer': answer,
                            'sources_count': len(sources),
                            'answer_length': len(answer),
                            'quality_score': answer_quality['score'],
                            'has_specific_info': answer_quality['has_specific_info'],
                            'confidence_level': answer_quality['confidence_level']
                        })
                        
                        # Show brief result
                        quality_indicator = "✅" if answer_quality['has_specific_info'] else "❌"
                        print(f"       {quality_indicator} Quality: {answer_quality['score']:.1f}/10, Length: {len(answer)}, Sources: {len(sources)}")
                        
                        # Show answer preview
                        print(f"       Answer: {answer[:100]}{'...' if len(answer) > 100 else ''}")
                    else:
                        print(f"       💥 Error: {result.get('error', 'Unknown error')}")
                        param_results.append({
                            'question': question,
                            'answer': None,
                            'error': result.get('error', 'Unknown error')
                        })
                        
                except Exception as e:
                    print(f"       💥 Exception: {str(e)}")
                    param_results.append({
                        'question': question,
                        'answer': None,
                        'error': str(e)
                    })
            
            # Calculate overall performance for this parameter set
            successful_results = [r for r in param_results if r.get('answer')]
            if successful_results:
                avg_quality = sum(r['quality_score'] for r in successful_results) / len(successful_results)
                specific_answers = sum(1 for r in successful_results if r['has_specific_info'])
                avg_sources = sum(r['sources_count'] for r in successful_results) / len(successful_results)
                avg_length = sum(r['answer_length'] for r in successful_results) / len(successful_results)
                
                print(f"   📊 Results: Avg Quality: {avg_quality:.1f}/10, Specific Answers: {specific_answers}/{len(successful_results)}, Avg Sources: {avg_sources:.1f}, Avg Length: {avg_length:.0f}")
            else:
                print(f"   📊 Results: No successful answers")
            
            results[param_set['name']] = param_results
        
        # Generate comprehensive report
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE RESULTS ANALYSIS")
        print("=" * 80)
        
        await generate_detailed_report(results, test_questions)
        
        return results
        
    except Exception as e:
        print(f"💥 Error during experimentation: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


def analyze_answer_quality(answer: str, question: str) -> Dict[str, Any]:
    """Analyze the quality of an answer"""
    if not answer:
        return {'score': 0, 'has_specific_info': False, 'confidence_level': 'none'}
    
    answer_lower = answer.lower()
    
    # Check for non-informative responses
    non_informative_phrases = [
        'does not include', 'does not contain', 'unable to provide',
        'not available', 'cannot provide', 'no information',
        'more information would be required', 'additional information',
        'not specified', 'not mentioned', 'not provided'
    ]
    
    has_non_informative = any(phrase in answer_lower for phrase in non_informative_phrases)
    
    # Check for specific information
    specific_indicators = [
        'is', 'are', 'includes', 'provides', 'offers', 'requires',
        'costs', 'fees', 'training', 'support', 'process', 'steps',
        'equipment', 'investment', 'territory', 'location', 'contact'
    ]
    
    specific_info_count = sum(1 for indicator in specific_indicators if indicator in answer_lower)
    
    # Calculate quality score
    score = 0
    
    # Length bonus (longer answers often have more detail)
    if len(answer) > 100:
        score += 2
    if len(answer) > 200:
        score += 1
    if len(answer) > 400:
        score += 1
    
    # Specific information bonus
    score += min(specific_info_count * 0.5, 3)
    
    # Penalty for non-informative responses
    if has_non_informative:
        score -= 3
    
    # Bonus for definitive statements
    if any(phrase in answer_lower for phrase in ['the company', 'the business', 'coochie hydrogreen']):
        score += 1
    
    # Bonus for creative/detailed responses
    if len(answer) > 300 and not has_non_informative:
        score += 2
    
    # Ensure score is between 0 and 10
    score = max(0, min(10, score))
    
    # Determine confidence level
    if score >= 7:
        confidence = 'high'
    elif score >= 4:
        confidence = 'medium'
    else:
        confidence = 'low'
    
    return {
        'score': score,
        'has_specific_info': not has_non_informative and specific_info_count > 0,
        'confidence_level': confidence,
        'specific_info_count': specific_info_count
    }


async def generate_detailed_report(results: Dict[str, List[Dict]], questions: List[str]):
    """Generate a detailed analysis report"""
    
    print("\n🏆 BEST PERFORMING PARAMETER SETS:")
    print("-" * 50)
    
    # Calculate performance metrics for each parameter set
    performance_summary = {}
    
    for param_name, param_results in results.items():
        successful_results = [r for r in param_results if r.get('answer')]
        
        if successful_results:
            avg_quality = sum(r['quality_score'] for r in successful_results) / len(successful_results)
            specific_answers = sum(1 for r in successful_results if r['has_specific_info'])
            total_sources = sum(r['sources_count'] for r in successful_results)
            avg_length = sum(r['answer_length'] for r in successful_results) / len(successful_results)
            
            performance_summary[param_name] = {
                'avg_quality': avg_quality,
                'specific_answers': specific_answers,
                'total_questions': len(successful_results),
                'success_rate': len(successful_results) / len(param_results),
                'total_sources': total_sources,
                'avg_length': avg_length
            }
    
    # Sort by average quality score
    sorted_performance = sorted(performance_summary.items(), 
                              key=lambda x: x[1]['avg_quality'], 
                              reverse=True)
    
    for i, (param_name, metrics) in enumerate(sorted_performance, 1):
        print(f"{i}. {param_name}")
        print(f"   Quality Score: {metrics['avg_quality']:.1f}/10")
        print(f"   Specific Answers: {metrics['specific_answers']}/{metrics['total_questions']}")
        print(f"   Success Rate: {metrics['success_rate']:.1%}")
        print(f"   Total Sources: {metrics['total_sources']}")
        print(f"   Avg Answer Length: {metrics['avg_length']:.0f} chars")
        print()
    
    print("\n📋 BEST ANSWERS BY QUESTION:")
    print("-" * 50)
    
    # Find best answer for each question
    for question in questions:
        best_answer = None
        best_score = 0
        best_param = None
        
        for param_name, param_results in results.items():
            for result in param_results:
                if (result['question'] == question and 
                    result.get('answer') and 
                    result['quality_score'] > best_score):
                    best_answer = result['answer']
                    best_score = result['quality_score']
                    best_param = param_name
        
        print(f"\nQ: {question}")
        if best_answer:
            print(f"Best Answer (Score: {best_score:.1f}, Params: {best_param}):")
            print(f"A: {best_answer}")
        else:
            print("A: No good answers found")
        print("-" * 30)


if __name__ == "__main__":
    asyncio.run(experiment_without_cache())
