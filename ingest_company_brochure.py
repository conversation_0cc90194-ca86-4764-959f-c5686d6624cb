#!/usr/bin/env python3
"""
Company Brochure Ingestion System
Specialized for processing and storing company brochure content
"""

import asyncio
import sys
import json
import os
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Add the project root to the path
sys.path.append('.')

# Import brochure-specific components
from brochure_rag_system import (
    BrochureTextNormalizer,
    BrochureMetadataExtractor,
    BrochureChunker,
    BrochureSection,
    BrochureMetadata
)

# Import production components
from production_rag_system import (
    ProductionEmbeddingService,
    ProductionVectorStore,
    DocumentChunk
)

class CompanyBrochureProcessor:
    """Comprehensive processor for company brochures"""
    
    def __init__(self):
        self.normalizer = BrochureTextNormalizer()
        self.metadata_extractor = BrochureMetadataExtractor()
        self.chunker = BrochureChunker(chunk_size=350, chunk_overlap=50)
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        
        print("✅ Company Brochure Processor initialized")
    
    async def process_brochure_pdf(self, file_path: str) -> str:
        """Process brochure PDF with enhanced text extraction"""
        try:
            print(f"📄 Processing brochure PDF: {file_path}")
            
            # Use PyPDF2 for text extraction
            from PyPDF2 import PdfReader
            
            reader = PdfReader(file_path)
            text_content = ""
            
            # Extract text from each page with page markers
            for i, page in enumerate(reader.pages):
                page_text = page.extract_text()
                if page_text:
                    # Add page marker for better structure
                    text_content += f"\n\n=== PAGE {i+1} ===\n\n{page_text}"
            
            print(f"✅ Extracted {len(text_content)} characters from brochure PDF")
            
            # Normalize brochure text
            normalized_text = self.normalizer.normalize_brochure_text(text_content)
            print(f"✅ Normalized brochure text: {len(normalized_text)} characters")
            
            return normalized_text
            
        except Exception as e:
            print(f"❌ Error processing brochure PDF: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    async def extract_brochure_structure(self, text: str) -> Tuple[List[BrochureSection], BrochureMetadata]:
        """Extract structured information from brochure"""
        try:
            print(f"🔍 Extracting brochure structure from {len(text)} characters")
            
            # Extract sections
            sections = self.normalizer.extract_sections(text)
            print(f"✅ Extracted {len(sections)} brochure sections:")
            for section in sections:
                print(f"   - {section.section_type}: {section.title}")
            
            # Extract metadata
            metadata = self.metadata_extractor.extract_metadata(text, sections)
            print(f"✅ Extracted brochure metadata:")
            print(f"   - Company: {metadata.company_name}")
            print(f"   - Industry: {metadata.industry}")
            print(f"   - Location: {metadata.location}")
            print(f"   - Services: {len(metadata.services or [])} identified")
            print(f"   - Contact Info: {len(metadata.contact_info or {})} items")
            
            return sections, metadata
            
        except Exception as e:
            print(f"❌ Error extracting brochure structure: {e}")
            import traceback
            traceback.print_exc()
            return [], BrochureMetadata()
    
    async def create_brochure_chunks(
        self, 
        text: str, 
        sections: List[BrochureSection], 
        metadata: BrochureMetadata
    ) -> List[DocumentChunk]:
        """Create optimized chunks for brochure content"""
        try:
            print(f"📝 Creating brochure chunks")
            
            # Create chunks using brochure-specific chunker
            chunks = self.chunker.chunk_brochure(text, sections, metadata)
            
            print(f"✅ Created {len(chunks)} brochure chunks:")
            
            # Show chunk distribution by section
            section_counts = {}
            for chunk in chunks:
                section_type = chunk.metadata.get('section_type', 'unknown')
                section_counts[section_type] = section_counts.get(section_type, 0) + 1
            
            for section_type, count in section_counts.items():
                print(f"   - {section_type}: {count} chunks")
            
            return chunks
            
        except Exception as e:
            print(f"❌ Error creating brochure chunks: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def generate_brochure_embeddings(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Generate embeddings optimized for brochure content"""
        try:
            print(f"🧠 Generating embeddings for {len(chunks)} brochure chunks")
            
            valid_chunks = []
            batch_size = 5
            
            for i in range(0, len(chunks), batch_size):
                batch_chunks = chunks[i:i + batch_size]
                print(f"  Processing batch {i//batch_size + 1}/{(len(chunks) + batch_size - 1)//batch_size}")
                
                for j, chunk in enumerate(batch_chunks):
                    try:
                        # Generate embedding for brochure content
                        embedding = self.embedding_service.generate_embedding(chunk.text)
                        chunk.embedding = embedding
                        valid_chunks.append(chunk)
                        
                        print(f"    ✅ Generated embedding {i+j+1}/{len(chunks)}: {len(embedding)} dimensions")
                        print(f"       Section: {chunk.metadata.get('section_type', 'unknown')}")
                        
                    except Exception as e:
                        print(f"    ❌ Failed to generate embedding for chunk {i+j+1}: {e}")
                        continue
                
                # Add delay to avoid rate limiting
                if i + batch_size < len(chunks):
                    time.sleep(0.5)
            
            print(f"✅ Generated {len(valid_chunks)}/{len(chunks)} valid brochure embeddings")
            return valid_chunks
            
        except Exception as e:
            print(f"❌ Error generating brochure embeddings: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def store_brochure_data(
        self, 
        franchisor_id: str, 
        text: str, 
        chunks: List[DocumentChunk],
        metadata: BrochureMetadata
    ) -> bool:
        """Store brochure data with enhanced metadata"""
        try:
            print(f"💾 Storing brochure data for franchisor: {franchisor_id}")
            
            # Clear existing embeddings
            self.vector_store.clear_franchisor_embeddings(franchisor_id)
            
            # Create comprehensive brochure text for main embedding
            brochure_summary = self._create_brochure_summary(text, metadata)
            
            # Generate embedding for the comprehensive brochure content
            main_embedding = self.embedding_service.generate_embedding(brochure_summary)
            
            # Enhanced metadata for storage
            storage_metadata = {
                'content_type': 'company_brochure',
                'company_name': metadata.company_name,
                'industry': metadata.industry,
                'location': metadata.location,
                'services_count': len(metadata.services or []),
                'contact_info': metadata.contact_info,
                'chunk_count': len(chunks),
                'text_length': len(text),
                'processing_time': time.time(),
                'sections_identified': len(set(chunk.metadata.get('section_type') for chunk in chunks))
            }
            
            # Store main embedding
            result = self.vector_store.store_franchisor_embedding(
                franchisor_id=franchisor_id,
                text_content=brochure_summary,
                embedding=main_embedding,
                metadata=storage_metadata
            )
            
            print(f"✅ Stored brochure embedding: {result}")
            return result
            
        except Exception as e:
            print(f"❌ Error storing brochure data: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _create_brochure_summary(self, text: str, metadata: BrochureMetadata) -> str:
        """Create comprehensive summary for main embedding"""
        summary_parts = []
        
        # Company information
        if metadata.company_name:
            summary_parts.append(f"Company: {metadata.company_name}")
        
        if metadata.industry:
            summary_parts.append(f"Industry: {metadata.industry}")
        
        if metadata.location:
            summary_parts.append(f"Location: {metadata.location}")
        
        # Services
        if metadata.services:
            services_text = "Services: " + ", ".join(metadata.services[:5])  # Top 5 services
            summary_parts.append(services_text)
        
        # Contact information
        if metadata.contact_info:
            contact_parts = []
            if metadata.phone:
                contact_parts.append(f"Phone: {metadata.phone}")
            if metadata.email:
                contact_parts.append(f"Email: {metadata.email}")
            if metadata.website:
                contact_parts.append(f"Website: {metadata.website}")
            
            if contact_parts:
                summary_parts.append("Contact: " + ", ".join(contact_parts))
        
        # Add first part of original text for context
        text_preview = text[:500] + "..." if len(text) > 500 else text
        summary_parts.append(f"Content: {text_preview}")
        
        return "\n".join(summary_parts)
    
    async def test_brochure_retrieval(self, franchisor_id: str, metadata: BrochureMetadata):
        """Test retrieval with brochure-specific questions"""
        try:
            print(f"🔍 Testing brochure retrieval for: {franchisor_id}")
            
            # Brochure-specific test questions
            test_questions = [
                f"What is {metadata.company_name or 'this company'}?",
                "What services does the company provide?",
                "Where is the company located?",
                "How can I contact the company?",
                "What makes this company special?",
                "Is this a franchise opportunity?",
                "What are the company's contact details?",
                "What industry is the company in?",
                "What areas does the company serve?",
                "What experience does the company have?"
            ]
            
            for question in test_questions:
                print(f"\n❓ Brochure Question: {question}")
                
                # Generate query embedding
                query_embedding = self.embedding_service.generate_embedding(question)
                
                # Search with brochure-optimized thresholds
                thresholds = [0.5, 0.4, 0.3, 0.2, 0.1]
                
                for threshold in thresholds:
                    results = self.vector_store.search_similar(
                        query_embedding=query_embedding,
                        top_k=3,
                        similarity_threshold=threshold,
                        franchisor_id=franchisor_id
                    )
                    
                    print(f"  Threshold {threshold}: Found {len(results)} results")
                    
                    for i, result in enumerate(results):
                        print(f"    {i+1}. Score: {result.similarity_score:.4f} - {result.text[:50]}...")
                    
                    if results:
                        break
            
            print("\n✅ Brochure retrieval testing completed")
            
        except Exception as e:
            print(f"❌ Error testing brochure retrieval: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """Main function to process company brochure"""
    print("🚀 Company Brochure Ingestion System")
    print("=" * 60)
    
    # Initialize processor
    processor = CompanyBrochureProcessor()
    
    # Franchisor ID for Coochie Hydrogreen
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    # Brochure PDF path
    pdf_path = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
    
    # Process brochure
    text_content = await processor.process_brochure_pdf(pdf_path)
    if not text_content:
        print("❌ Failed to extract text from brochure PDF")
        return
    
    # Extract structure and metadata
    sections, metadata = await processor.extract_brochure_structure(text_content)
    if not sections and not metadata.company_name:
        print("❌ Failed to extract brochure structure")
        return
    
    # Create chunks
    chunks = await processor.create_brochure_chunks(text_content, sections, metadata)
    if not chunks:
        print("❌ Failed to create brochure chunks")
        return
    
    # Generate embeddings
    chunks_with_embeddings = await processor.generate_brochure_embeddings(chunks)
    if not chunks_with_embeddings:
        print("❌ Failed to generate brochure embeddings")
        return
    
    # Store brochure data
    result = await processor.store_brochure_data(
        franchisor_id=franchisor_id,
        text=text_content,
        chunks=chunks_with_embeddings,
        metadata=metadata
    )
    if not result:
        print("❌ Failed to store brochure data")
        return
    
    # Test retrieval
    await processor.test_brochure_retrieval(franchisor_id, metadata)
    
    print("\n🎉 Company brochure ingestion completed successfully!")
    print(f"📊 Summary:")
    print(f"   - Company: {metadata.company_name}")
    print(f"   - Sections: {len(sections)}")
    print(f"   - Chunks: {len(chunks_with_embeddings)}")
    print(f"   - Services: {len(metadata.services or [])}")

if __name__ == "__main__":
    asyncio.run(main())
