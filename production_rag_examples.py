#!/usr/bin/env python3
"""
Production-Grade RAG System Usage Examples
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.append('.')

async def example_document_processing():
    """Example of document processing with production-grade RAG"""
    from docqa.production_integration import production_rag
    
    print("🚀 Example: Document Processing")
    print("=" * 50)
    
    # Sample document
    document_path = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    # Read document
    with open(document_path, 'rb') as f:
        pdf_content = f.read()
    
    # Process PDF
    from PyPDF2 import PdfReader
    from io import BytesIO
    
    reader = PdfReader(BytesIO(pdf_content))
    text_content = ""
    
    # Extract text from each page
    for i, page in enumerate(reader.pages):
        page_text = page.extract_text()
        if page_text:
            text_content += f"\n\n--- Page {i+1} ---\n\n{page_text}"
    
    print(f"✅ Extracted {len(text_content)} characters from PDF")
    
    # Process document
    chunks = production_rag.process_document(
        text_content=text_content,
        metadata={
            'source': Path(document_path).name,
            'franchisor_id': franchisor_id
        }
    )
    
    print(f"✅ Created {len(chunks)} chunks with embeddings")
    
    # Store document
    result = production_rag.store_document(
        franchisor_id=franchisor_id,
        text_content=text_content,
        chunks=chunks
    )
    
    print(f"✅ Document stored: {result}")
    
    return chunks

async def example_question_answering():
    """Example of question answering with production-grade RAG"""
    from docqa.production_integration import production_rag
    
    print("\n🚀 Example: Question Answering")
    print("=" * 50)
    
    # Sample questions
    questions = [
        "What is Coochie Hydrogreen?",
        "Where is Coochie Hydrogreen located?",
        "What services does Coochie Hydrogreen provide?",
        "What are the franchise fees?"
    ]
    
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. Question: {question}")
        
        # Answer question
        result = await production_rag.answer_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=0.5,
            top_k=5,
            temperature=0.1
        )
        
        if result['success']:
            print(f"   Answer: {result['answer']}")
            
            if result['sources']:
                print(f"   Top Source: {result['sources'][0]['text'][:100]}...")
                print(f"   Similarity: {result['sources'][0]['similarity_score']:.4f}")
        else:
            print(f"   Error: {result['error']}")

async def example_direct_api_usage():
    """Example of direct API usage with production-grade components"""
    print("\n🚀 Example: Direct API Usage")
    print("=" * 50)
    
    # Import individual components
    from docqa.text_processing.production_text_processor import ProductionTextNormalizer, ProductionChunker
    from docqa.vector_store.production_embeddings import ProductionEmbeddingService
    from docqa.vector_store.production_vector_store import ProductionVectorStore
    
    # Initialize components
    normalizer = ProductionTextNormalizer()
    chunker = ProductionChunker(chunk_size=400, chunk_overlap=75)
    embedding_service = ProductionEmbeddingService()
    vector_store = ProductionVectorStore()
    
    # Sample text
    text = """
    Coochie Hydrogreen is a franchisor based in Australia.
    The company specializes in lawn care and maintenance services.
    """
    
    # Normalize text
    normalized_text = normalizer.normalize_text(text)
    print(f"✅ Normalized text: {normalized_text[:50]}...")
    
    # Create chunks
    chunks = chunker.chunk_text(normalized_text)
    print(f"✅ Created {len(chunks)} chunks")
    
    # Generate embeddings
    for chunk in chunks:
        chunk.embedding = embedding_service.generate_embedding(chunk.text)
    print(f"✅ Generated embeddings")
    
    # Generate query embedding
    query = "What is Coochie Hydrogreen?"
    query_embedding = embedding_service.generate_embedding(query)
    print(f"✅ Generated query embedding")
    
    # Search for similar content
    results = vector_store.search_similar(
        query_embedding=query_embedding,
        top_k=3,
        similarity_threshold=0.5
    )
    
    print(f"✅ Found {len(results)} similar results")
    for i, result in enumerate(results, 1):
        print(f"   {i}. Score: {result.similarity_score:.4f} - {result.text[:50]}...")

async def main():
    """Run all examples"""
    print("🚀 Production-Grade RAG System Examples")
    print("=" * 60)
    
    # Run examples
    await example_document_processing()
    await example_question_answering()
    await example_direct_api_usage()
    
    print("\n✅ All examples completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
