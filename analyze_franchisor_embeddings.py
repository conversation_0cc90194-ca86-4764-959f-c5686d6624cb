#!/usr/bin/env python3
"""
Analyze embeddings in franchisor table for Coochie Hydrogreen
and find optimal parameters for question answering
"""

import asyncio
import sys
import json
import psycopg2
from typing import Dict, List, Any, Optional
import numpy as np

# Add the project root to the path
sys.path.append('.')

async def get_franchisor_embedding_data():
    """Get the actual embedding data from the franchisor table"""
    try:
        from docqa.config import get_config
        
        config = get_config()

        # Convert asyncpg URL to psycopg2 format
        db_url = config.database_url.replace("postgresql+asyncpg://", "postgresql://")

        # Connect to database
        conn = psycopg2.connect(db_url)
        cur = conn.cursor()
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Get franchisor data with embedding
        cur.execute("""
            SELECT 
                id,
                name,
                region,
                industry_id,
                brochure_url,
                embedding,
                created_at,
                updated_at
            FROM franchisors 
            WHERE id = %s AND embedding IS NOT NULL
        """, (franchisor_id,))
        
        result = cur.fetchone()
        
        if result:
            franchisor_data = {
                'id': result[0],
                'name': result[1],
                'region': result[2],
                'industry_id': result[3],
                'brochure_url': result[4],
                'embedding': result[5],  # This is the vector
                'created_at': result[6],
                'updated_at': result[7]
            }
            
            print(f"✅ Found franchisor: {franchisor_data['name']}")
            print(f"   Region: {franchisor_data['region']}")
            print(f"   Industry ID: {franchisor_data['industry_id']}")
            print(f"   Brochure URL: {franchisor_data['brochure_url']}")
            print(f"   Embedding dimension: {len(franchisor_data['embedding'])}")
            print(f"   Created: {franchisor_data['created_at']}")
            print(f"   Updated: {franchisor_data['updated_at']}")
            
            return franchisor_data
        else:
            print("❌ No franchisor found with embedding")
            return None
            
    except Exception as e:
        print(f"💥 Error getting franchisor data: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()


async def test_similarity_with_questions(franchisor_data: Dict):
    """Test similarity scores with different questions"""
    try:
        from docqa.vector_store import EmbeddingService
        
        embedding_service = EmbeddingService()
        franchisor_embedding = franchisor_data['embedding']
        
        # Test questions
        test_questions = [
            "What training does the business provide?",
            "Describe business history.",
            "What is FRANCHISEE APPROVAL PROCESS?",
            "What are the franchisee fees?",
            "What are the franchisee marketing fees?",
            "What are the specialised Coochie Hydrogreen franchise?",
            "What is Coochie Hydrogreen?",
            "Where is Coochie Hydrogreen located?",
            "What services does Coochie Hydrogreen provide?",
            "What type of business is Coochie Hydrogreen?",
            "What industry is Coochie Hydrogreen in?",
            "Tell me about the company",
            "What does the business do?",
            "What is the business model?",
            "What support is provided?",
            "What equipment is needed?",
            "What territories are available?",
            "How much investment is required?",
            "What are the ongoing costs?",
            "What is the franchise opportunity?"
        ]
        
        print("\n🔍 Testing similarity scores for different questions:")
        print("=" * 80)
        
        similarity_results = []
        
        for i, question in enumerate(test_questions, 1):
            # Generate embedding for question
            question_embedding = embedding_service.generate_embedding(question)
            
            # Calculate cosine similarity
            similarity = calculate_cosine_similarity(question_embedding, franchisor_embedding)
            
            similarity_results.append({
                'question': question,
                'similarity': similarity,
                'rank': i
            })
            
            print(f"{i:2d}. Similarity: {similarity:.4f} - {question}")
        
        # Sort by similarity score
        similarity_results.sort(key=lambda x: x['similarity'], reverse=True)
        
        print("\n📊 Questions ranked by similarity score:")
        print("-" * 60)
        
        for i, result in enumerate(similarity_results, 1):
            print(f"{i:2d}. Score: {result['similarity']:.4f} - {result['question']}")
        
        return similarity_results
        
    except Exception as e:
        print(f"💥 Error testing similarity: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


def calculate_cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """Calculate cosine similarity between two vectors"""
    try:
        # Convert to numpy arrays
        a = np.array(vec1)
        b = np.array(vec2)
        
        # Calculate cosine similarity
        dot_product = np.dot(a, b)
        norm_a = np.linalg.norm(a)
        norm_b = np.linalg.norm(b)
        
        if norm_a == 0 or norm_b == 0:
            return 0.0
        
        similarity = dot_product / (norm_a * norm_b)
        return float(similarity)
        
    except Exception as e:
        print(f"Error calculating similarity: {str(e)}")
        return 0.0


async def test_parameter_combinations(similarity_results: List[Dict]):
    """Test different parameter combinations to find optimal settings"""
    try:
        from docqa.central_api import ask_question
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Get top 5 questions with highest similarity
        top_questions = similarity_results[:5]
        
        # Parameter combinations to test
        parameter_sets = [
            {
                "name": "Very Low Threshold",
                "similarity_threshold": 0.01,
                "top_k": 10,
                "temperature": 0.7
            },
            {
                "name": "Ultra Low Threshold",
                "similarity_threshold": 0.001,
                "top_k": 15,
                "temperature": 0.7
            },
            {
                "name": "Zero Threshold",
                "similarity_threshold": 0.0,
                "top_k": 20,
                "temperature": 0.7
            },
            {
                "name": "Zero Threshold + High Creativity",
                "similarity_threshold": 0.0,
                "top_k": 25,
                "temperature": 1.2
            },
            {
                "name": "Zero Threshold + Max Creativity",
                "similarity_threshold": 0.0,
                "top_k": 30,
                "temperature": 1.5
            }
        ]
        
        print("\n🧪 Testing parameter combinations with top similarity questions:")
        print("=" * 80)
        
        results = {}
        
        for param_set in parameter_sets:
            print(f"\n🔬 Testing: {param_set['name']}")
            print(f"   Threshold: {param_set['similarity_threshold']}, Top-K: {param_set['top_k']}, Temp: {param_set['temperature']}")
            
            param_results = []
            
            for question_data in top_questions:
                question = question_data['question']
                expected_similarity = question_data['similarity']
                
                print(f"   Q: {question[:50]}... (Expected sim: {expected_similarity:.4f})")
                
                request = {
                    "question": question,
                    "franchisor_id": franchisor_id,
                    "similarity_threshold": param_set["similarity_threshold"],
                    "top_k": param_set["top_k"],
                    "temperature": param_set["temperature"],
                    "max_tokens": 800,
                    "force_refresh": True
                }
                
                try:
                    result = await ask_question(request)
                    
                    if result.get('success'):
                        answer = result.get('answer', '')
                        sources = result.get('sources', [])
                        
                        # Analyze answer quality
                        quality_score = analyze_answer_quality(answer)
                        
                        param_results.append({
                            'question': question,
                            'answer': answer,
                            'sources_count': len(sources),
                            'quality_score': quality_score,
                            'expected_similarity': expected_similarity,
                            'answer_length': len(answer)
                        })
                        
                        print(f"      ✅ Quality: {quality_score:.1f}/10, Sources: {len(sources)}, Length: {len(answer)}")
                        print(f"      Answer: {answer[:80]}...")
                    else:
                        print(f"      ❌ Error: {result.get('error', 'Unknown')}")
                        
                except Exception as e:
                    print(f"      💥 Exception: {str(e)}")
            
            # Calculate average performance
            if param_results:
                avg_quality = sum(r['quality_score'] for r in param_results) / len(param_results)
                avg_sources = sum(r['sources_count'] for r in param_results) / len(param_results)
                avg_length = sum(r['answer_length'] for r in param_results) / len(param_results)
                
                print(f"   📊 Average Quality: {avg_quality:.1f}/10, Sources: {avg_sources:.1f}, Length: {avg_length:.0f}")
            
            results[param_set['name']] = param_results
        
        return results
        
    except Exception as e:
        print(f"💥 Error testing parameters: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


def analyze_answer_quality(answer: str) -> float:
    """Analyze answer quality and return score 0-10"""
    if not answer:
        return 0.0
    
    score = 0.0
    answer_lower = answer.lower()
    
    # Length scoring
    if len(answer) > 50:
        score += 1
    if len(answer) > 150:
        score += 1
    if len(answer) > 300:
        score += 1
    
    # Content quality indicators
    positive_indicators = [
        'coochie hydrogreen', 'australia', 'franchise', 'business',
        'provides', 'offers', 'includes', 'training', 'support',
        'fees', 'investment', 'process', 'approval', 'marketing'
    ]
    
    negative_indicators = [
        'does not include', 'does not contain', 'not available',
        'cannot provide', 'unable to provide', 'no information',
        'not specified', 'not mentioned'
    ]
    
    # Positive scoring
    positive_count = sum(1 for indicator in positive_indicators if indicator in answer_lower)
    score += min(positive_count * 0.5, 4)
    
    # Negative penalty
    negative_count = sum(1 for indicator in negative_indicators if indicator in answer_lower)
    score -= negative_count * 2
    
    # Specific information bonus
    if 'coochie hydrogreen' in answer_lower and len(answer) > 100:
        score += 2
    
    return max(0, min(10, score))


async def main():
    """Main analysis function"""
    print("🚀 Starting comprehensive franchisor embedding analysis...")
    
    # Step 1: Get franchisor data
    franchisor_data = await get_franchisor_embedding_data()
    if not franchisor_data:
        return
    
    # Step 2: Test similarity with questions
    similarity_results = await test_similarity_with_questions(franchisor_data)
    if not similarity_results:
        return
    
    # Step 3: Test parameter combinations
    parameter_results = await test_parameter_combinations(similarity_results)
    
    # Step 4: Generate final recommendations
    print("\n" + "=" * 80)
    print("🎯 FINAL ANALYSIS AND RECOMMENDATIONS")
    print("=" * 80)
    
    # Save results
    analysis_results = {
        'franchisor_data': {
            'id': franchisor_data['id'],
            'name': franchisor_data['name'],
            'region': franchisor_data['region'],
            'embedding_dimension': len(franchisor_data['embedding'])
        },
        'similarity_results': similarity_results,
        'parameter_results': parameter_results
    }
    
    with open('franchisor_embedding_analysis.json', 'w') as f:
        json.dump(analysis_results, f, indent=2, default=str)
    
    print("✅ Analysis complete! Results saved to franchisor_embedding_analysis.json")


if __name__ == "__main__":
    asyncio.run(main())
