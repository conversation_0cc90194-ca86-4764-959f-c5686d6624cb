include:
  remote: 'https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/build.yaml'

stages:
  - build
  - deploy

variables:
  PROJECT: "growthhive-ai-api"
  TECHNOLOGY: "python" 

build:
  stage: build
  extends: .build
  variables:
    BUILD_ARGS: "--build-arg APP_NAME=${PROJECT} --build-arg PROFILE=${CI_COMMIT_REF_NAME}"

deploy_dev:
  stage: deploy
  extends: .deploy_devspace
  environment:
    url: https://$PROJECT.$DEV_BASE_DOMAIN
  variables:
    CONT_PORT: "8000"
  only:
    - development
