# Database Configuration

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=1rR+0sSRTdjuNRfiyvENhcjrOQyADlRtLk59D6Za
AWS_REGION=ap-south-1
S3_BUCKET_NAME=openxcell-development-public
S3_BASE_URL=https://openxcell-development-public.s3.ap-south-1.amazonaws.com

# Development Settings
ENVIRONMENT=development
DEBUG=true

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/growthhive.log

# Database URL
DATABASE_URL=postgresql+asyncpg://growthhive:<EMAIL>:5432/growthhive

