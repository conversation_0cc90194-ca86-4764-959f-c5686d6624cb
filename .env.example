# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4-turbo
OPENAI_VISION_MODEL=gpt-4o
EMBEDDING_MODEL=text-embedding-3-small

# AWS Configuration (optional - can use AWS CLI credentials)
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_DEFAULT_REGION=us-east-1

# DocQA Configuration
FAISS_INDEX_PATH=./data/faiss_index
CHUNK_SIZE=400
CHUNK_OVERLAP=50
TOP_K_RETRIEVAL=6
MAX_TOKENS_RESPONSE=1000

# Logging Configuration
LOG_LEVEL=INFO
