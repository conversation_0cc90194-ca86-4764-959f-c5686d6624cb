#!/usr/bin/env python3
"""
Enhanced script to reprocess Coochie Hydrogreen brochure with improved extraction
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append('.')

async def enhanced_reprocess_coochie_brochure():
    """Reprocess the Coochie Hydrogreen brochure with enhanced extraction"""
    try:
        print("🔄 Reprocessing Coochie Hydrogreen brochure with enhanced extraction...")
        
        # Clear existing data first
        print("🧹 Clearing existing embeddings...")
        await clear_existing_embeddings()
        
        # Import the DocQA integration service
        from app.services.docqa_integration_service import DocQAIntegrationService
        
        # Initialize the service
        docqa_service = DocQAIntegrationService()
        
        # Coochie Hydrogreen details
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        brochure_url = "growthhive/brochure/20250715_120336_4cd334261504.pdf"
        
        print(f"📄 Processing brochure with enhanced extraction: {brochure_url}")
        print(f"🏢 Franchisor ID: {franchisor_id}")
        
        # Process the brochure
        result = await docqa_service.process_franchisor_brochure(
            franchisor_id=franchisor_id,
            brochure_url=brochure_url
        )
        
        if result and result.success:
            print(f"✅ Enhanced processing successful!")
            print(f"📊 Chunks created: {result.chunks_created}")
            print(f"📋 Table: {result.table_name}")
            print(f"🆔 Document ID: {result.document_id}")
            
            # Test the enhanced extraction
            print("\n🧪 Testing enhanced extraction...")
            await test_enhanced_extraction()
        else:
            print(f"❌ Processing failed: {result.error_message if result else 'Unknown error'}")
            
    except Exception as e:
        print(f"💥 Error during reprocessing: {str(e)}")
        import traceback
        traceback.print_exc()


async def clear_existing_embeddings():
    """Clear existing embeddings for Coochie Hydrogreen"""
    try:
        from docqa.vector_store import PgVectorStore
        
        vector_store = PgVectorStore()
        
        # Clear franchisor embeddings
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Delete existing embeddings (if method exists)
        try:
            await vector_store.delete_franchisor_embeddings(franchisor_id)
            print(f"✅ Cleared existing embeddings for franchisor {franchisor_id}")
        except AttributeError:
            print(f"⚠️ Warning: delete_franchisor_embeddings method not available")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not clear existing embeddings: {str(e)}")


async def test_enhanced_extraction():
    """Test the enhanced extraction with sample questions"""
    try:
        from docqa.central_api import ask_question

        test_questions = [
            "What training does the business provide?",
            "What are the franchise fees?",
            "What is the franchisee approval process?",
            "Describe the business model",
            "What support is provided to franchisees?",
            "What are the marketing fees?",
            "What is the business history?",
            "What are the specialised Coochie Hydrogreen franchise services?"
        ]

        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"

        print("\n📝 Testing enhanced extraction with sample questions:")

        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}. ❓ Question: {question}")

            request = {
                "question": question,
                "top_k": 8,
                "similarity_threshold": 0.1,  # Very low threshold to find any relevant content
                "franchisor_id": franchisor_id,
                "force_refresh": True
            }

            try:
                result = await ask_question(request)

                if result.get('success'):
                    answer = result.get('answer', 'No answer provided')
                    sources = result.get('sources', [])

                    print(f"   ✅ Answer: {answer[:150]}{'...' if len(answer) > 150 else ''}")
                    print(f"   📚 Sources found: {len(sources)}")

                    if sources:
                        for j, source in enumerate(sources[:2]):  # Show first 2 sources
                            relevance = source.get('similarity_score', 0)
                            content_preview = source.get('text', '')[:100]
                            print(f"      Source {j+1}: Relevance {relevance:.3f} - {content_preview}...")
                    else:
                        print("      ⚠️ No sources found")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"   💥 Error processing question: {str(e)}")

        print("\n🎯 Enhanced extraction testing completed!")

    except Exception as e:
        print(f"💥 Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()


async def analyze_document_content():
    """Analyze the document content to understand what was extracted"""
    try:
        print("\n🔍 Analyzing document content...")
        
        from docqa.vector_store import PgVectorStore
        
        vector_store = PgVectorStore()
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Get all chunks for this franchisor
        chunks = await vector_store.get_franchisor_chunks(franchisor_id)
        
        if chunks:
            print(f"📊 Total chunks found: {len(chunks)}")
            
            # Analyze chunk types and content
            chunk_types = {}
            total_content_length = 0
            
            for chunk in chunks[:10]:  # Analyze first 10 chunks
                content = chunk.get('content', '')
                chunk_type = chunk.get('metadata', {}).get('chunk_type', 'unknown')
                
                if chunk_type not in chunk_types:
                    chunk_types[chunk_type] = 0
                chunk_types[chunk_type] += 1
                
                total_content_length += len(content)
                
                print(f"\n   Chunk {chunk.get('id', 'unknown')[:8]}...")
                print(f"   Type: {chunk_type}")
                print(f"   Length: {len(content)} chars")
                print(f"   Preview: {content[:200]}...")
            
            print(f"\n📈 Chunk type distribution: {chunk_types}")
            print(f"📏 Average content length: {total_content_length / min(len(chunks), 10):.0f} chars")
        else:
            print("❌ No chunks found for this franchisor")
        
    except Exception as e:
        print(f"💥 Error analyzing content: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(enhanced_reprocess_coochie_brochure())
