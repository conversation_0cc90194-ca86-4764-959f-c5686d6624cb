#!/usr/bin/env python3
"""
Demo script for Advanced Franchisor Detection

This script demonstrates the OpenAI-based franchisor detection functionality
with real examples including the "Coochie Hydrogreen" use case.
"""

import asyncio
import os
import sys

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config.settings import Settings
from app.services.franchisor_detection_service import AdvancedFranchisorDetectionService


class MockFranchisorRepository:
    """Mock repository for demo purposes"""
    
    def __init__(self):
        self.mock_franchisors = [
            {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Coochie Hydrogreen",
                "description": "Eco-friendly car wash franchise specializing in waterless cleaning technology and environmental sustainability",
                "category": "Automotive Services",
                "region": "Australia",
                "is_active": True
            },
            {
                "id": "456e7890-e89b-12d3-a456-426614174001", 
                "name": "Pizza Palace",
                "description": "Italian pizza restaurant franchise with authentic wood-fired recipes and traditional ingredients",
                "category": "Food & Beverage",
                "region": "USA",
                "is_active": True
            },
            {
                "id": "789e0123-e89b-12d3-a456-426614174002",
                "name": "FitLife Gym",
                "description": "Modern fitness center franchise with state-of-the-art equipment and personal training services",
                "category": "Health & Fitness",
                "region": "Canada",
                "is_active": True
            },
            {
                "id": "012e3456-e89b-12d3-a456-426614174003",
                "name": "TechRepair Pro",
                "description": "Electronics repair franchise specializing in smartphones, tablets, and computer repairs",
                "category": "Technology Services",
                "region": "UK",
                "is_active": True
            }
        ]
    
    async def get_multi_with_filters(self, is_active=None, limit=100, **kwargs):
        """Mock method to return franchisors"""
        franchisors = []
        for data in self.mock_franchisors:
            if is_active is None or data["is_active"] == is_active:
                # Create a mock franchisor object
                franchisor = type('MockFranchisor', (), data)()
                franchisors.append(franchisor)
        
        return franchisors[:limit], len(franchisors)


class MockDetectionService(AdvancedFranchisorDetectionService):
    """Mock detection service for demo"""
    
    def __init__(self):
        # Initialize without database session
        self.settings = Settings()
        
        # Check if OpenAI API key is available
        if not self.settings.OPENAI_API_KEY:
            print("⚠️  OpenAI API key not found in environment variables.")
            print("   Set OPENAI_API_KEY environment variable to test with real OpenAI API.")
            print("   Continuing with mock responses...\n")
            self.use_real_openai = False
        else:
            print("✅ OpenAI API key found. Using real OpenAI API for detection.\n")
            self.use_real_openai = True
            import openai
            self.openai_client = openai.OpenAI(api_key=self.settings.OPENAI_API_KEY)
        
        # Configuration
        self.embedding_model = self.settings.FRANCHISOR_DETECTION_EMBEDDING_MODEL
        self.detection_model = self.settings.FRANCHISOR_DETECTION_MODEL
        self.confidence_threshold = self.settings.FRANCHISOR_DETECTION_CONFIDENCE_THRESHOLD
        self.semantic_similarity_threshold = self.settings.FRANCHISOR_DETECTION_SEMANTIC_THRESHOLD
        
        # Mock repository
        self.franchisor_repository = MockFranchisorRepository()
        
        # Cache
        self._franchisor_embeddings_cache = {}
        self._franchisors_cache = None


async def demo_franchisor_detection():
    """Run the franchisor detection demo"""
    
    print("🚀 Advanced Franchisor Detection Demo")
    print("=" * 50)
    
    # Initialize the mock service
    service = MockDetectionService()
    
    # Test questions
    test_questions = [
        {
            "question": "I want to know about Coochie Hydrogreen franchise opportunities",
            "expected": "Coochie Hydrogreen",
            "description": "Direct franchise name mention"
        },
        {
            "question": "Tell me about eco-friendly car wash franchises in Australia",
            "expected": "Coochie Hydrogreen",
            "description": "Contextual keywords matching"
        },
        {
            "question": "I'm looking for waterless car cleaning business opportunities",
            "expected": "Coochie Hydrogreen", 
            "description": "Service-specific keywords"
        },
        {
            "question": "What pizza franchise options are available?",
            "expected": "Pizza Palace",
            "description": "Category-based detection"
        },
        {
            "question": "I need information about gym franchise opportunities",
            "expected": "FitLife Gym",
            "description": "Industry keyword matching"
        },
        {
            "question": "What's the weather like today?",
            "expected": None,
            "description": "Non-franchise related question"
        }
    ]
    
    print(f"Testing {len(test_questions)} questions...\n")
    
    results = []
    
    for i, test_case in enumerate(test_questions, 1):
        question = test_case["question"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        print(f"Test {i}: {description}")
        print(f"Question: '{question}'")
        
        try:
            if service.use_real_openai:
                # Use real detection
                result = await service.detect_franchisor_from_question(
                    question=question,
                    include_confidence=True,
                    use_embeddings=True
                )
                
                if result:
                    franchisor_id, confidence = result
                    if franchisor_id:
                        # Find franchisor name
                        franchisors = await service._get_active_franchisors_with_cache()
                        detected_name = None
                        for f in franchisors:
                            if f["id"] == franchisor_id:
                                detected_name = f["name"]
                                break
                        
                        print(f"✅ Detected: {detected_name} (confidence: {confidence:.2f})")
                        success = detected_name == expected
                    else:
                        print(f"❌ No franchisor detected (confidence: {confidence:.2f})")
                        success = expected is None
                else:
                    print("❌ Detection failed")
                    success = False
            else:
                # Mock responses for demo
                if "Coochie Hydrogreen" in question or "eco-friendly" in question or "waterless" in question:
                    print("✅ Detected: Coochie Hydrogreen (confidence: 0.85) [MOCK]")
                    success = expected == "Coochie Hydrogreen"
                elif "pizza" in question.lower():
                    print("✅ Detected: Pizza Palace (confidence: 0.82) [MOCK]")
                    success = expected == "Pizza Palace"
                elif "gym" in question.lower() or "fitness" in question.lower():
                    print("✅ Detected: FitLife Gym (confidence: 0.78) [MOCK]")
                    success = expected == "FitLife Gym"
                else:
                    print("❌ No franchisor detected (confidence: 0.15) [MOCK]")
                    success = expected is None
            
            results.append({
                "question": question,
                "expected": expected,
                "success": success
            })
            
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                "question": question,
                "expected": expected,
                "success": False
            })
        
        print("-" * 50)
    
    # Summary
    successful_tests = sum(1 for r in results if r["success"])
    total_tests = len(results)
    
    print("\n📊 Test Results Summary:")
    print(f"Successful: {successful_tests}/{total_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests == total_tests:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        
        # Show failed tests
        failed_tests = [r for r in results if not r["success"]]
        if failed_tests:
            print("\nFailed tests:")
            for test in failed_tests:
                print(f"  - '{test['question'][:50]}...' (expected: {test['expected']})")


async def demo_webhook_integration():
    """Demo webhook integration"""
    
    print("\n🔗 Webhook Integration Demo")
    print("=" * 50)
    
    # Simulate webhook message processing
    webhook_messages = [
        "Hi, I'm interested in Coochie Hydrogreen franchise opportunities in my area. Can you provide more information?",
        "What are the requirements for opening a pizza franchise?",
        "I need help with my current business operations."
    ]
    
    for i, message in enumerate(webhook_messages, 1):
        print(f"Webhook Message {i}:")
        print(f"'{message}'")
        
        # In real implementation, this would call the webhook processing function
        # which would use the franchisor detection service
        print("📨 Processing with enhanced webhook handler...")
        print("🔍 Dynamic franchisor detection enabled")
        print("🤖 RAG system will use detected franchisor context")
        print("-" * 50)


if __name__ == "__main__":
    print("Starting Advanced Franchisor Detection Demo...\n")
    
    # Run the demo
    asyncio.run(demo_franchisor_detection())
    asyncio.run(demo_webhook_integration())
    
    print("\n✨ Demo completed!")
    print("\nNext steps:")
    print("1. Set OPENAI_API_KEY environment variable for real API testing")
    print("2. Run the test suite: python -m pytest tests/test_franchisor_detection.py -v")
    print("3. Test webhook integration with real franchise questions")
    print("4. Monitor logs for detection accuracy and performance")
