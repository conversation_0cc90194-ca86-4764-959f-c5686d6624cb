#!/usr/bin/env python3
"""
Re-ingest Coochie Hydrogreen document using production-grade RAG system
"""

import asyncio
import sys
import json
import os
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add the project root to the path
sys.path.append('.')

# Import production RAG system
from production_rag_system import (
    ProductionTextNormalizer,
    ProductionChunker,
    ProductionEmbeddingService,
    ProductionVectorStore,
    DocumentChunk
)

class DocumentProcessor:
    """Production-grade document processor"""
    
    def __init__(self):
        self.normalizer = ProductionTextNormalizer()
        self.chunker = ProductionChunker(chunk_size=400, chunk_overlap=75)
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
    
    async def process_pdf(self, file_path: str) -> str:
        """Process PDF document with production-grade techniques"""
        try:
            print(f"📄 Processing PDF: {file_path}")
            
            # Use PyPDF2 for text extraction
            from PyPDF2 import PdfReader
            
            reader = PdfReader(file_path)
            text_content = ""
            
            # Extract text from each page
            for i, page in enumerate(reader.pages):
                page_text = page.extract_text()
                if page_text:
                    text_content += f"\n\n--- Page {i+1} ---\n\n{page_text}"
            
            print(f"✅ Extracted {len(text_content)} characters from PDF")
            
            # Normalize text
            normalized_text = self.normalizer.normalize_text(text_content)
            print(f"✅ Normalized text: {len(normalized_text)} characters")
            
            return normalized_text
            
        except Exception as e:
            print(f"❌ Error processing PDF: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    async def chunk_document(self, text: str, metadata: Dict[str, Any] = None) -> List[DocumentChunk]:
        """Chunk document with production-grade chunking"""
        try:
            print(f"📝 Chunking document: {len(text)} characters")
            
            # Create chunks
            chunks = self.chunker.chunk_text(text, metadata)
            
            print(f"✅ Created {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            print(f"❌ Error chunking document: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def generate_embeddings(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Generate embeddings for chunks"""
        try:
            print(f"🧠 Generating embeddings for {len(chunks)} chunks")
            
            # Extract text from chunks
            texts = [chunk.text for chunk in chunks]
            
            # Generate embeddings in batches
            embeddings = []
            batch_size = 5
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                print(f"  Processing batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
                
                for j, text in enumerate(batch_texts):
                    try:
                        embedding = self.embedding_service.generate_embedding(text)
                        embeddings.append(embedding)
                        print(f"    ✅ Generated embedding {i+j+1}/{len(texts)}: {len(embedding)} dimensions")
                    except Exception as e:
                        print(f"    ❌ Failed to generate embedding for chunk {i+j+1}: {e}")
                        embeddings.append([])
                
                # Add small delay to avoid rate limiting
                if i + batch_size < len(texts):
                    time.sleep(0.5)
            
            # Update chunks with embeddings
            for i, chunk in enumerate(chunks):
                if i < len(embeddings) and embeddings[i]:
                    chunk.embedding = embeddings[i]
            
            # Filter out chunks without embeddings
            valid_chunks = [chunk for chunk in chunks if chunk.embedding]
            print(f"✅ Generated {len(valid_chunks)}/{len(chunks)} valid embeddings")
            
            return valid_chunks
            
        except Exception as e:
            print(f"❌ Error generating embeddings: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def store_franchisor_embedding(
        self, 
        franchisor_id: str, 
        text: str, 
        chunks: List[DocumentChunk]
    ) -> bool:
        """Store franchisor embedding with production-grade approach"""
        try:
            print(f"💾 Storing embedding for franchisor: {franchisor_id}")
            
            # Clear existing embeddings
            self.vector_store.clear_franchisor_embeddings(franchisor_id)
            
            # Generate embedding for the full text
            embedding = self.embedding_service.generate_embedding(text)
            
            # Store embedding
            result = self.vector_store.store_franchisor_embedding(
                franchisor_id=franchisor_id,
                text_content=text,
                embedding=embedding,
                metadata={
                    'chunk_count': len(chunks),
                    'text_length': len(text),
                    'processing_time': time.time()
                }
            )
            
            print(f"✅ Stored franchisor embedding: {result}")
            return result
            
        except Exception as e:
            print(f"❌ Error storing franchisor embedding: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_retrieval(self, franchisor_id: str):
        """Test retrieval with production-grade parameters"""
        try:
            print(f"🔍 Testing retrieval for franchisor: {franchisor_id}")
            
            # Test questions
            test_questions = [
                "What is Coochie Hydrogreen?",
                "Where is Coochie Hydrogreen located?",
                "What services does Coochie Hydrogreen provide?",
                "What are the franchise fees?",
                "What training does the business provide?"
            ]
            
            for question in test_questions:
                print(f"\n❓ Question: {question}")
                
                # Generate query embedding
                query_embedding = self.embedding_service.generate_embedding(question)
                
                # Search with different thresholds
                thresholds = [0.8, 0.7, 0.5, 0.3, 0.1]
                
                for threshold in thresholds:
                    results = self.vector_store.search_similar(
                        query_embedding=query_embedding,
                        top_k=5,
                        similarity_threshold=threshold,
                        franchisor_id=franchisor_id
                    )
                    
                    print(f"  Threshold {threshold}: Found {len(results)} results")
                    
                    for i, result in enumerate(results):
                        print(f"    {i+1}. Score: {result.similarity_score:.4f} - {result.text[:50]}...")
                    
                    if results:
                        # Found results, no need to try lower thresholds
                        break
            
            print("\n✅ Retrieval testing completed")
            
        except Exception as e:
            print(f"❌ Error testing retrieval: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """Main function to re-ingest Coochie Hydrogreen document"""
    print("🚀 Re-ingesting Coochie Hydrogreen document with production-grade RAG")
    print("=" * 70)
    
    # Initialize document processor
    processor = DocumentProcessor()
    
    # Franchisor ID for Coochie Hydrogreen
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    # PDF path
    pdf_path = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
    
    # Process document
    text_content = await processor.process_pdf(pdf_path)
    if not text_content:
        print("❌ Failed to extract text from PDF")
        return
    
    # Create chunks
    chunks = await processor.chunk_document(text_content, {
        'source': 'Coochie_Information pack.pdf',
        'franchisor_id': franchisor_id
    })
    if not chunks:
        print("❌ Failed to create chunks")
        return
    
    # Generate embeddings
    chunks_with_embeddings = await processor.generate_embeddings(chunks)
    if not chunks_with_embeddings:
        print("❌ Failed to generate embeddings")
        return
    
    # Store franchisor embedding
    result = await processor.store_franchisor_embedding(
        franchisor_id=franchisor_id,
        text=text_content,
        chunks=chunks_with_embeddings
    )
    if not result:
        print("❌ Failed to store franchisor embedding")
        return
    
    # Test retrieval
    await processor.test_retrieval(franchisor_id)
    
    print("\n🎉 Re-ingestion completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
