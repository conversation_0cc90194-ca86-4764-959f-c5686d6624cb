#!/usr/bin/env python3
"""
Authenticated PDF Upload Test
Tests the complete PDF upload and ingestion workflow with authentication
"""

import os
import sys
import requests
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Test configuration
BASE_URL = "http://localhost:8000"
PDF_FILE_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
TOKEN_FILE = "test_auth_token.txt"

class AuthenticatedPDFTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.pdf_path = PDF_FILE_PATH
        self.access_token = None
        self.user_info = {}
        self.session_id = f"auth_test_{int(time.time())}"
        
    def log(self, message, level="INFO"):
        """Log messages with timestamp"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def load_auth_token(self):
        """Load authentication token from file"""
        try:
            if not os.path.exists(TOKEN_FILE):
                self.log(f"❌ Token file not found: {TOKEN_FILE}", "ERROR")
                self.log("Please run 'python create_test_user_and_token.py' first", "ERROR")
                return False
            
            with open(TOKEN_FILE, 'r') as f:
                content = f.read()
                
                # Parse token file
                for line in content.split('\n'):
                    if line.startswith('EMAIL='):
                        self.user_info['email'] = line.split('=', 1)[1]
                    elif line.startswith('USER_ID='):
                        self.user_info['user_id'] = line.split('=', 1)[1]
                    elif line.startswith('ACCESS_TOKEN='):
                        self.access_token = line.split('=', 1)[1]
            
            if self.access_token:
                self.log("✅ Authentication token loaded successfully")
                self.log(f"   - User: {self.user_info.get('email', 'Unknown')}")
                self.log(f"   - Token: {self.access_token[:30]}...")
                return True
            else:
                self.log("❌ No access token found in file", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Error loading token: {str(e)}", "ERROR")
            return False
    
    def get_headers(self):
        """Get request headers with authentication"""
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Accept": "application/json"
        }
    
    def test_server_health(self):
        """Test if the server is running"""
        try:
            self.log("🏥 Checking server health...")
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                self.log("✅ Server is healthy")
                return True
            else:
                self.log(f"⚠️ Server health check returned: {response.status_code}")
                return True  # Continue anyway
        except Exception as e:
            self.log(f"❌ Server health check failed: {str(e)}", "ERROR")
            return False
    
    def test_authentication(self):
        """Test authentication with the loaded token"""
        try:
            self.log("🔐 Testing authentication...")
            headers = self.get_headers()
            
            # Try to access a protected endpoint
            response = requests.get(f"{self.base_url}/api/agents/status", headers=headers)
            
            if response.status_code == 200:
                self.log("✅ Authentication successful")
                return True
            elif response.status_code == 401:
                self.log("❌ Authentication failed - token may be expired", "ERROR")
                return False
            else:
                self.log(f"⚠️ Unexpected response: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Authentication test failed: {str(e)}", "ERROR")
            return False
    
    def test_pdf_file_validation(self):
        """Test PDF file validation"""
        try:
            self.log("📄 Testing PDF file validation...")
            
            if not os.path.exists(self.pdf_path):
                self.log(f"❌ PDF file not found: {self.pdf_path}", "ERROR")
                return False
            
            file_size = os.path.getsize(self.pdf_path)
            self.log(f"✅ PDF file found: {os.path.basename(self.pdf_path)} ({file_size:,} bytes)")
            
            # Check file extension
            if not self.pdf_path.lower().endswith('.pdf'):
                self.log("❌ File is not a PDF", "ERROR")
                return False
            
            self.log("✅ PDF file validation passed")
            return True
            
        except Exception as e:
            self.log(f"❌ PDF file validation failed: {str(e)}", "ERROR")
            return False
    
    def test_agent_document_upload(self):
        """Test document upload via agent endpoint"""
        try:
            self.log("📤 Testing agent document upload...")
            
            with open(self.pdf_path, 'rb') as pdf_file:
                files = {
                    'file': ('Coochie_Information_pack.pdf', pdf_file, 'application/pdf')
                }
                data = {
                    'session_id': self.session_id,
                    'document_type': 'brochure'
                }
                
                headers = self.get_headers()
                # Remove Content-Type to let requests set it for multipart
                headers.pop('Accept', None)
                
                response = requests.post(
                    f"{self.base_url}/api/agents/upload-document",
                    files=files,
                    data=data,
                    headers=headers,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log("✅ Agent document upload successful")
                    self.log(f"   - Success: {result.get('success', False)}")
                    self.log(f"   - Message: {result.get('message', 'No message')}")
                    self.log(f"   - Session ID: {result.get('session_id', 'Unknown')}")
                    return True
                else:
                    self.log(f"❌ Agent upload failed: {response.status_code}", "ERROR")
                    self.log(f"   Response: {response.text}", "ERROR")
                    return False
                    
        except Exception as e:
            self.log(f"❌ Agent document upload error: {str(e)}", "ERROR")
            return False
    
    def test_franchisor_upload(self):
        """Test upload via franchisor endpoint"""
        try:
            self.log("🏢 Testing franchisor brochure upload...")
            
            # Use a test franchisor ID
            test_franchisor_id = "test_franchisor_123"
            
            with open(self.pdf_path, 'rb') as pdf_file:
                files = {
                    'file': ('Coochie_Information_pack.pdf', pdf_file, 'application/pdf')
                }
                
                headers = self.get_headers()
                # Remove Content-Type to let requests set it for multipart
                headers.pop('Accept', None)
                
                response = requests.post(
                    f"{self.base_url}/api/franchisors/{test_franchisor_id}/upload-brochure",
                    files=files,
                    headers=headers,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log("✅ Franchisor upload successful")
                    self.log(f"   - Success: {result.get('success', False)}")
                    message = result.get('message', {})
                    if isinstance(message, dict):
                        self.log(f"   - Title: {message.get('title', 'No title')}")
                        self.log(f"   - Description: {message.get('description', 'No description')}")
                    return True
                elif response.status_code == 404:
                    self.log("⚠️ Franchisor not found (expected for test ID)")
                    return True  # This is expected for a test franchisor ID
                else:
                    self.log(f"❌ Franchisor upload failed: {response.status_code}", "ERROR")
                    self.log(f"   Response: {response.text}", "ERROR")
                    return False
                    
        except Exception as e:
            self.log(f"❌ Franchisor upload error: {str(e)}", "ERROR")
            return False
    
    def test_chat_with_agents(self):
        """Test chat functionality with uploaded document"""
        try:
            self.log("💬 Testing chat with agents...")
            
            test_questions = [
                "Hello! I'm interested in franchise opportunities.",
                "What are the investment requirements for this franchise?",
                "Tell me about the franchise fees and ongoing costs.",
                "What support is provided to franchisees?",
                "What are the territory requirements and restrictions?"
            ]
            
            headers = self.get_headers()
            
            for i, question in enumerate(test_questions, 1):
                self.log(f"   Question {i}: {question}")
                
                chat_data = {
                    "message": question,
                    "session_id": self.session_id
                }
                
                response = requests.post(
                    f"{self.base_url}/api/agents/chat",
                    json=chat_data,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result.get('response', 'No response')
                    intent = result.get('intent', 'Unknown')
                    
                    self.log(f"   ✅ Response: {answer[:100]}...")
                    self.log(f"   📍 Intent: {intent}")
                    
                    # Small delay between questions
                    time.sleep(2)
                else:
                    self.log(f"   ❌ Chat failed: {response.status_code} - {response.text}", "ERROR")
                    return False
            
            self.log("✅ Chat functionality test completed")
            return True
            
        except Exception as e:
            self.log(f"❌ Chat functionality test failed: {str(e)}", "ERROR")
            return False
    
    def test_non_pdf_rejection(self):
        """Test that non-PDF files are rejected"""
        try:
            self.log("🚫 Testing non-PDF file rejection...")
            
            # Create a temporary text file
            temp_file_path = "/tmp/test_document.txt"
            with open(temp_file_path, 'w') as f:
                f.write("This is a test document that should be rejected.")
            
            try:
                with open(temp_file_path, 'rb') as test_file:
                    files = {
                        'file': ('test_document.txt', test_file, 'text/plain')
                    }
                    data = {
                        'session_id': self.session_id,
                        'document_type': 'brochure'
                    }
                    
                    headers = self.get_headers()
                    headers.pop('Accept', None)
                    
                    response = requests.post(
                        f"{self.base_url}/api/agents/upload-document",
                        files=files,
                        data=data,
                        headers=headers,
                        timeout=30
                    )
                    
                    if response.status_code == 400:
                        self.log("✅ Non-PDF file correctly rejected")
                        return True
                    else:
                        self.log(f"❌ Non-PDF file was not rejected: {response.status_code}", "ERROR")
                        return False
            finally:
                # Clean up temp file
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    
        except Exception as e:
            self.log(f"❌ Non-PDF rejection test failed: {str(e)}", "ERROR")
            return False
    
    def run_all_tests(self):
        """Run all tests in sequence"""
        self.log("🚀 Starting authenticated PDF upload tests...")
        self.log("=" * 60)
        
        tests = [
            ("Load Authentication Token", self.load_auth_token),
            ("Server Health Check", self.test_server_health),
            ("Authentication Test", self.test_authentication),
            ("PDF File Validation", self.test_pdf_file_validation),
            ("Agent Document Upload", self.test_agent_document_upload),
            ("Franchisor Upload", self.test_franchisor_upload),
            ("Chat with Agents", self.test_chat_with_agents),
            ("Non-PDF Rejection", self.test_non_pdf_rejection),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            self.log(f"\n🧪 Running: {test_name}")
            self.log("-" * 40)
            
            try:
                result = test_func()
                results[test_name] = result
                
                if result:
                    self.log(f"✅ {test_name}: PASSED")
                else:
                    self.log(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                self.log(f"💥 {test_name}: ERROR - {str(e)}", "ERROR")
                results[test_name] = False
        
        # Summary
        self.log("\n" + "=" * 60)
        self.log("📊 TEST SUMMARY")
        self.log("=" * 60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            self.log(f"{test_name}: {status}")
        
        self.log(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            self.log("🎉 All tests passed! Authenticated PDF upload system is working correctly.")
        else:
            self.log("⚠️ Some tests failed. Please check the logs above for details.")
        
        return passed == total


def main():
    """Main function to run the tests"""
    print("🔐 Authenticated PDF Upload Test")
    print("=" * 40)
    
    # Check if PDF file exists
    if not os.path.exists(PDF_FILE_PATH):
        print(f"❌ ERROR: PDF file not found at {PDF_FILE_PATH}")
        print("Please ensure the Coochie_Information pack.pdf file is in the correct location.")
        return False
    
    # Run tests
    tester = AuthenticatedPDFTester()
    success = tester.run_all_tests()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
