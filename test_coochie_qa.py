#!/usr/bin/env python3
"""
Test script to check Coochie Hydrogreen QA system
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append('.')

async def test_coochie_qa():
    """Test the QA system for Coochie Hydrogreen"""
    try:
        # Import the DocQA central API
        from docqa.central_api import ask_question
        
        # Test question about franchisee approval process
        test_questions = [
            "What is FRANCHISEE APPROVAL PROCESS?",
            "What is the franchise fee?",
            "What are the requirements to become a franchisee?",
            "Tell me about Coochie Hydrogreen franchise",
            "What is the initial investment required?"
        ]
        
        print("🧪 Testing Coochie Hydrogreen QA System")
        print("=" * 60)
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n📝 Test {i}: {question}")
            print("-" * 40)
            
            # Prepare request with Coochie Hydrogreen franchisor ID
            request = {
                "question": question,
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",  # Coochie Hydrogreen
                "top_k": 5,
                "similarity_threshold": 0.6,  # Lower threshold to get more results
                "include_metadata": True
            }
            
            # Get answer
            result = await ask_question(request)
            
            print(f"✅ Success: {result.get('success', False)}")
            print(f"📄 Answer: {result.get('answer', 'No answer')}")
            
            if 'metadata' in result:
                metadata = result['metadata']
                if 'sources' in metadata:
                    print(f"📚 Sources: {len(metadata['sources'])} found")
                    for j, source in enumerate(metadata['sources'][:3], 1):
                        print(f"   {j}. {source.get('content', '')[:100]}...")
                
                if 'franchisor_info' in metadata:
                    franchisor_info = metadata['franchisor_info']
                    print(f"🏢 Franchisor: {franchisor_info.get('franchisor_name', 'Unknown')}")
            
            print()
        
        print("=" * 60)
        print("🏁 Testing completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_coochie_qa())
