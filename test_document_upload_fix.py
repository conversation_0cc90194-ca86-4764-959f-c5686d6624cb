#!/usr/bin/env python3
"""
Test script to verify the document upload fix
"""

import requests
import time
import tempfile
import os


def create_test_file():
    """Create a simple test PDF file"""
    content = """
    TEST DOCUMENT FOR GROWTHHIVE DOCQA
    
    This is a test document to verify that the document ID fix is working correctly.
    
    Key Information:
    - Document Type: Test PDF
    - Purpose: Verify DocQA integration
    - Expected Result: No foreign key constraint violation
    
    Business Details:
    - Investment Range: $50,000 - $100,000
    - Franchise Fee: $25,000
    - Territory: Melbourne, Australia
    - Support: Training and ongoing support provided
    
    Contact Information:
    - Email: <EMAIL>
    - Phone: +61 3 9876 5432
    - Website: www.testfranchise.com.au
    """
    
    # Create a temporary text file (we'll upload as PDF)
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(content)
        return f.name


def test_document_upload():
    """Test document upload with the fix"""
    print("🧪 Testing document upload with DocQA integration fix...")
    
    # API endpoint
    base_url = "http://localhost:8000"
    upload_url = f"{base_url}/api/documents/upload"
    
    # Login first to get token
    login_url = f"{base_url}/api/auth/login"
    login_data = {
        "email_or_mobile": "<EMAIL>",
        "password": "Admin@1234",
        "remember_me": True
    }
    
    print("🔐 Logging in...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return False
    
    login_result = login_response.json()
    token = login_result["data"]["access_token"]
    print("✅ Login successful")
    
    # Create test file
    test_file_path = create_test_file()
    
    try:
        # Prepare upload
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        files = {
            'file': ('test_document.txt', open(test_file_path, 'rb'), 'text/plain')
        }
        
        data = {
            'name': 'Test Document for DocQA Fix',
            'description': 'Testing document ID fix for foreign key constraint',
            'is_active': True
        }
        
        print("📄 Uploading test document...")
        upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
        
        files['file'][1].close()  # Close the file
        
        if upload_response.status_code == 201:
            result = upload_response.json()
            document_id = result["data"]["id"]
            print("✅ Document uploaded successfully!")
            print(f"📋 Document ID: {document_id}")
            print("⏳ DocQA processing should be initiated automatically...")
            
            # Wait a bit for processing
            print("⏳ Waiting 10 seconds for DocQA processing...")
            time.sleep(10)
            
            return True
        else:
            print(f"❌ Document upload failed: {upload_response.status_code}")
            print(upload_response.text)
            return False
            
    finally:
        # Clean up test file
        try:
            os.unlink(test_file_path)
        except:
            pass


def check_server_logs():
    """Check if there are any foreign key constraint violations in recent logs"""
    print("\n🔍 Checking server logs for foreign key constraint violations...")
    print("(Check the server terminal for any database errors)")
    print("✅ If no 'foreign key constraint violation' errors appear, the fix is working!")


def main():
    """Run the test"""
    print("🚀 Starting document upload fix verification...\n")
    
    try:
        success = test_document_upload()
        
        if success:
            check_server_logs()
            print("\n🎯 Test completed successfully!")
            print("\n📝 What to check:")
            print("1. Document uploaded without errors")
            print("2. No foreign key constraint violations in server logs")
            print("3. DocQA processing should use the correct document ID")
            print("4. document_chunks table should reference existing document")
        else:
            print("\n❌ Test failed!")
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
