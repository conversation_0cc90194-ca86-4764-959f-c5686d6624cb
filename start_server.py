#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to start the GrowthHive server with proper environment loading
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Verify required environment variables
required_vars = [
    'OPENAI_API_KEY',
    'DATABASE_URL',
]

missing_vars = []
for var in required_vars:
    if not os.getenv(var):
        missing_vars.append(var)

if missing_vars:
    print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
    sys.exit(1)

print("✅ All required environment variables are loaded")
print(f"✅ OPENAI_API_KEY: {'*' * 20}...{os.getenv('OPENAI_API_KEY', '')[-10:]}")
print(f"✅ DATABASE_URL: {os.getenv('DATABASE_URL', '')}")

# Start the server
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
