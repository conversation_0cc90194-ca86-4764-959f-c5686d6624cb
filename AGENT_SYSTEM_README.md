# 🤖 Multi-Agent System for GrowthHive

A production-grade, modular multi-agent system built with LangGraph for handling franchise consulting workflows including document ingestion, question answering, lead qualification, and meeting booking.

## 🏗️ Architecture Overview

The system uses **LangGraph** for orchestrating multiple specialized agents in a stateful workflow. Each agent has specific responsibilities and can communicate through a shared state system.

### Core Components

- **LangGraph StateGraph**: Manages workflow orchestration and state transitions
- **Specialized Agents**: Each handles specific business logic
- **Tool Registry**: Centralized tool management and discovery
- **Redis Memory**: Session management and conversation history
- **PostgreSQL + pgvector**: Database and vector storage for documents
- **FastAPI Endpoints**: REST API for system interaction

## 🎯 Agent Roles

### 1. **Conversation Agent**
- Handles greetings and general conversation
- Routes out-of-scope queries appropriately
- Maintains conversation context

### 2. **Document Ingestion Agent**
- Processes PDF documents only
- Extracts text and generates embeddings
- Stores documents in vector database
- Integrates with existing DocQA system

### 3. **Question Answering Agent**
- Answers questions using RAG (Retrieval Augmented Generation)
- Searches relevant documents for context
- Escalates complex queries to human consultants

### 4. **Lead Qualification Agent**
- Collects lead information through structured questioning
- Validates and scores lead quality
- Updates lead status based on qualification criteria

### 5. **Meeting Booking Agent**
- Integrates with Zoho Meetings API
- Checks calendar availability
- Books and manages consultation appointments

## 🛠️ Key Features

### ✅ **Modular Design**
- Each agent is independently testable and deployable
- Easy to add new agents or modify existing ones
- Clear separation of concerns

### ✅ **State Management**
- Persistent conversation state using Redis
- Cross-agent memory sharing
- Session-based context management

### ✅ **Tool System**
- Centralized tool registry
- Reusable tools across agents
- Easy tool discovery and instantiation

### ✅ **Error Handling**
- Comprehensive error handling and retries
- Graceful degradation when services are unavailable
- Detailed logging and monitoring

### ✅ **Scalability**
- Async/await throughout for high performance
- Stateless agent design for horizontal scaling
- Efficient resource utilization

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

Update your `.env` file with the required configuration:

```env
# LangGraph Configuration
LANGGRAPH_VERBOSE=true
LANGGRAPH_CHECKPOINT_ENABLED=true
LANGGRAPH_MAX_EXECUTION_TIME=300

# Redis Configuration
REDIS_URL=redis://localhost:6379/1
REDIS_SESSION_DB=2

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4-turbo

# Zoho Meetings Configuration
ZOHO_CLIENT_ID=your_zoho_client_id
ZOHO_CLIENT_SECRET=your_zoho_client_secret
```

### 3. Run the Demo

```bash
python agent_system_demo.py
```

### 4. Start the API Server

```bash
uvicorn app.main:app --reload
```

## 📡 API Endpoints

### Chat with Agents
```http
POST /api/agents/chat
Content-Type: application/json

{
  "message": "Hello, I'm interested in franchise opportunities",
  "session_id": "user_123",
  "context": {}
}
```

### Upload PDF Document
```http
POST /api/agents/upload-document
Content-Type: multipart/form-data

file: [PDF file only]
session_id: user_123
document_type: brochure
```

### Get System Status
```http
GET /api/agents/status
```

### Webhook Handler
```http
POST /api/agents/webhook/message
Content-Type: application/json

{
  "message": "User message from external platform",
  "sender": "user_phone_number",
  "platform": "whatsapp"
}
```

## 🔧 Configuration

### Agent Configuration

Each agent can be configured with:

```python
AgentConfig(
    role=AgentRole.CONVERSATION,
    name="Conversation Agent",
    description="Handle user greetings and general conversation",
    model="gpt-4-turbo",
    temperature=0.1,
    max_tokens=1000,
    tools=["store_memory", "retrieve_memory"]
)
```

### Tool Registry

Tools are automatically registered and can be accessed by name:

```python
# Get specific tools
tools = tool_registry.get_tools_by_names(["create_lead", "book_meeting"])

# Get tools by category
database_tools = tool_registry.get_tools_by_category("database")
```

## 🧪 Testing

### Run the Demo Script
```bash
python agent_system_demo.py
```

### Test Individual Agents
```python
from app.agents.orchestrator import AgentOrchestrator

orchestrator = AgentOrchestrator()
result = await orchestrator.process_message(
    message="Hello!",
    session_id="test_session"
)
```

### Health Checks
```python
agent = orchestrator.agents.get("conversation")
is_healthy = await agent.health_check()
```

## 📊 Monitoring and Observability

### Agent Status
Each agent provides detailed status information:
- Execution count and success rate
- Current status and last activity
- Error counts and health status

### Workflow Tracking
- Execution path tracking through agents
- State transitions and decision points
- Performance metrics and timing

### Logging
Structured logging with:
- Agent-specific log contexts
- Request/response tracking
- Error details and stack traces

## 🔄 Workflow Example

1. **User Input**: "I'm interested in McDonald's franchise"
2. **Router**: Classifies intent as "document_question"
3. **Question Answering Agent**: 
   - Searches for McDonald's franchise documents
   - Generates response using RAG
   - Checks if escalation needed
4. **Response**: Provides franchise information and offers consultation

## 🛡️ Security Features

- JWT authentication for API endpoints
- Input validation and sanitization
- Rate limiting and request throttling
- Secure credential management

## 📈 Performance Optimization

- Async/await for non-blocking operations
- Connection pooling for database and Redis
- Caching of frequently accessed data
- Efficient vector similarity search

## 🔮 Future Enhancements

- [ ] Multi-language support
- [ ] Voice interaction capabilities
- [ ] Advanced analytics and reporting
- [ ] A/B testing for agent responses
- [ ] Integration with more meeting platforms
- [ ] Advanced lead scoring algorithms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ using LangGraph, FastAPI, and OpenAI**
