"""
Tests for the Multi-Agent System
Comprehensive test suite for agents, tools, and orchestration
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from app.agents.base import Agent<PERSON>onfig, AgentRole, AgentState
from app.agents.orchestrator import AgentOrchestrator
from app.agents.conversation import ConversationAgent
from app.agents.lead_qualification import LeadQualificationAgent
from app.agents.meeting_booking import MeetingBookingAgent
from app.agents.document_ingestion import DocumentIngestionAgent


class TestAgentBase:
    """Test base agent functionality"""
    
    def test_agent_config_creation(self):
        """Test agent configuration creation"""
        config = AgentConfig(
            role=AgentRole.CONVERSATION,
            name="Test Agent",
            description="Test agent for unit tests",
            model="gpt-4-turbo",
            temperature=0.1
        )
        
        assert config.role == AgentRole.CONVERSATION
        assert config.name == "Test Agent"
        assert config.model == "gpt-4-turbo"
        assert config.temperature == 0.1
    
    def test_agent_state_structure(self):
        """Test agent state structure"""
        state: AgentState = {
            "user_input": "Hello",
            "messages": [],
            "session_id": "test_session",
            "intent": None,
            "next_action": None,
            "lead_id": None,
            "lead_data": None,
            "lead_status": None,
            "document_id": None,
            "document_content": None,
            "search_results": None,
            "meeting_data": None,
            "availability": None,
            "context": {},
            "conversation_history": [],
            "response": None,
            "error": None,
            "metadata": {},
            "current_agent": None,
            "execution_path": [],
            "retry_count": 0
        }
        
        assert state["user_input"] == "Hello"
        assert state["session_id"] == "test_session"
        assert state["retry_count"] == 0


class TestConversationAgent:
    """Test conversation agent functionality"""
    
    @pytest.fixture
    def conversation_agent(self):
        """Create conversation agent for testing"""
        config = AgentConfig(
            role=AgentRole.CONVERSATION,
            name="Test Conversation Agent",
            description="Test conversation agent"
        )
        return ConversationAgent(config)
    
    @pytest.mark.asyncio
    async def test_greeting_handling(self, conversation_agent):
        """Test greeting message handling"""
        state: AgentState = {
            "user_input": "Hello there!",
            "messages": [],
            "session_id": "test_session",
            "intent": "greeting",
            "next_action": None,
            "lead_id": None,
            "lead_data": None,
            "lead_status": None,
            "document_id": None,
            "document_content": None,
            "search_results": None,
            "meeting_data": None,
            "availability": None,
            "context": {},
            "conversation_history": [],
            "response": None,
            "error": None,
            "metadata": {},
            "current_agent": None,
            "execution_path": [],
            "retry_count": 0
        }
        
        with patch.object(conversation_agent.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "Hello! Welcome to GrowthHive. How can I help you with franchise opportunities today?"
            
            result = await conversation_agent.process_state(state)
            
            assert result["response"] is not None
            assert result["next_action"] == "end"
            assert len(result["messages"]) > 0
    
    @pytest.mark.asyncio
    async def test_out_of_scope_handling(self, conversation_agent):
        """Test out-of-scope query handling"""
        state: AgentState = {
            "user_input": "What's the weather like?",
            "messages": [],
            "session_id": "test_session",
            "intent": "out_of_scope",
            "next_action": None,
            "lead_id": None,
            "lead_data": None,
            "lead_status": None,
            "document_id": None,
            "document_content": None,
            "search_results": None,
            "meeting_data": None,
            "availability": None,
            "context": {},
            "conversation_history": [],
            "response": None,
            "error": None,
            "metadata": {},
            "current_agent": None,
            "execution_path": [],
            "retry_count": 0
        }
        
        with patch.object(conversation_agent.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "I'm specialized in franchise opportunities. How can I help you with franchising?"
            
            result = await conversation_agent.process_state(state)
            
            assert "franchise" in result["response"].lower()
            assert result["next_action"] == "end"


class TestLeadQualificationAgent:
    """Test lead qualification agent functionality"""
    
    @pytest.fixture
    def lead_agent(self):
        """Create lead qualification agent for testing"""
        config = AgentConfig(
            role=AgentRole.LEAD_QUALIFICATION,
            name="Test Lead Agent",
            description="Test lead qualification agent"
        )
        return LeadQualificationAgent(config)
    
    @pytest.mark.asyncio
    async def test_lead_information_extraction(self, lead_agent):
        """Test lead information extraction"""
        with patch.object(lead_agent.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = '{"full_name": "John Smith", "location": "Sydney", "budget_preference": "500000"}'
            
            result = await lead_agent._extract_lead_information(
                "Hi, I'm John Smith from Sydney looking to invest $500,000",
                {}
            )
            
            assert result["full_name"] == "John Smith"
            assert result["location"] == "Sydney"
            assert result["budget_preference"] == "500000"
    
    @pytest.mark.asyncio
    async def test_qualification_assessment(self, lead_agent):
        """Test lead qualification assessment"""
        lead_data = {
            "full_name": "John Smith",
            "email": "<EMAIL>",
            "location": "Sydney",
            "budget_preference": "500000",
            "experience": "5 years business experience"
        }
        
        with patch.object(lead_agent.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "qualified"
            
            result = await lead_agent._assess_qualification(lead_data)
            
            assert result == "qualified"


class TestMeetingBookingAgent:
    """Test meeting booking agent functionality"""
    
    @pytest.fixture
    def meeting_agent(self):
        """Create meeting booking agent for testing"""
        config = AgentConfig(
            role=AgentRole.MEETING_BOOKING,
            name="Test Meeting Agent",
            description="Test meeting booking agent"
        )
        return MeetingBookingAgent(config)
    
    @pytest.mark.asyncio
    async def test_booking_intent_determination(self, meeting_agent):
        """Test meeting booking intent determination"""
        with patch.object(meeting_agent.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "book_meeting"
            
            result = await meeting_agent._determine_booking_intent(
                "I'd like to schedule a consultation for next Tuesday"
            )
            
            assert result == "book_meeting"
    
    @pytest.mark.asyncio
    async def test_availability_generation(self, meeting_agent):
        """Test availability slot generation"""
        from datetime import timedelta
        
        start_date = datetime.now()
        end_date = start_date + timedelta(days=7)
        
        availability = await meeting_agent._get_mock_availability()
        
        assert len(availability) > 0
        assert all("datetime" in slot for slot in availability)
        assert all("display_time" in slot for slot in availability)


class TestAgentOrchestrator:
    """Test agent orchestrator functionality"""
    
    @pytest.fixture
    def orchestrator(self):
        """Create orchestrator for testing"""
        return AgentOrchestrator()
    
    def test_orchestrator_initialization(self, orchestrator):
        """Test orchestrator initialization"""
        assert orchestrator.graph is not None
        assert len(orchestrator.agents) > 0
        assert "conversation" in orchestrator.agents
        assert "question_answering" in orchestrator.agents
        assert "lead_qualification" in orchestrator.agents
        assert "meeting_booking" in orchestrator.agents
        assert "document_ingestion" in orchestrator.agents
    
    @pytest.mark.asyncio
    async def test_intent_routing(self, orchestrator):
        """Test intent-based routing"""
        state: AgentState = {
            "user_input": "Hello there!",
            "messages": [],
            "session_id": "test_session",
            "intent": None,
            "next_action": None,
            "lead_id": None,
            "lead_data": None,
            "lead_status": None,
            "document_id": None,
            "document_content": None,
            "search_results": None,
            "meeting_data": None,
            "availability": None,
            "context": {},
            "conversation_history": [],
            "response": None,
            "error": None,
            "metadata": {},
            "current_agent": None,
            "execution_path": [],
            "retry_count": 0
        }
        
        with patch.object(orchestrator.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "greeting"
            
            result = await orchestrator._route_request(state)
            
            assert result["intent"] == "greeting"
            assert "router" in result["execution_path"]
    
    def test_next_agent_determination(self, orchestrator):
        """Test next agent determination logic"""
        # Test greeting intent
        state = {"intent": "greeting", "error": None}
        next_agent = orchestrator._determine_next_agent(state)
        assert next_agent == "conversation"
        
        # Test document question intent
        state = {"intent": "document_question", "error": None}
        next_agent = orchestrator._determine_next_agent(state)
        assert next_agent == "question_answering"
        
        # Test error handling
        state = {"intent": "greeting", "error": "Some error"}
        next_agent = orchestrator._determine_next_agent(state)
        assert next_agent == "error"
    
    @pytest.mark.asyncio
    async def test_message_processing(self, orchestrator):
        """Test end-to-end message processing"""
        with patch.object(orchestrator.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "greeting"
            
            # Mock agent processing
            for agent in orchestrator.agents.values():
                agent.process_state = AsyncMock(return_value={
                    "response": "Test response",
                    "next_action": "end",
                    "messages": []
                })
            
            result = await orchestrator.process_message(
                message="Hello!",
                session_id="test_session"
            )
            
            assert result["success"] is True
            assert "response" in result


class TestDocumentIngestionAgent:
    """Test document ingestion agent functionality"""

    @pytest.fixture
    def document_agent(self):
        """Create document ingestion agent for testing"""
        config = AgentConfig(
            role=AgentRole.DOCUMENT_INGESTION,
            name="Test Document Agent",
            description="Test document ingestion agent"
        )
        return DocumentIngestionAgent(config)

    def test_supported_formats_pdf_only(self, document_agent):
        """Test that only PDF format is supported"""
        supported_formats = document_agent.get_supported_formats()
        assert len(supported_formats) == 1
        assert "PDF (.pdf)" in supported_formats

    @pytest.mark.asyncio
    async def test_pdf_file_processing(self, document_agent):
        """Test PDF file processing"""
        state: AgentState = {
            "user_input": "Please process this PDF document",
            "messages": [],
            "session_id": "test_session",
            "intent": "document_upload",
            "next_action": None,
            "lead_id": None,
            "lead_data": None,
            "lead_status": None,
            "document_id": None,
            "document_content": None,
            "search_results": None,
            "meeting_data": None,
            "availability": None,
            "context": {
                "document_path": "/path/to/test.pdf",
                "franchisor_id": "test_franchisor_123",
                "document_type": "brochure"
            },
            "conversation_history": [],
            "response": None,
            "error": None,
            "metadata": {},
            "current_agent": None,
            "execution_path": [],
            "retry_count": 0
        }

        with patch.object(document_agent, '_extract_text_from_file', new_callable=AsyncMock) as mock_extract:
            mock_extract.return_value = "Sample PDF content for testing..."

            with patch.object(document_agent, '_store_document_embeddings', new_callable=AsyncMock) as mock_store:
                mock_store.return_value = "doc_123"

                with patch.object(document_agent.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
                    mock_llm.return_value.content = "This is a franchise brochure containing investment information."

                    result = await document_agent.process_state(state)

                    assert result["response"] is not None
                    assert "processed successfully" in result["response"]
                    assert result["document_id"] == "doc_123"
                    assert result["next_action"] == "end"


class TestAgentTools:
    """Test agent tools functionality"""

    def test_tool_registry_initialization(self):
        """Test tool registry initialization"""
        from app.agents.tools.registry import tool_registry

        available_tools = tool_registry.list_available_tools()
        assert len(available_tools) > 0
        assert "create_lead" in available_tools
        assert "search_documents" in available_tools
        assert "book_meeting" in available_tools
    
    def test_tool_categories(self):
        """Test tool categorization"""
        from app.agents.tools.registry import tool_registry
        
        categories = tool_registry.list_categories()
        assert "database" in categories
        assert "document" in categories
        assert "meeting" in categories
        assert "memory" in categories
    
    def test_tool_retrieval(self):
        """Test tool retrieval by name and category"""
        from app.agents.tools.registry import tool_registry
        
        # Test single tool retrieval
        create_lead_tool = tool_registry.get_tool("create_lead")
        assert create_lead_tool is not None
        assert create_lead_tool.name == "create_lead"
        
        # Test category-based retrieval
        database_tools = tool_registry.get_tools_by_category("database")
        assert len(database_tools) > 0


# Integration Tests
class TestIntegration:
    """Integration tests for the complete system"""

    @pytest.mark.asyncio
    async def test_full_conversation_flow(self):
        """Test a complete conversation flow"""
        orchestrator = AgentOrchestrator()

        # Mock LLM responses
        with patch.object(orchestrator.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "greeting"

            # Mock all agent processing
            for agent in orchestrator.agents.values():
                agent.process_state = AsyncMock(return_value={
                    "response": "Hello! How can I help you with franchise opportunities?",
                    "next_action": "end",
                    "messages": [],
                    "intent": "greeting"
                })

            result = await orchestrator.process_message(
                message="Hello, I'm interested in franchises",
                session_id="integration_test"
            )

            assert result["success"] is True
            assert result["response"] is not None
            assert result.get("intent") == "greeting"

    @pytest.mark.asyncio
    async def test_pdf_document_ingestion_flow(self):
        """Test PDF document ingestion through the complete system"""
        orchestrator = AgentOrchestrator()

        # Mock LLM responses for document ingestion
        with patch.object(orchestrator.llm, 'ainvoke', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value.content = "document_upload"

            # Mock document agent processing
            doc_agent = orchestrator.agents.get("document_ingestion")
            if doc_agent:
                doc_agent.process_state = AsyncMock(return_value={
                    "response": "PDF document processed successfully! I've analyzed the content and it's now available for questions.",
                    "next_action": "end",
                    "messages": [],
                    "document_id": "test_doc_123"
                })

            result = await orchestrator.process_message(
                message="Please process the uploaded brochure document: test.pdf",
                session_id="pdf_test",
                context={
                    "document_path": "/tmp/test.pdf",
                    "franchisor_id": "test_franchisor",
                    "document_type": "brochure"
                }
            )

            assert result["success"] is True
            assert "processed successfully" in result["response"]

    @pytest.mark.asyncio
    async def test_franchisor_brochure_upload_integration(self):
        """Test franchisor brochure upload with agent integration"""
        from app.services.franchisor_service import FranchisorService

        # Mock dependencies
        mock_repo = Mock()
        mock_s3_service = Mock()
        mock_s3_service.upload_file = AsyncMock(return_value="brochures/test_brochure.pdf")

        service = FranchisorService(repository=mock_repo, s3_service=mock_s3_service)

        # Mock the agent processing method
        service._process_brochure_with_agents = AsyncMock()

        # Mock file upload
        mock_file = Mock()
        mock_file.filename = "test_brochure.pdf"

        # Test the integration
        with patch.object(mock_repo, 'update_brochure_url', new_callable=AsyncMock):
            await service.update_franchisor(
                franchisor_id="test_franchisor_123",
                update_data={},
                brochure_file=mock_file
            )

            # Verify agent processing was called
            service._process_brochure_with_agents.assert_called_once_with(
                "test_franchisor_123",
                "brochures/test_brochure.pdf"
            )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
