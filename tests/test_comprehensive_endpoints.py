"""
Comprehensive test suite for all GrowthHive endpoints.
This test suite can be run multiple times to verify all endpoints are working correctly.
"""

import pytest
from httpx import AsyncClient
from fastapi.testclient import TestClient
from app.main import app
from app.core.database.connection import get_db
from tests.utils import TestDataFactory, HTTP_STATUS_CODES


class TestComprehensiveEndpoints:
    """Comprehensive test suite for all endpoints"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self, test_db_async):
        """Setup for each test method"""
        self.db = test_db_async
        app.dependency_overrides[get_db] = lambda: test_db_async
        self.client = TestClient(app)
        self.async_client = AsyncClient(app=app, base_url="http://test")
    
    def test_health_check(self):
        """Test basic health check endpoint"""
        response = self.client.get("/health")
        assert response.status_code == HTTP_STATUS_CODES["OK"]
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_api_docs_accessible(self):
        """Test that API documentation is accessible"""
        response = self.client.get("/docs")
        assert response.status_code == HTTP_STATUS_CODES["OK"]
        
        response = self.client.get("/redoc")
        assert response.status_code == HTTP_STATUS_CODES["OK"]
    
    def test_openapi_schema(self):
        """Test OpenAPI schema generation"""
        response = self.client.get("/openapi.json")
        assert response.status_code == HTTP_STATUS_CODES["OK"]
        schema = response.json()
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema

    def test_categories_pagination(self):
        """Test categories listing with pagination"""
        # First login to get token
        login_response = self.client.post("/api/auth/login", json={
            "email_or_mobile": "<EMAIL>",
            "password": "Admin@1234",
            "remember_me": True
        })
        assert login_response.status_code == HTTP_STATUS_CODES["OK"]
        token = login_response.json()["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Test categories listing with pagination
        response = self.client.get("/api/categories?skip=0&limit=10", headers=headers)
        assert response.status_code == HTTP_STATUS_CODES["OK"]
        data = response.json()

        # Verify pagination structure
        assert "success" in data
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
        assert "total_count" in data["data"]
        assert "pagination" in data["data"]

        # Verify pagination info structure
        pagination = data["data"]["pagination"]
        assert "current_page" in pagination
        assert "total_pages" in pagination
        assert "items_per_page" in pagination
        assert "total_items" in pagination
        assert pagination["items_per_page"] == 10
        assert pagination["current_page"] >= 1

        # Verify each item has required fields
        for item in data["data"]["items"]:
            assert "id" in item
            assert "name" in item
            assert "is_active" in item
            assert "is_deleted" in item
            assert "created_at" in item
            assert "updated_at" in item

    def test_subcategories_pagination(self):
        """Test subcategories listing with pagination"""
        # First login to get token
        login_response = self.client.post("/api/auth/login", json={
            "email_or_mobile": "<EMAIL>",
            "password": "Admin@1234",
            "remember_me": True
        })
        assert login_response.status_code == HTTP_STATUS_CODES["OK"]
        token = login_response.json()["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Test subcategories listing with pagination using existing category
        response = self.client.get("/api/categories/07a64d9b-1f6f-4fe4-aa76-84de693d2e06/subcategories?skip=0&limit=5", headers=headers)
        assert response.status_code == HTTP_STATUS_CODES["OK"]
        data = response.json()

        # Verify pagination structure
        assert "success" in data
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
        assert "total_count" in data["data"]
        assert "pagination" in data["data"]

        # Verify pagination info structure
        pagination = data["data"]["pagination"]
        assert "current_page" in pagination
        assert "total_pages" in pagination
        assert "items_per_page" in pagination
        assert "total_items" in pagination
        assert pagination["items_per_page"] == 5
        assert pagination["current_page"] >= 1

        # Verify each item has required fields
        for item in data["data"]["items"]:
            assert "id" in item
            assert "name" in item
            assert "is_active" in item
            assert "is_deleted" in item
            assert "category_id" in item
            assert "created_at" in item
            assert "updated_at" in item

    def test_subcategories_search_pagination(self):
        """Test subcategories search with pagination"""
        # First login to get token
        login_response = self.client.post("/api/auth/login", json={
            "email_or_mobile": "<EMAIL>",
            "password": "Admin@1234",
            "remember_me": True
        })
        assert login_response.status_code == HTTP_STATUS_CODES["OK"]
        token = login_response.json()["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Test subcategories search with pagination
        response = self.client.get("/api/subcategories?skip=0&limit=10", headers=headers)
        assert response.status_code == HTTP_STATUS_CODES["OK"]
        data = response.json()

        # Verify pagination structure
        assert "success" in data
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
        assert "total_count" in data["data"]
        assert "pagination" in data["data"]

        # Verify pagination info structure
        pagination = data["data"]["pagination"]
        assert "current_page" in pagination
        assert "total_pages" in pagination
        assert "items_per_page" in pagination
        assert "total_items" in pagination
        assert pagination["items_per_page"] == 10
        assert pagination["current_page"] >= 1

        # Verify each item has required fields
        for item in data["data"]["items"]:
            assert "id" in item
            assert "name" in item
            assert "is_active" in item
            assert "is_deleted" in item
            assert "category_id" in item
            assert "created_at" in item
            assert "updated_at" in item


class TestAuthenticationEndpoints:
    """Test authentication endpoints"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self, test_db_async):
        """Setup for each test method"""
        self.db = test_db_async
        app.dependency_overrides[get_db] = lambda: test_db_async
        self.client = TestClient(app)
    
    def test_register_user_success(self):
        """Test successful user registration"""
        user_data = TestDataFactory.create_user_data(
            email="<EMAIL>",
            password="TestPassword123!",
            mobile="+1234567890"
        )
        
        response = self.client.post("/api/auth/register", json=user_data)
        
        # Should succeed or return appropriate error
        assert response.status_code in [
            HTTP_STATUS_CODES["CREATED"], 
            HTTP_STATUS_CODES["CONFLICT"],
            HTTP_STATUS_CODES["NOT_FOUND"]  # If endpoint doesn't exist
        ]
    
    def test_login_endpoint_exists(self):
        """Test that login endpoint exists and responds"""
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        response = self.client.post("/api/auth/login", json=login_data)
        
        # Should not return 404 (endpoint should exist)
        assert response.status_code != HTTP_STATUS_CODES["NOT_FOUND"]
    
    def test_protected_endpoint_requires_auth(self):
        """Test that protected endpoints require authentication"""
        response = self.client.get("/api/auth/me")
        
        # Should require authentication
        assert response.status_code in [
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["NOT_FOUND"]
        ]


class TestCategoryEndpoints:
    """Test category endpoints"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self, test_db_async):
        """Setup for each test method"""
        self.db = test_db_async
        app.dependency_overrides[get_db] = lambda: test_db_async
        self.client = TestClient(app)
    
    def test_list_categories_endpoint_exists(self):
        """Test that list categories endpoint exists"""
        response = self.client.get("/api/categories")
        
        # Should not return 404 (endpoint should exist)
        assert response.status_code != HTTP_STATUS_CODES["NOT_FOUND"]
    
    def test_create_category_requires_auth(self):
        """Test that creating category requires authentication"""
        category_data = TestDataFactory.create_category_data()
        
        response = self.client.post("/api/categories", json=category_data)
        
        # Should require authentication
        assert response.status_code in [
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["NOT_FOUND"]
        ]
    
    def test_get_category_by_id_endpoint_exists(self):
        """Test that get category by ID endpoint exists"""
        test_id = "123e4567-e89b-12d3-a456-426614174000"
        response = self.client.get(f"/api/categories/{test_id}")
        
        # Should not return 404 for endpoint (may return 404 for resource)
        assert response.status_code != HTTP_STATUS_CODES["NOT_FOUND"] or "not found" in response.text.lower()


class TestSubcategoryEndpoints:
    """Test subcategory endpoints"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self, test_db_async):
        """Setup for each test method"""
        self.db = test_db_async
        app.dependency_overrides[get_db] = lambda: test_db_async
        self.client = TestClient(app)
    
    def test_list_subcategories_endpoint_exists(self):
        """Test that list subcategories endpoint exists"""
        test_category_id = "123e4567-e89b-12d3-a456-426614174000"
        response = self.client.get(f"/api/categories/{test_category_id}/subcategories")
        
        # Should not return 404 (endpoint should exist)
        assert response.status_code != HTTP_STATUS_CODES["NOT_FOUND"]
    
    def test_create_subcategory_requires_auth(self):
        """Test that creating subcategory requires authentication"""
        test_category_id = "123e4567-e89b-12d3-a456-426614174000"
        subcategory_data = TestDataFactory.create_subcategory_data()
        
        response = self.client.post(
            f"/api/categories/{test_category_id}/subcategories", 
            json=subcategory_data
        )
        
        # Should require authentication
        assert response.status_code in [
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["NOT_FOUND"]
        ]


class TestFranchisorEndpoints:
    """Test franchisor endpoints"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self, test_db_async):
        """Setup for each test method"""
        self.db = test_db_async
        app.dependency_overrides[get_db] = lambda: test_db_async
        self.client = TestClient(app)
    
    def test_list_franchisors_endpoint_exists(self):
        """Test that list franchisors endpoint exists"""
        response = self.client.get("/api/franchisors")
        
        # Should not return 404 (endpoint should exist)
        assert response.status_code != HTTP_STATUS_CODES["NOT_FOUND"]
    
    def test_create_franchisor_requires_auth(self):
        """Test that creating franchisor requires authentication"""
        franchisor_data = TestDataFactory.create_franchisor_data()
        
        response = self.client.post("/api/franchisors", json=franchisor_data)
        
        # Should require authentication
        assert response.status_code in [
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["NOT_FOUND"]
        ]
    
    def test_get_franchisor_by_id_endpoint_exists(self):
        """Test that get franchisor by ID endpoint exists"""
        test_id = "frc_123456789"
        response = self.client.get(f"/api/franchisors/{test_id}")
        
        # Should not return 404 for endpoint (may return 404 for resource)
        assert response.status_code != HTTP_STATUS_CODES["NOT_FOUND"] or "not found" in response.text.lower()

    def test_holiday_endpoints_exist(self):
        """Test that holiday endpoints exist"""
        # Test holiday list endpoint
        response = self.client.get("/api/settings/holidays")
        assert response.status_code in [
            HTTP_STATUS_CODES["OK"],
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["NOT_FOUND"]
        ]

        # Test holiday create endpoint
        response = self.client.post("/api/settings/holidays", json={
            "holiday_type": "PREDEFINED",
            "date": "2024-12-25",
            "all_day": True,
            "description": "Test Holiday"
        })
        assert response.status_code in [
            HTTP_STATUS_CODES["CREATED"],
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["UNPROCESSABLE_ENTITY"]
        ]

    def test_messaging_rule_endpoints_exist(self):
        """Test that messaging rule endpoints exist"""
        # Test messaging rule list endpoint
        response = self.client.get("/api/settings/messaging-rules")
        assert response.status_code in [
            HTTP_STATUS_CODES["OK"],
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["NOT_FOUND"]
        ]

        # Test messaging rule create endpoint
        response = self.client.post("/api/settings/messaging-rules", json={
            "lead_init_delay_h": 2,
            "no_response_delay_h": 24,
            "max_followups": 3
        })
        assert response.status_code in [
            HTTP_STATUS_CODES["CREATED"],
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            HTTP_STATUS_CODES["UNPROCESSABLE_ENTITY"]
        ]

        # Test active messaging rule endpoint
        response = self.client.get("/api/settings/messaging-rules/active")
        assert response.status_code in [
            HTTP_STATUS_CODES["OK"],
            HTTP_STATUS_CODES["NOT_FOUND"],
            HTTP_STATUS_CODES["UNAUTHORIZED"]
        ]

    def test_holidays_pagination_structure(self):
        """Test holidays listing pagination structure matches franchisors API"""
        # First login to get token
        login_response = self.client.post("/api/auth/login", json={
            "email_or_mobile": "<EMAIL>",
            "password": "Admin@1234",
            "remember_me": True
        })
        assert login_response.status_code == HTTP_STATUS_CODES["OK"]
        token = login_response.json()["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # Test holidays listing with pagination
        response = self.client.get("/api/settings/holidays?skip=0&limit=5", headers=headers)
        assert response.status_code == HTTP_STATUS_CODES["OK"]
        data = response.json()

        # Verify pagination structure matches franchisors API exactly
        assert "success" in data
        assert data["success"] is True
        assert "status" in data
        assert data["status"] == "success"
        assert "message" in data
        assert "title" in data["message"]
        assert "description" in data["message"]
        assert "data" in data
        assert "items" in data["data"]
        assert "total_count" in data["data"]
        assert "pagination" in data["data"]

        # Verify pagination info structure
        pagination = data["data"]["pagination"]
        assert "current_page" in pagination
        assert "total_pages" in pagination
        assert "items_per_page" in pagination
        assert "total_items" in pagination
        assert pagination["items_per_page"] == 5
        assert pagination["current_page"] >= 1

        # Verify each holiday item has required fields
        for item in data["data"]["items"]:
            assert "id" in item
            assert "holiday_type" in item
            assert "date" in item
            assert "all_day" in item
            assert "is_active" in item
            assert "is_deleted" in item
            assert "created_at" in item
            assert "updated_at" in item

        # Verify response structure matches franchisors exactly
        assert "metadata" in data
        assert "timestamp" in data["metadata"]


class TestEndpointSecurity:
    """Test endpoint security and validation"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self, test_db_async):
        """Setup for each test method"""
        self.db = test_db_async
        app.dependency_overrides[get_db] = lambda: test_db_async
        self.client = TestClient(app)
    
    def test_invalid_json_handling(self):
        """Test that endpoints handle invalid JSON gracefully"""
        response = self.client.post(
            "/api/categories",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        # Should return 422 for invalid JSON
        assert response.status_code in [
            HTTP_STATUS_CODES["UNPROCESSABLE_ENTITY"],
            HTTP_STATUS_CODES["BAD_REQUEST"],
            HTTP_STATUS_CODES["UNAUTHORIZED"]
        ]
    
    def test_missing_required_fields(self):
        """Test validation of required fields"""
        response = self.client.post("/api/categories", json={})
        
        # Should return validation error
        assert response.status_code in [
            HTTP_STATUS_CODES["UNPROCESSABLE_ENTITY"],
            HTTP_STATUS_CODES["BAD_REQUEST"],
            HTTP_STATUS_CODES["UNAUTHORIZED"]
        ]
    
    def test_sql_injection_protection(self):
        """Test protection against SQL injection"""
        malicious_id = "'; DROP TABLE users; --"
        response = self.client.get(f"/api/categories/{malicious_id}")
        
        # Should handle gracefully, not crash
        assert response.status_code in [
            HTTP_STATUS_CODES["BAD_REQUEST"],
            HTTP_STATUS_CODES["NOT_FOUND"],
            HTTP_STATUS_CODES["UNPROCESSABLE_ENTITY"],
            HTTP_STATUS_CODES["UNAUTHORIZED"]
        ]


class TestErrorHandling:
    """Test error handling across endpoints"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self, test_db_async):
        """Setup for each test method"""
        self.db = test_db_async
        app.dependency_overrides[get_db] = lambda: test_db_async
        self.client = TestClient(app)
    
    def test_404_handling(self):
        """Test 404 error handling"""
        response = self.client.get("/api/nonexistent-endpoint")
        assert response.status_code == HTTP_STATUS_CODES["NOT_FOUND"]
    
    def test_method_not_allowed(self):
        """Test method not allowed handling"""
        response = self.client.patch("/api/categories")  # Assuming PATCH is not allowed
        assert response.status_code in [
            405,  # Method Not Allowed
            HTTP_STATUS_CODES["NOT_FOUND"],
            HTTP_STATUS_CODES["UNAUTHORIZED"]
        ]
    
    def test_large_payload_handling(self):
        """Test handling of large payloads"""
        large_data = {"name": "x" * 10000, "description": "y" * 10000}
        response = self.client.post("/api/categories", json=large_data)
        
        # Should handle gracefully
        assert response.status_code in [
            HTTP_STATUS_CODES["BAD_REQUEST"],
            HTTP_STATUS_CODES["UNPROCESSABLE_ENTITY"],
            HTTP_STATUS_CODES["UNAUTHORIZED"],
            413  # Payload Too Large
        ]


# Test runner function
def run_comprehensive_tests():
    """
    Run comprehensive tests and return results.
    This function can be called programmatically to check endpoint health.
    """
    import subprocess
    import sys
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_comprehensive_endpoints.py", 
            "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=".")
        
        return {
            "success": result.returncode == 0,
            "output": result.stdout,
            "errors": result.stderr,
            "return_code": result.returncode
        }
    except Exception as e:
        return {
            "success": False,
            "output": "",
            "errors": str(e),
            "return_code": -1
        }


if __name__ == "__main__":
    # Allow running this file directly for quick testing
    results = run_comprehensive_tests()
    print("Test Results:")
    print(f"Success: {results['success']}")
    print(f"Return Code: {results['return_code']}")
    if results['output']:
        print("Output:")
        print(results['output'])
    if results['errors']:
        print("Errors:")
        print(results['errors'])
