"""
Tests for core models and data structures.

This module tests the Pydantic models used throughout the system
to ensure proper validation and serialization.
"""

import pytest
from datetime import datetime

import numpy as np

from ingest.core import (
    ChartCaption,
    ChartType,
    DocumentChunk,
    FileType,
    IngestionConfig,
    LanguageCode,
    ProcessingResult,
    VectorEntry,
)


class TestChartCaption:
    """Test the ChartCaption model."""
    
    def test_create_basic_chart_caption(self):
        """Test creating a basic chart caption."""
        caption = ChartCaption(
            description="A bar chart showing sales data",
        )
        
        assert caption.description == "A bar chart showing sales data"
        assert caption.chart_type == ChartType.UNKNOWN
        assert caption.data_insights == []
        assert caption.confidence_score == 0.0
        assert caption.extraction_method == "unknown"
    
    def test_create_detailed_chart_caption(self):
        """Test creating a detailed chart caption."""
        caption = ChartCaption(
            chart_type=ChartType.BAR,
            description="Quarterly sales performance showing 20% growth",
            data_insights=["Q4 shows highest sales", "Consistent upward trend"],
            confidence_score=0.85,
            extraction_method="gpt-4o",
        )
        
        assert caption.chart_type == ChartType.BAR
        assert caption.description == "Quarterly sales performance showing 20% growth"
        assert len(caption.data_insights) == 2
        assert caption.confidence_score == 0.85
        assert caption.extraction_method == "gpt-4o"
    
    def test_confidence_score_validation(self):
        """Test confidence score validation."""
        # Valid confidence scores
        ChartCaption(description="test", confidence_score=0.0)
        ChartCaption(description="test", confidence_score=0.5)
        ChartCaption(description="test", confidence_score=1.0)
        
        # Invalid confidence scores should be clamped or raise validation error
        with pytest.raises(ValueError):
            ChartCaption(description="test", confidence_score=-0.1)
        
        with pytest.raises(ValueError):
            ChartCaption(description="test", confidence_score=1.1)
    
    def test_description_validation(self):
        """Test description validation."""
        # Valid descriptions
        ChartCaption(description="Valid description")
        
        # Empty description should fail
        with pytest.raises(ValueError):
            ChartCaption(description="")
        
        # Very long description should fail
        with pytest.raises(ValueError):
            ChartCaption(description="x" * 2001)


class TestDocumentChunk:
    """Test the DocumentChunk model."""
    
    def test_create_basic_document_chunk(self):
        """Test creating a basic document chunk."""
        chunk = DocumentChunk(
            text="This is a sample text chunk.",
            source="test.txt",
        )
        
        assert chunk.text == "This is a sample text chunk."
        assert chunk.source == "test.txt"
        assert chunk.file_type == FileType.UNKNOWN
        assert chunk.language == LanguageCode.UNKNOWN
        assert chunk.chunk_index == 0
        assert chunk.token_count == 0
        assert len(chunk.chart_captions) == 0
        assert isinstance(chunk.id, str)
        assert isinstance(chunk.created_at, datetime)
    
    def test_create_detailed_document_chunk(self):
        """Test creating a detailed document chunk."""
        chart_caption = ChartCaption(
            description="Sample chart",
            chart_type=ChartType.PIE,
        )
        
        chunk = DocumentChunk(
            text="Detailed text content with charts and data.",
            source="report.pdf",
            file_type=FileType.PDF,
            language=LanguageCode.ENGLISH,
            start_page=5,
            end_page=5,
            chunk_index=3,
            token_count=150,
            chart_captions=[chart_caption],
            metadata={"section": "analysis", "confidence": 0.9},
        )
        
        assert chunk.text == "Detailed text content with charts and data."
        assert chunk.source == "report.pdf"
        assert chunk.file_type == FileType.PDF
        assert chunk.language == LanguageCode.ENGLISH
        assert chunk.start_page == 5
        assert chunk.end_page == 5
        assert chunk.chunk_index == 3
        assert chunk.token_count == 150
        assert len(chunk.chart_captions) == 1
        assert chunk.chart_captions[0].chart_type == ChartType.PIE
        assert chunk.metadata["section"] == "analysis"
    
    def test_page_range_validation(self):
        """Test page range validation."""
        # Valid page ranges
        DocumentChunk(text="test", source="test.txt", start_page=1, end_page=1)
        DocumentChunk(text="test", source="test.txt", start_page=1, end_page=5)
        DocumentChunk(text="test", source="test.txt", start_page=None, end_page=None)
        
        # Invalid page range should fail
        with pytest.raises(ValueError):
            DocumentChunk(text="test", source="test.txt", start_page=5, end_page=1)
    
    def test_text_validation(self):
        """Test text content validation."""
        # Valid text
        DocumentChunk(text="Valid text content", source="test.txt")
        
        # Empty text should fail
        with pytest.raises(ValueError):
            DocumentChunk(text="", source="test.txt")
    
    def test_source_validation(self):
        """Test source validation."""
        # Valid source
        DocumentChunk(text="test", source="valid_source.txt")
        
        # Empty source should fail
        with pytest.raises(ValueError):
            DocumentChunk(text="test", source="")


class TestVectorEntry:
    """Test the VectorEntry model."""
    
    def test_create_vector_entry(self):
        """Test creating a vector entry."""
        vector = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        
        entry = VectorEntry(
            vector=vector,
            chunk_id="chunk-123",
            source_file="document.pdf",
            page_number=1,
            language=LanguageCode.ENGLISH,
            chart_caption="Sample chart description",
            metadata={"confidence": 0.8},
        )
        
        assert np.array_equal(entry.vector, vector)
        assert entry.chunk_id == "chunk-123"
        assert entry.source_file == "document.pdf"
        assert entry.page_number == 1
        assert entry.language == LanguageCode.ENGLISH
        assert entry.chart_caption == "Sample chart description"
        assert entry.metadata["confidence"] == 0.8
        assert isinstance(entry.id, str)
        assert isinstance(entry.created_at, datetime)
    
    def test_vector_entry_serialization(self):
        """Test vector entry JSON serialization."""
        vector = np.array([0.1, 0.2, 0.3])
        
        entry = VectorEntry(
            vector=vector,
            chunk_id="chunk-123",
            source_file="test.txt",
        )
        
        # Test that it can be serialized to dict
        entry_dict = entry.dict()
        assert "vector" in entry_dict
        assert "chunk_id" in entry_dict
        assert "created_at" in entry_dict
        
        # Vector should be converted to list for JSON serialization
        assert isinstance(entry_dict["vector"], list)


class TestProcessingResult:
    """Test the ProcessingResult model."""
    
    def test_create_successful_result(self):
        """Test creating a successful processing result."""
        chunk = DocumentChunk(text="test", source="test.txt")
        
        result = ProcessingResult(
            success=True,
            file_path="test.txt",
            file_type=FileType.TXT,
            chunks=[chunk],
            processing_time=1.5,
            charts_extracted=2,
            pages_processed=1,
        )
        
        assert result.success is True
        assert result.file_path == "test.txt"
        assert result.file_type == FileType.TXT
        assert len(result.chunks) == 1
        assert result.processing_time == 1.5
        assert result.charts_extracted == 2
        assert result.pages_processed == 1
        assert result.error_message is None
    
    def test_create_failed_result(self):
        """Test creating a failed processing result."""
        result = ProcessingResult(
            success=False,
            file_path="broken.pdf",
            file_type=FileType.PDF,
            error_message="File is corrupted",
            processing_time=0.5,
        )
        
        assert result.success is False
        assert result.file_path == "broken.pdf"
        assert result.file_type == FileType.PDF
        assert len(result.chunks) == 0
        assert result.error_message == "File is corrupted"
        assert result.processing_time == 0.5
        assert result.charts_extracted == 0
        assert result.pages_processed == 0


class TestIngestionConfig:
    """Test the IngestionConfig model."""
    
    def test_create_default_config(self):
        """Test creating a default configuration."""
        config = IngestionConfig()
        
        assert config.extract_charts is True
        assert config.translate is False
        assert config.target_language == LanguageCode.ENGLISH
        assert config.chunk_size == 400
        assert config.chunk_overlap == 50
        assert config.max_file_size_mb == 100
        assert config.timeout_seconds == 30
        assert config.concurrent_workers == 4
    
    def test_create_custom_config(self):
        """Test creating a custom configuration."""
        config = IngestionConfig(
            extract_charts=False,
            translate=True,
            target_language=LanguageCode.SPANISH,
            chunk_size=200,
            chunk_overlap=25,
            max_file_size_mb=50,
            timeout_seconds=60,
            concurrent_workers=8,
        )
        
        assert config.extract_charts is False
        assert config.translate is True
        assert config.target_language == LanguageCode.SPANISH
        assert config.chunk_size == 200
        assert config.chunk_overlap == 25
        assert config.max_file_size_mb == 50
        assert config.timeout_seconds == 60
        assert config.concurrent_workers == 8
    
    def test_chunk_overlap_validation(self):
        """Test chunk overlap validation."""
        # Valid configurations
        IngestionConfig(chunk_size=400, chunk_overlap=50)
        IngestionConfig(chunk_size=100, chunk_overlap=99)
        
        # Invalid configuration (overlap >= chunk_size)
        with pytest.raises(ValueError):
            IngestionConfig(chunk_size=100, chunk_overlap=100)
        
        with pytest.raises(ValueError):
            IngestionConfig(chunk_size=100, chunk_overlap=150)
    
    def test_range_validations(self):
        """Test various range validations."""
        # Valid ranges
        IngestionConfig(chunk_size=50, chunk_overlap=0)
        IngestionConfig(chunk_size=2000, chunk_overlap=500)
        IngestionConfig(max_file_size_mb=1, timeout_seconds=5, concurrent_workers=1)
        IngestionConfig(max_file_size_mb=1000, timeout_seconds=300, concurrent_workers=16)
        
        # Invalid ranges
        with pytest.raises(ValueError):
            IngestionConfig(chunk_size=49)  # Below minimum
        
        with pytest.raises(ValueError):
            IngestionConfig(chunk_size=2001)  # Above maximum
        
        with pytest.raises(ValueError):
            IngestionConfig(max_file_size_mb=0)  # Below minimum
        
        with pytest.raises(ValueError):
            IngestionConfig(timeout_seconds=4)  # Below minimum
        
        with pytest.raises(ValueError):
            IngestionConfig(concurrent_workers=0)  # Below minimum
