#!/usr/bin/env python3
"""
Test Script for RAG System Questions

This script tests the enhanced RAG system with the questions from the guide
to demonstrate the dynamic franchisor detection and answer generation capabilities.
"""

import requests
import time
from typing import List, Dict, Any


class RAGQuestionTester:
    """Test the RAG system with various question types"""
    
    def __init__(self, base_url: str = "http://localhost:8000", auth_token: str = None):
        self.base_url = base_url
        self.auth_token = auth_token
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}" if auth_token else ""
        }
    
    def test_webhook_question(self, question: str, description: str = "") -> Dict[str, Any]:
        """Test a question through the webhook endpoint"""
        print(f"\n{'='*80}")
        print(f"🤔 TESTING: {description}")
        print(f"❓ Question: {question}")
        print(f"{'='*80}")
        
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": "2025-07-10T16:30:00Z",
            "mo": {
                "id": f"test-msg-{int(time.time())}",
                "sender": "61430250079",
                "recipient": "61430250080",
                "message": question
            }
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/webhooks/webhooks/kudosity",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                print("✅ SUCCESS - Question processed successfully")
                # The actual answer will be in the server logs
                return {"success": True, "status_code": 200}
            else:
                print(f"❌ FAILED - Status: {response.status_code}")
                print(f"Response: {response.text}")
                return {"success": False, "status_code": response.status_code, "error": response.text}
                
        except Exception as e:
            print(f"❌ ERROR - {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_direct_api_question(self, question: str, description: str = "") -> Dict[str, Any]:
        """Test a question through the direct API endpoint (if available)"""
        print(f"\n{'='*80}")
        print(f"🤔 TESTING (Direct API): {description}")
        print(f"❓ Question: {question}")
        print(f"{'='*80}")
        
        # This would be for a direct QnA API endpoint
        # Implementation depends on available endpoints
        print("ℹ️  Direct API testing not implemented - use webhook method")
        return {"success": False, "error": "Direct API not available"}
    
    def run_coochie_hydrogreen_tests(self):
        """Run tests specifically for Coochie Hydrogreen questions"""
        print("\n🌱 COOCHIE HYDROGREEN FRANCHISE QUESTIONS")
        print("="*80)
        
        coochie_questions = [
            {
                "question": "What are the investment requirements for Coochie Hydrogreen franchise?",
                "description": "Investment Requirements - Direct Mention",
                "category": "Financial"
            },
            {
                "question": "I want to learn about Coochie Hydrogreen franchise opportunities and investment requirements",
                "description": "Franchise Opportunities - Natural Language",
                "category": "General"
            },
            {
                "question": "How much does it cost to start a Coochie Hydrogreen franchise?",
                "description": "Startup Costs - Direct Question",
                "category": "Financial"
            },
            {
                "question": "What percentage of revenue do I pay as royalties to Coochie Hydrogreen?",
                "description": "Royalty Structure - Specific Query",
                "category": "Financial"
            },
            {
                "question": "What kind of support does Coochie Hydrogreen provide to franchisees?",
                "description": "Support Services - Business Model",
                "category": "Support"
            },
            {
                "question": "Is Coochie Hydrogreen an eco-friendly business?",
                "description": "Environmental Focus - Brand Positioning",
                "category": "Business Model"
            },
            {
                "question": "What is the franchisee approval process for Coochie Hydrogreen?",
                "description": "Application Process - Procedural",
                "category": "Application"
            }
        ]
        
        results = []
        for test_case in coochie_questions:
            result = self.test_webhook_question(
                test_case["question"], 
                test_case["description"]
            )
            result.update(test_case)
            results.append(result)
            time.sleep(2)  # Brief pause between requests
        
        return results
    
    def run_contextual_detection_tests(self):
        """Run tests for contextual franchisor detection"""
        print("\n🔍 CONTEXTUAL DETECTION TESTS")
        print("="*80)
        
        contextual_questions = [
            {
                "question": "Tell me about eco-friendly car wash franchises in Australia",
                "description": "Contextual Detection - Eco-friendly + Car Wash + Australia",
                "expected_detection": "Coochie Hydrogreen"
            },
            {
                "question": "I'm looking for waterless car cleaning business opportunities",
                "description": "Service-Specific Keywords - Waterless Cleaning",
                "expected_detection": "Coochie Hydrogreen"
            },
            {
                "question": "What automotive franchise opportunities are available?",
                "description": "Industry Category - Automotive",
                "expected_detection": "Multiple or Coochie Hydrogreen"
            },
            {
                "question": "Which franchises don't require volatile tenancy fees?",
                "description": "Business Model Feature - No Tenancy Fees",
                "expected_detection": "Coochie Hydrogreen"
            }
        ]
        
        results = []
        for test_case in contextual_questions:
            result = self.test_webhook_question(
                test_case["question"], 
                test_case["description"]
            )
            result.update(test_case)
            results.append(result)
            time.sleep(2)
        
        return results
    
    def run_general_franchise_tests(self):
        """Run tests for general franchise questions"""
        print("\n🏢 GENERAL FRANCHISE QUESTIONS")
        print("="*80)
        
        general_questions = [
            {
                "question": "What franchise opportunities are available?",
                "description": "General Inquiry - All Franchises",
                "category": "General"
            },
            {
                "question": "Which franchises operate in Australia?",
                "description": "Location-Based Query - Australia",
                "category": "Location"
            },
            {
                "question": "What are the different franchise categories available?",
                "description": "Category Overview - All Industries",
                "category": "Categories"
            },
            {
                "question": "Which franchises have the lowest startup costs?",
                "description": "Comparative Query - Cost Comparison",
                "category": "Comparison"
            }
        ]
        
        results = []
        for test_case in general_questions:
            result = self.test_webhook_question(
                test_case["question"], 
                test_case["description"]
            )
            result.update(test_case)
            results.append(result)
            time.sleep(2)
        
        return results
    
    def run_edge_case_tests(self):
        """Run tests for edge cases and system limits"""
        print("\n⚠️  EDGE CASE TESTS")
        print("="*80)
        
        edge_cases = [
            {
                "question": "What's the weather like today?",
                "description": "Non-Franchise Question - Should Return General Response",
                "expected": "No franchisor detection"
            },
            {
                "question": "Tell me about XYZ Franchise that doesn't exist",
                "description": "Non-Existent Franchise - Error Handling",
                "expected": "No detection or general response"
            },
            {
                "question": "Coochie Hydrogreen pizza restaurant opportunities",
                "description": "Mixed Context - Correct Brand, Wrong Industry",
                "expected": "Should detect Coochie Hydrogreen, clarify services"
            }
        ]
        
        results = []
        for test_case in edge_cases:
            result = self.test_webhook_question(
                test_case["question"], 
                test_case["description"]
            )
            result.update(test_case)
            results.append(result)
            time.sleep(2)
        
        return results
    
    def generate_summary_report(self, all_results: List[Dict[str, Any]]):
        """Generate a summary report of all test results"""
        print("\n📊 TEST SUMMARY REPORT")
        print("="*80)
        
        total_tests = len(all_results)
        successful_tests = sum(1 for r in all_results if r.get("success", False))
        failed_tests = total_tests - successful_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in all_results:
                if not result.get("success", False):
                    print(f"  - {result.get('description', 'Unknown')}")
                    print(f"    Question: {result.get('question', 'N/A')}")
                    print(f"    Error: {result.get('error', 'Unknown error')}")
        
        print("\n✅ Test Categories Covered:")
        categories = set()
        for result in all_results:
            if result.get("category"):
                categories.add(result["category"])
        
        for category in sorted(categories):
            category_tests = [r for r in all_results if r.get("category") == category]
            category_success = sum(1 for r in category_tests if r.get("success", False))
            print(f"  - {category}: {category_success}/{len(category_tests)} successful")


def main():
    """Main test execution"""
    print("🚀 RAG SYSTEM QUESTION TESTING")
    print("="*80)
    print("This script tests the enhanced RAG system with dynamic franchisor detection")
    print("Make sure the server is running on http://localhost:8000")
    print("="*80)
    
    # Initialize tester with auth token
    auth_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************.nZoPU-4srcP-LM4ELoVRmg_e5VMAS_SZ9CG6SYNUvRk"
    
    tester = RAGQuestionTester(auth_token=auth_token)
    
    all_results = []
    
    # Run different test suites
    print("\n🧪 Starting Test Suites...")
    
    # 1. Coochie Hydrogreen specific tests
    coochie_results = tester.run_coochie_hydrogreen_tests()
    all_results.extend(coochie_results)
    
    # 2. Contextual detection tests
    contextual_results = tester.run_contextual_detection_tests()
    all_results.extend(contextual_results)
    
    # 3. General franchise tests
    general_results = tester.run_general_franchise_tests()
    all_results.extend(general_results)
    
    # 4. Edge case tests
    edge_results = tester.run_edge_case_tests()
    all_results.extend(edge_results)
    
    # Generate summary
    tester.generate_summary_report(all_results)
    
    print("\n🎉 Testing Complete!")
    print("Check the server logs to see the actual AI responses and franchisor detection results.")
    print("The webhook responses show in the server console with detailed detection information.")


if __name__ == "__main__":
    main()
