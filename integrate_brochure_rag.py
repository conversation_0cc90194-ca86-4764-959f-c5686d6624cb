#!/usr/bin/env python3
"""
Integrate Brochure-Optimized RAG System into Existing Codebase
Updates all components to use brochure-specific processing
"""

import os
import sys
import shutil
from pathlib import Path
import asyncio

# Add the project root to the path
sys.path.append('.')

def update_production_integration():
    """Update production integration to use brochure-optimized components"""
    print("\n🔄 Updating Production Integration for Brochures...")
    
    target_file = "docqa/production_integration.py"
    
    try:
        # Create backup
        if os.path.exists(target_file):
            shutil.copy2(target_file, f"{target_file}.bak")
            print(f"✅ Created backup: {target_file}.bak")
        
        # Create brochure-optimized integration
        with open(target_file, 'w') as f:
            f.write("""\"\"\"
Brochure-Optimized RAG Integration Module
Provides easy access to all brochure-optimized components
\"\"\"

import structlog
from typing import Dict, List, Any, Optional

# Import brochure-optimized components
from ..brochure_rag_system import (
    BrochureTextNormalizer,
    BrochureMetadataExtractor,
    BrochureChunker,
    BrochureSection,
    BrochureMetadata,
    DocumentChunk
)
from ..brochure_qa_system import BrochureQASystem
from .production_embeddings import ProductionEmbeddingService
from .production_vector_store import ProductionVectorStore, RetrievalResult

logger = structlog.get_logger()

class BrochureRAGSystem:
    \"\"\"
    Brochure-Optimized RAG System Integration
    
    This class provides a unified interface to all brochure-optimized components:
    - Brochure-specific text normalization and section extraction
    - Metadata extraction for company information
    - Optimized chunking for marketing content
    - Enhanced question answering for brochure queries
    
    Usage:
        rag_system = BrochureRAGSystem()
        
        # Process brochure
        sections, metadata = rag_system.extract_brochure_structure(text)
        chunks = rag_system.process_brochure(text, sections, metadata)
        
        # Store brochure
        rag_system.store_brochure(franchisor_id, text, chunks, metadata)
        
        # Answer questions
        answer = await rag_system.answer_brochure_question(question, franchisor_id)
    \"\"\"
    
    def __init__(self):
        self.text_normalizer = BrochureTextNormalizer()
        self.metadata_extractor = BrochureMetadataExtractor()
        self.chunker = BrochureChunker(chunk_size=350, chunk_overlap=50)
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        self.qa_system = BrochureQASystem()
        
        logger.info("Brochure RAG System initialized")
    
    def extract_brochure_structure(self, text: str) -> tuple[List[BrochureSection], BrochureMetadata]:
        \"\"\"
        Extract structured information from brochure text
        
        Args:
            text: Raw brochure text
            
        Returns:
            Tuple of (sections, metadata)
        \"\"\"
        # Normalize text
        normalized_text = self.text_normalizer.normalize_brochure_text(text)
        
        # Extract sections
        sections = self.text_normalizer.extract_sections(normalized_text)
        
        # Extract metadata
        metadata = self.metadata_extractor.extract_metadata(normalized_text, sections)
        
        return sections, metadata
    
    def process_brochure(
        self, 
        text: str, 
        sections: List[BrochureSection] = None,
        metadata: BrochureMetadata = None
    ) -> List[DocumentChunk]:
        \"\"\"
        Process brochure text into optimized chunks
        
        Args:
            text: Brochure text content
            sections: Optional pre-extracted sections
            metadata: Optional pre-extracted metadata
            
        Returns:
            List of document chunks with embeddings
        \"\"\"
        # Extract structure if not provided
        if sections is None or metadata is None:
            sections, metadata = self.extract_brochure_structure(text)
        
        # Create chunks
        chunks = self.chunker.chunk_brochure(text, sections, metadata)
        
        # Generate embeddings
        for chunk in chunks:
            chunk.embedding = self.embedding_service.generate_embedding(chunk.text)
        
        return chunks
    
    def store_brochure(
        self,
        franchisor_id: str,
        text: str,
        chunks: List[DocumentChunk] = None,
        metadata: BrochureMetadata = None
    ) -> bool:
        \"\"\"
        Store brochure in vector store with enhanced metadata
        
        Args:
            franchisor_id: Franchisor ID
            text: Full brochure text
            chunks: Optional pre-processed chunks
            metadata: Optional brochure metadata
            
        Returns:
            True if successful
        \"\"\"
        # Process brochure if chunks not provided
        if chunks is None:
            sections, extracted_metadata = self.extract_brochure_structure(text)
            chunks = self.process_brochure(text, sections, extracted_metadata)
            if metadata is None:
                metadata = extracted_metadata
        
        # Create comprehensive brochure summary
        brochure_summary = self._create_brochure_summary(text, metadata)
        
        # Generate embedding for full brochure
        embedding = self.embedding_service.generate_embedding(brochure_summary)
        
        # Enhanced metadata for storage
        storage_metadata = {
            'content_type': 'company_brochure',
            'company_name': metadata.company_name if metadata else None,
            'industry': metadata.industry if metadata else None,
            'location': metadata.location if metadata else None,
            'services_count': len(metadata.services) if metadata and metadata.services else 0,
            'contact_info': metadata.contact_info if metadata else {},
            'chunk_count': len(chunks),
            'text_length': len(text)
        }
        
        # Store in vector store
        return self.vector_store.store_franchisor_embedding(
            franchisor_id=franchisor_id,
            text_content=brochure_summary,
            embedding=embedding,
            metadata=storage_metadata
        )
    
    async def answer_brochure_question(
        self,
        question: str,
        franchisor_id: Optional[str] = None,
        similarity_threshold: float = 0.4,  # Lower for brochures
        top_k: int = 5,
        temperature: float = 0.2  # Balanced for marketing content
    ) -> Dict[str, Any]:
        \"\"\"
        Answer question using brochure-optimized QA
        
        Args:
            question: User question about the brochure
            franchisor_id: Optional franchisor filter
            similarity_threshold: Lower threshold for brochure content
            top_k: Number of chunks to retrieve
            temperature: Generation temperature
            
        Returns:
            Dict with answer, sources, and metadata
        \"\"\"
        return await self.qa_system.answer_brochure_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )
    
    def _create_brochure_summary(self, text: str, metadata: BrochureMetadata) -> str:
        \"\"\"Create comprehensive summary for main embedding\"\"\"
        summary_parts = []
        
        # Company information
        if metadata and metadata.company_name:
            summary_parts.append(f"Company: {metadata.company_name}")
        
        if metadata and metadata.industry:
            summary_parts.append(f"Industry: {metadata.industry}")
        
        if metadata and metadata.location:
            summary_parts.append(f"Location: {metadata.location}")
        
        # Services
        if metadata and metadata.services:
            services_text = "Services: " + ", ".join(metadata.services[:5])
            summary_parts.append(services_text)
        
        # Contact information
        if metadata and metadata.contact_info:
            contact_parts = []
            if metadata.phone:
                contact_parts.append(f"Phone: {metadata.phone}")
            if metadata.email:
                contact_parts.append(f"Email: {metadata.email}")
            if metadata.website:
                contact_parts.append(f"Website: {metadata.website}")
            
            if contact_parts:
                summary_parts.append("Contact: " + ", ".join(contact_parts))
        
        # Add normalized text preview
        normalized_text = self.text_normalizer.normalize_brochure_text(text)
        text_preview = normalized_text[:800] + "..." if len(normalized_text) > 800 else normalized_text
        summary_parts.append(f"Content: {text_preview}")
        
        return "\\n".join(summary_parts)

# Create singleton instance
brochure_rag = BrochureRAGSystem()
\"\"\"
Singleton instance of BrochureRAGSystem for easy import
\"\"\"

# Backward compatibility aliases
production_rag = brochure_rag  # Alias for existing code
ProductionRAGSystem = BrochureRAGSystem  # Class alias
""")
        
        print(f"✅ Updated {target_file} with brochure optimization")
        return True
    
    except Exception as e:
        print(f"❌ Failed to update production integration: {e}")
        return False

def update_serve_api():
    """Update serve.py to use brochure-optimized functions"""
    print("\n🔄 Updating Serve API for Brochures...")
    
    target_file = "docqa/serve.py"
    
    try:
        # Read current content
        with open(target_file, 'r') as f:
            content = f.read()
        
        # Create backup
        shutil.copy2(target_file, f"{target_file}.bak")
        print(f"✅ Created backup: {target_file}.bak")
        
        # Add brochure import
        if "from .brochure_qa_system import BrochureQASystem" not in content:
            # Find import section
            import_lines = []
            other_lines = []
            in_imports = True
            
            for line in content.split('\\n'):
                if in_imports and (line.startswith('from ') or line.startswith('import ') or line.strip() == ''):
                    import_lines.append(line)
                else:
                    in_imports = False
                    other_lines.append(line)
            
            # Add brochure import
            import_lines.append("from .brochure_qa_system import BrochureQASystem")
            
            # Reconstruct content
            content = '\\n'.join(import_lines + other_lines)
        
        # Add brochure ask function
        brochure_function = '''
async def ask_brochure_question(question: str, **kwargs) -> str:
    """
    Brochure-optimized question answering for company brochures.
    
    This function uses brochure-specific processing with:
    - Section-aware chunking for marketing content
    - Lower similarity thresholds for brochure content
    - Enhanced prompts for company information
    - Metadata extraction for contact details
    
    Args:
        question: The question about the company brochure
        **kwargs: Additional parameters:
            - franchisor_id: Optional franchisor filter
            - top_k: Number of results to retrieve (default: 5)
            - similarity_threshold: Minimum similarity score (default: 0.4)
            - temperature: Generation temperature (default: 0.2)
            - max_tokens: Maximum response tokens (default: 800)
            - format: Response format ('text' or 'json', default: 'text')
    
    Returns:
        String answer about the company brochure
        
    Example:
        >>> answer = await ask_brochure_question("What services does the company provide?")
        >>> print(answer)
        "The company provides lawn care and maintenance services..."
    """
    try:
        # Validate input
        if not question or not question.strip():
            return "Please provide a valid question about the company brochure."
        
        # Extract parameters with brochure-optimized defaults
        franchisor_id = kwargs.get('franchisor_id')
        top_k = kwargs.get('top_k', 5)
        similarity_threshold = kwargs.get('similarity_threshold', 0.4)  # Lower for brochures
        temperature = kwargs.get('temperature', 0.2)  # Balanced for marketing
        max_tokens = kwargs.get('max_tokens', 800)
        response_format = kwargs.get('format', 'text')
        
        # Use brochure QA system
        from .production_integration import brochure_rag
        
        result = await brochure_rag.answer_brochure_question(
            question=question.strip(),
            franchisor_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )
        
        if not result['success']:
            return f"Error: {result.get('error', 'Unknown error occurred')}"
        
        # Format response
        if response_format == 'json':
            import json
            return json.dumps(result, indent=2, default=str)
        else:
            answer = result['answer']
            
            # Add source information if requested
            if kwargs.get('include_metadata', False) and result['sources']:
                answer += "\\n\\n--- Sources ---"
                for i, source in enumerate(result['sources'], 1):
                    score = source['similarity_score']
                    text_preview = source['text'][:100] + "..." if len(source['text']) > 100 else source['text']
                    answer += f"\\n{i}. Score: {score:.3f} - {text_preview}"
            
            return answer
            
    except Exception as e:
        logger.error("Brochure question processing failed", 
                    question=question[:100],
                    error=str(e))
        return f"Error processing brochure question: {str(e)}"

'''
        
        # Add the function before the convenience aliases
        if "ask_brochure_question" not in content:
            # Find the convenience aliases section
            aliases_index = content.find("# Convenience aliases for external use")
            if aliases_index != -1:
                content = content[:aliases_index] + brochure_function + "\\n" + content[aliases_index:]
            else:
                # Add at the end
                content += brochure_function
        
        # Update convenience aliases
        if "ask_brochure = ask_brochure_question" not in content:
            content = content.replace(
                "# Convenience aliases for external use\\nask = ask_question",
                "# Convenience aliases for external use\\nask = ask_question\\nask_brochure = ask_brochure_question"
            )
        
        # Write updated content
        with open(target_file, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated {target_file} with brochure functions")
        return True
    
    except Exception as e:
        print(f"❌ Failed to update serve API: {e}")
        return False

def update_main_init():
    """Update main __init__.py to expose brochure functions"""
    print("\n🔄 Updating Main __init__.py...")
    
    target_file = "docqa/__init__.py"
    
    try:
        # Read current content
        with open(target_file, 'r') as f:
            content = f.read()
        
        # Create backup
        shutil.copy2(target_file, f"{target_file}.bak")
        print(f"✅ Created backup: {target_file}.bak")
        
        # Update imports and exports
        content = content.replace(
            'from .serve import ask_question, ask_question_production',
            'from .serve import ask_question, ask_question_production, ask_brochure_question'
        )
        
        content = content.replace(
            '__all__ = ["ask_question", "ask_question_production"]',
            '__all__ = ["ask_question", "ask_question_production", "ask_brochure_question"]'
        )
        
        # Write updated content
        with open(target_file, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated {target_file}")
        return True
    
    except Exception as e:
        print(f"❌ Failed to update main __init__.py: {e}")
        return False

def update_cli():
    """Update CLI to include brochure commands"""
    print("\n🔄 Updating CLI for Brochures...")
    
    target_file = "docqa.py"
    
    try:
        # Read current content
        with open(target_file, 'r') as f:
            content = f.read()
        
        # Create backup
        shutil.copy2(target_file, f"{target_file}.bak")
        print(f"✅ Created backup: {target_file}.bak")
        
        # Add brochure command description
        content = content.replace(
            '    # Ask questions (production-grade system)\\n    python docqa.py ask-production "What is Coochie Hydrogreen?"',
            '''    # Ask questions (production-grade system)
    python docqa.py ask-production "What is Coochie Hydrogreen?"
    
    # Ask questions (brochure-optimized system)
    python docqa.py ask-brochure "What services does the company provide?"'''
        )
        
        # Add brochure command
        brochure_command = '''
@app.command("ask-brochure")
def ask_brochure(
    question: str = typer.Argument(..., help="Question about company brochure"),
    franchisor_id: str = typer.Option(None, "--franchisor-id", "-f", help="Optional franchisor ID filter"),
    top_k: int = typer.Option(5, "--top-k", "-k", help="Number of top results to retrieve"),
    threshold: float = typer.Option(0.4, "--threshold", "-t", help="Similarity threshold (lower for brochures)"),
    temperature: float = typer.Option(0.2, "--temperature", help="Generation temperature (0.0-1.0)"),
    context: bool = typer.Option(True, "--context/--no-context", help="Include context information"),
    json_output: bool = typer.Option(False, "--json", help="Output in JSON format")
):
    """Ask a question using brochure-optimized RAG system"""
    
    console.print(f"[bold cyan]Brochure Question:[/bold cyan] {question}")
    if franchisor_id:
        console.print(f"[dim]Franchisor Filter: {franchisor_id}[/dim]")
    console.print()

    try:
        import asyncio
        from docqa.serve import ask_brochure_question

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Processing with brochure RAG...", total=None)

            # Run the async function
            response = asyncio.run(ask_brochure_question(
                question=question,
                franchisor_id=franchisor_id,
                top_k=top_k,
                similarity_threshold=threshold,
                temperature=temperature,
                include_metadata=context,
                format='json' if json_output else 'text'
            ))

            progress.update(task, description="Brochure question processed!")

        if json_output:
            console.print(response)
        else:
            # Parse the response if it's JSON
            try:
                import json
                parsed_response = json.loads(response)
                
                # Display answer
                answer = parsed_response.get('answer', response)
                console.print(Panel(answer, title="Brochure Answer", border_style="cyan"))
                
                # Display metadata
                if context and 'metadata' in parsed_response:
                    metadata = parsed_response['metadata']
                    console.print(f"\\n[dim]Processing Time: {metadata.get('processing_time', 0):.2f}s[/dim]")
                    console.print(f"[dim]Model: {metadata.get('model_used', 'Unknown')}[/dim]")
                    console.print(f"[dim]Chunks Found: {metadata.get('chunks_found', 0)}[/dim]")
                    console.print(f"[dim]Similarity Threshold: {metadata.get('similarity_threshold_used', 0):.2f}[/dim]")
                    console.print(f"[dim]Question Type: {metadata.get('question_type', 'Unknown')}[/dim]")
                
                # Display sources
                if context and 'sources' in parsed_response:
                    sources = parsed_response['sources']
                    if sources:
                        console.print("\\n[bold]Brochure Sources:[/bold]")
                        for i, source in enumerate(sources[:3], 1):
                            score = source.get('similarity_score', 0)
                            text_preview = source.get('text', '')[:150] + "..."
                            console.print(f"[dim]Source {i} (Score: {score:.3f}):[/dim] {text_preview}")
                
            except json.JSONDecodeError:
                # If not JSON, display as plain text
                console.print(Panel(response, title="Brochure Answer", border_style="cyan"))

    except Exception as e:
        console.print(f"[red]Error:[/red] {str(e)}")
        raise typer.Exit(1)
'''
        
        # Add the command after the existing ask-production command
        if "@app.command(\"ask-brochure\")" not in content:
            # Find the end of ask-production command
            production_end = content.find("raise typer.Exit(1)", content.find("@app.command(\"ask-production\")"))
            if production_end != -1:
                # Find the end of the function
                next_function = content.find("@app.command", production_end + 1)
                if next_function != -1:
                    content = content[:next_function] + brochure_command + "\\n" + content[next_function:]
                else:
                    content += brochure_command
        
        # Write updated content
        with open(target_file, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated {target_file} with brochure command")
        return True
    
    except Exception as e:
        print(f"❌ Failed to update CLI: {e}")
        return False

def copy_brochure_files():
    """Copy brochure system files to docqa directory"""
    print("\n🔄 Copying Brochure System Files...")
    
    try:
        # Copy brochure_rag_system.py
        shutil.copy2("brochure_rag_system.py", "docqa/brochure_rag_system.py")
        print("✅ Copied brochure_rag_system.py to docqa/")
        
        # Copy brochure_qa_system.py
        shutil.copy2("brochure_qa_system.py", "docqa/brochure_qa_system.py")
        print("✅ Copied brochure_qa_system.py to docqa/")
        
        return True
    
    except Exception as e:
        print(f"❌ Failed to copy brochure files: {e}")
        return False

async def main():
    """Main function to integrate brochure-optimized RAG system"""
    print("🚀 Integrating Brochure-Optimized RAG System")
    print("=" * 60)
    
    # Copy brochure files
    copy_brochure_files()
    
    # Update components
    update_production_integration()
    update_serve_api()
    update_main_init()
    update_cli()
    
    print("\\n🎉 Brochure RAG integration completed!")
    print("\\nYou can now use:")
    print("  - ask_brochure_question() in Python")
    print("  - python3 docqa.py ask-brochure 'question' in CLI")
    print("  - Optimized for company brochure content")

if __name__ == "__main__":
    asyncio.run(main())
