#!/usr/bin/env python3
"""
Interactive RAG Question Tester

A simple interactive tool to test the RAG system with dynamic franchisor detection.
You can ask questions and see the responses in real-time.
"""

import requests
import time
from datetime import datetime


class InteractiveRAGTester:
    """Interactive tester for the RAG system"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.auth_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************.nZoPU-4srcP-LM4ELoVRmg_e5VMAS_SZ9CG6SYNUvRk"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.auth_token}"
        }
        self.question_count = 0
    
    def send_question(self, question: str) -> bool:
        """Send a question to the RAG system via webhook"""
        self.question_count += 1
        
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": datetime.now().isoformat() + "Z",
            "mo": {
                "id": f"interactive-test-{self.question_count}-{int(time.time())}",
                "sender": "61430250079",
                "recipient": "61430250080",
                "message": question
            }
        }
        
        try:
            print("🚀 Sending question to RAG system...")
            response = requests.post(
                f"{self.base_url}/api/webhooks/webhooks/kudosity",
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                print("✅ Question processed successfully!")
                print(f"📊 Status: {response.status_code}")
                print("🔍 Check the server logs for the AI response and franchisor detection details.")
                return True
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    def show_sample_questions(self):
        """Display sample questions users can try"""
        print("\n💡 SAMPLE QUESTIONS TO TRY:")
        print("="*60)
        
        samples = [
            "🌱 Coochie Hydrogreen Specific:",
            "  • What are the investment requirements for Coochie Hydrogreen?",
            "  • How much does a Coochie Hydrogreen franchise cost?",
            "  • What support does Coochie Hydrogreen provide?",
            "  • What is the royalty structure for Coochie Hydrogreen?",
            "",
            "🔍 Contextual Detection:",
            "  • Tell me about eco-friendly car wash franchises",
            "  • I'm looking for waterless cleaning business opportunities",
            "  • What automotive franchises are available in Australia?",
            "",
            "🏢 General Franchise:",
            "  • What franchise opportunities are available?",
            "  • Which franchises have low startup costs?",
            "  • Compare different franchise investment requirements",
            "",
            "🧪 Test Cases:",
            "  • What's the weather today? (non-franchise question)",
            "  • Tell me about pizza franchises (different industry)",
        ]
        
        for sample in samples:
            print(sample)
    
    def show_help(self):
        """Show help information"""
        print("\n❓ HELP & COMMANDS:")
        print("="*60)
        print("📝 Just type your question and press Enter")
        print("🔍 The system will automatically detect franchise names")
        print("🤖 AI responses appear in the server logs")
        print("")
        print("Special Commands:")
        print("  • 'help' or '?' - Show this help")
        print("  • 'samples' - Show sample questions")
        print("  • 'stats' - Show session statistics")
        print("  • 'clear' - Clear screen")
        print("  • 'quit' or 'exit' - Exit the program")
    
    def show_stats(self):
        """Show session statistics"""
        print("\n📊 SESSION STATISTICS:")
        print("="*60)
        print(f"Questions Asked: {self.question_count}")
        print(f"Server URL: {self.base_url}")
        print(f"Authentication: {'✅ Configured' if self.auth_token else '❌ Missing'}")
    
    def clear_screen(self):
        """Clear the screen"""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def run(self):
        """Run the interactive tester"""
        print("🤖 INTERACTIVE RAG SYSTEM TESTER")
        print("="*60)
        print("Welcome to the enhanced RAG system with dynamic franchisor detection!")
        print("Ask questions about franchises and see the AI responses in real-time.")
        print("")
        print("💡 Tip: The server logs will show detailed franchisor detection and AI responses")
        print("🔧 Make sure the server is running on http://localhost:8000")
        print("")
        print("Type 'help' for commands or 'samples' for example questions")
        print("="*60)
        
        while True:
            try:
                # Get user input
                question = input("\n❓ Your question (or 'quit' to exit): ").strip()
                
                if not question:
                    continue
                
                # Handle special commands
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Thank you for testing the RAG system!")
                    break
                elif question.lower() in ['help', '?']:
                    self.show_help()
                    continue
                elif question.lower() == 'samples':
                    self.show_sample_questions()
                    continue
                elif question.lower() == 'stats':
                    self.show_stats()
                    continue
                elif question.lower() == 'clear':
                    self.clear_screen()
                    continue
                
                # Process the question
                print(f"\n🔄 Processing: '{question}'")
                print("-" * 60)
                
                success = self.send_question(question)
                
                if success:
                    print(f"✨ Question #{self.question_count} completed!")
                    print("📺 Check your server console for the detailed AI response")
                else:
                    print(f"⚠️  Question #{self.question_count} failed to process")
                
                print("-" * 60)
                
            except KeyboardInterrupt:
                print("\n\n👋 Interrupted by user. Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
                continue


def main():
    """Main function"""
    tester = InteractiveRAGTester()
    tester.run()


if __name__ == "__main__":
    main()
