#!/usr/bin/env python3
"""
Script to reprocess Coochie Hydrogreen brochure
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append('.')

async def reprocess_coochie_brochure():
    """Reprocess the Coochie Hydrogreen brochure"""
    try:
        print("🔄 Reprocessing Coochie Hydrogreen brochure...")
        
        # Import the DocQA integration service
        from app.services.docqa_integration_service import DocQAIntegrationService
        
        # Initialize the service
        docqa_service = DocQAIntegrationService()
        
        # Coochie Hydrogreen details
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        brochure_url = "growthhive/brochure/20250715_120336_4cd334261504.pdf"
        
        print(f"📄 Processing brochure: {brochure_url}")
        print(f"🏢 Franchisor ID: {franchisor_id}")
        
        # Process the brochure
        result = await docqa_service.process_franchisor_brochure(
            franchisor_id=franchisor_id,
            brochure_url=brochure_url
        )
        
        if result and result.success:
            print(f"✅ Successfully processed brochure!")
            print(f"📊 Chunks created: {result.chunks_created}")
            print(f"📋 Table: {result.table_name}")
            print(f"🆔 Document ID: {result.document_id}")
        else:
            print(f"❌ Failed to process brochure")
            if result:
                print(f"Error: {result.error_message}")
            else:
                print("No result returned")
        
        # Test a question after processing
        print("\n🧪 Testing question after reprocessing...")
        from docqa.central_api import ask_question
        
        request = {
            "question": "What is FRANCHISEE APPROVAL PROCESS?",
            "franchisor_id": franchisor_id,
            "top_k": 5,
            "similarity_threshold": 0.5,
            "force_refresh": True  # Force refresh to bypass cache
        }
        
        answer_result = await ask_question(request)
        print(f"📝 Answer: {answer_result.get('answer', 'No answer')}")
        
    except Exception as e:
        print(f"❌ Error during reprocessing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(reprocess_coochie_brochure())
