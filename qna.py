#!/usr/bin/env python3
"""
Universal Document QnA System

Single script to process ANY file type and answer questions.
Supports: PDF, Excel (.xlsx/.xls), Word (.docx), PowerPoint (.pptx), 
         CSV, Text, HTML, Images, and more.

Usage:
    python3 qna.py "file_url" "your question"
    python3 qna.py "file_url"  # Interactive mode

Examples:
    python3 qna.py "s3://bucket/document.pdf" "What is this document about?"
    python3 qna.py "https://example.com/data.xlsx" "What data is in this file?"
    python3 qna.py "s3://bucket/presentation.pptx" "Summarize the key points"
"""

import argparse
import os
import subprocess
import tempfile
import requests
from pathlib import Path
from urllib.parse import urlparse

try:
    import pandas as pd
    import openai
    from PIL import Image
    import base64
    import io
    LIBS_AVAILABLE = True
except ImportError:
    LIBS_AVAILABLE = False

# Try to import additional libraries for image extraction
try:
    import fitz  # PyMuPDF for PDF image extraction
    PDF_LIBS_AVAILABLE = True
except ImportError:
    PDF_LIBS_AVAILABLE = False

try:
    from docx import Document
    from docx.document import Document as DocumentType
    WORD_LIBS_AVAILABLE = True
except ImportError:
    WORD_LIBS_AVAILABLE = False

try:
    from pptx import Presentation
    PPTX_LIBS_AVAILABLE = True
except ImportError:
    PPTX_LIBS_AVAILABLE = False


class UniversalDocumentQA:
    """Universal document processing and QnA system."""
    
    def __init__(self, api_key: str):
        """Initialize the system."""
        self.api_key = api_key
        self.openai_client = openai.OpenAI(api_key=api_key)
        self.temp_files = []
        self.document_content = ""
        self.file_type = ""
        self.processed = False
        self.extracted_images = []  # Store extracted images for analysis
        self.image_analyses = []    # Store GPT-4 Vision analyses
    
    def process_url(self, url: str) -> bool:
        """Process document from URL."""
        try:
            print("🔄 Processing document from URL...")
            print(f"🔗 URL: {url}")
            
            # Handle different URL types
            if url.startswith('s3://'):
                return self._process_s3_url(url)
            elif url.startswith('http'):
                return self._process_http_url(url)
            else:
                # Assume local file
                return self._process_local_file(url)
                
        except Exception as e:
            print(f"❌ Error processing URL: {e}")
            return False
    
    def _process_s3_url(self, s3_url: str) -> bool:
        """Process S3 URL using existing docqa.py system."""
        try:
            print("📄 Processing S3 document with docqa.py...")
            
            # Use existing docqa.py for S3 files
            result = subprocess.run([
                "python", "docqa.py", "ingest", s3_url, "--force"
            ], capture_output=True, text=True, env={**os.environ})
            
            if result.returncode == 0:
                print("✅ S3 document processed successfully!")
                self.processed = True
                self.file_type = "s3_document"
                return True
            else:
                print(f"❌ S3 processing failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ S3 processing error: {e}")
            return False
    
    def _process_http_url(self, url: str) -> bool:
        """Process HTTP/HTTPS URL by downloading and processing."""
        try:
            print("🔄 Downloading document from HTTP URL...")
            
            # Download file
            response = requests.get(url, timeout=60)
            response.raise_for_status()
            
            # Determine file extension
            parsed_url = urlparse(url)
            file_ext = Path(parsed_url.path).suffix
            if not file_ext:
                content_type = response.headers.get('content-type', '')
                file_ext = self._get_extension_from_content_type(content_type)
            
            # Save to temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_ext)
            temp_filename = temp_file.name
            self.temp_files.append(temp_filename)
            
            with temp_file as f:
                f.write(response.content)
            
            print(f"✅ Downloaded: {len(response.content):,} bytes")
            
            # Process the downloaded file
            return self._process_local_file(temp_filename)
            
        except Exception as e:
            print(f"❌ HTTP download error: {e}")
            return False
    
    def _process_local_file(self, file_path: str) -> bool:
        """Process local file based on its type."""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                print(f"❌ File not found: {file_path}")
                return False
            
            file_ext = file_path.suffix.lower()
            print(f"📄 Processing {file_ext} file: {file_path.name}")
            
            # Process based on file type
            if file_ext == '.pdf':
                return self._process_pdf(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                return self._process_excel(file_path)
            elif file_ext in ['.docx', '.doc']:
                return self._process_word(file_path)
            elif file_ext in ['.pptx', '.ppt']:
                return self._process_powerpoint(file_path)
            elif file_ext == '.csv':
                return self._process_csv(file_path)
            elif file_ext in ['.txt', '.md']:
                return self._process_text(file_path)
            elif file_ext in ['.html', '.htm']:
                return self._process_html(file_path)
            elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                return self._process_image(file_path)
            else:
                print(f"❌ Unsupported file type: {file_ext}")
                return False
                
        except Exception as e:
            print(f"❌ Local file processing error: {e}")
            return False
    
    def _process_excel(self, file_path: Path) -> bool:
        """Process Excel file with image analysis."""
        try:
            excel_data = pd.read_excel(file_path, sheet_name=None)

            content_parts = [f"EXCEL FILE: {file_path.name}", f"Total Sheets: {len(excel_data)}", ""]

            for sheet_name, df in excel_data.items():
                content_parts.extend([
                    f"SHEET: {sheet_name}",
                    f"Size: {len(df)} rows × {len(df.columns)} columns",
                    f"Columns: {list(df.columns)}",
                    ""
                ])

                if len(df) > 0:
                    content_parts.append("Sample Data:")
                    content_parts.append(df.head(3).to_string())
                    content_parts.append("")

            # Extract and analyze images
            images = self._extract_images_from_excel(file_path)
            if images:
                content_parts.append("=== IMAGE ANALYSIS ===")
                for img in images:
                    analysis = self._analyze_image_with_gpt4_vision(img['path'], f"Excel file: {file_path.name}")
                    content_parts.append(f"Image Analysis: {analysis}")
                    content_parts.append("")

            self.document_content = "\n".join(content_parts)
            self.file_type = "excel"
            self.processed = True
            print(f"✅ Excel file processed: {len(excel_data)} sheets, {len(images)} images analyzed")
            return True

        except Exception as e:
            print(f"❌ Excel processing error: {e}")
            return False
    
    def _process_text(self, file_path: Path) -> bool:
        """Process text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.document_content = f.read()
            
            self.file_type = "text"
            self.processed = True
            print(f"✅ Text file processed: {len(self.document_content)} characters")
            return True
            
        except Exception as e:
            print(f"❌ Text processing error: {e}")
            return False
    
    def _process_csv(self, file_path: Path) -> bool:
        """Process CSV file."""
        try:
            df = pd.read_csv(file_path)
            
            content_parts = [
                f"CSV FILE: {file_path.name}",
                f"Size: {len(df)} rows × {len(df.columns)} columns",
                f"Columns: {list(df.columns)}",
                "",
                "Sample Data:",
                df.head(5).to_string(),
                "",
                "Statistics:",
                df.describe().to_string() if len(df.select_dtypes(include=['number']).columns) > 0 else "No numeric columns"
            ]
            
            self.document_content = "\n".join(content_parts)
            self.file_type = "csv"
            self.processed = True
            print(f"✅ CSV file processed: {len(df)} rows")
            return True
            
        except Exception as e:
            print(f"❌ CSV processing error: {e}")
            return False
    
    def _process_pdf(self, file_path: Path) -> bool:
        """Process PDF with image extraction and analysis."""
        try:
            content_parts = [f"PDF FILE: {file_path.name}"]

            # Extract and analyze images from PDF
            images = self._extract_images_from_pdf(file_path)

            if images:
                content_parts.append(f"\n=== EXTRACTED {len(images)} IMAGES FROM PDF ===")
                for img in images:
                    analysis = self._analyze_image_with_gpt4_vision(
                        img['path'],
                        f"PDF page {img['page']}"
                    )
                    content_parts.append(f"\nPage {img['page']}, Image {img['index']}:")
                    content_parts.append(analysis)
                    content_parts.append("")
            else:
                content_parts.append("(No images extracted - may require PyMuPDF library)")

            self.document_content = "\n".join(content_parts)
            self.file_type = "pdf"
            self.processed = True
            print(f"✅ PDF processed: {len(images)} images analyzed")
            return True

        except Exception as e:
            print(f"❌ PDF processing error: {e}")
            return False
    
    def _process_word(self, file_path: Path) -> bool:
        """Process Word document."""
        self.document_content = f"WORD DOCUMENT: {file_path.name}\n(Word processing requires python-docx)"
        self.file_type = "word"
        self.processed = True
        print("✅ Word document noted")
        return True
    
    def _process_powerpoint(self, file_path: Path) -> bool:
        """Process PowerPoint."""
        self.document_content = f"POWERPOINT: {file_path.name}\n(PowerPoint processing requires python-pptx)"
        self.file_type = "powerpoint"
        self.processed = True
        print("✅ PowerPoint noted")
        return True
    
    def _process_html(self, file_path: Path) -> bool:
        """Process HTML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # Basic HTML processing (remove tags for simple text extraction)
            import re
            text_content = re.sub(r'<[^>]+>', '', html_content)
            
            self.document_content = f"HTML FILE: {file_path.name}\n\n{text_content}"
            self.file_type = "html"
            self.processed = True
            print("✅ HTML file processed")
            return True
            
        except Exception as e:
            print(f"❌ HTML processing error: {e}")
            return False
    
    def _process_image(self, file_path: Path) -> bool:
        """Process image file with GPT-4 Vision analysis."""
        try:
            print(f"🖼️  Processing image file: {file_path.name}")

            # Analyze the image directly with GPT-4 Vision
            analysis = self._analyze_image_with_gpt4_vision(str(file_path), f"Standalone image: {file_path.name}")

            content_parts = [
                f"IMAGE FILE: {file_path.name}",
                "",
                "=== GPT-4 VISION ANALYSIS ===",
                analysis
            ]

            self.document_content = "\n".join(content_parts)
            self.file_type = "image"
            self.processed = True
            print("✅ Image analyzed with GPT-4 Vision")
            return True

        except Exception as e:
            print(f"❌ Image processing error: {e}")
            return False
    
    def _get_extension_from_content_type(self, content_type: str) -> str:
        """Get file extension from content type."""
        content_type_map = {
            'application/pdf': '.pdf',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
            'application/vnd.ms-excel': '.xls',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
            'text/plain': '.txt',
            'text/csv': '.csv',
            'text/html': '.html',
            'image/jpeg': '.jpg',
            'image/png': '.png',
        }
        return content_type_map.get(content_type, '.bin')

    def _encode_image_to_base64(self, image_path: str) -> str:
        """Encode image to base64 for OpenAI Vision API."""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"❌ Error encoding image: {e}")
            return ""

    def _analyze_image_with_gpt4_vision(self, image_path: str, context: str = "") -> str:
        """Analyze image using GPT-4 Vision."""
        try:
            print(f"👁️  Analyzing image with GPT-4 Vision: {Path(image_path).name}")

            # Encode image
            base64_image = self._encode_image_to_base64(image_path)
            if not base64_image:
                return "Failed to encode image"

            # Create prompt
            prompt = f"""
Analyze this image in detail. This image is from a document{f' ({context})' if context else ''}.

Please provide:
1. **Image Type**: What type of visual content is this? (chart, graph, diagram, table, photo, etc.)
2. **Content Description**: Detailed description of what you see
3. **Data/Information**: Any specific data, numbers, text, or information visible
4. **Key Insights**: Important insights or patterns you can identify
5. **Context**: How this image relates to the document content

Be thorough and specific in your analysis.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                temperature=0.1,
            )

            analysis = response.choices[0].message.content.strip()
            print("✅ Image analysis completed")
            return analysis

        except Exception as e:
            print(f"❌ Image analysis error: {e}")
            return f"Error analyzing image: {str(e)}"

    def _extract_images_from_pdf(self, pdf_path: Path) -> list:
        """Extract images from PDF using PyMuPDF."""
        if not PDF_LIBS_AVAILABLE:
            print("⚠️  PyMuPDF not available for PDF image extraction")
            return []

        try:
            print("🖼️  Extracting images from PDF...")
            doc = fitz.open(pdf_path)
            images = []

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                image_list = page.get_images()

                for img_index, img in enumerate(image_list):
                    try:
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)

                        if pix.n - pix.alpha < 4:  # GRAY or RGB
                            img_data = pix.tobytes("png")

                            # Save image
                            img_filename = f"pdf_image_p{page_num+1}_{img_index+1}.png"
                            img_path = Path(tempfile.gettempdir()) / img_filename

                            with open(img_path, "wb") as img_file:
                                img_file.write(img_data)

                            self.temp_files.append(str(img_path))
                            images.append({
                                'path': str(img_path),
                                'page': page_num + 1,
                                'index': img_index + 1
                            })

                        pix = None

                    except Exception as e:
                        print(f"⚠️  Error extracting image {img_index+1} from page {page_num+1}: {e}")
                        continue

            doc.close()
            print(f"✅ Extracted {len(images)} images from PDF")
            return images

        except Exception as e:
            print(f"❌ PDF image extraction error: {e}")
            return []

    def _extract_images_from_excel(self, file_path: Path) -> list:
        """Extract embedded images from Excel files."""
        # Excel image extraction is complex and requires openpyxl with additional setup
        # For now, we'll note that images exist but can't extract them easily
        print(f"📊 Excel file {file_path.name} may contain charts - analyzing data structure for visual patterns")
        return []

    def _extract_images_from_word(self, file_path: Path) -> list:
        """Extract images from Word documents."""
        if not WORD_LIBS_AVAILABLE:
            print("⚠️  python-docx not available for Word image extraction")
            return []

        try:
            print("🖼️  Extracting images from Word document...")
            doc = Document(file_path)
            images = []

            # Extract images from document
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    try:
                        img_data = rel.target_part.blob

                        # Determine image format
                        img_format = "png"
                        if rel.target_ref.endswith('.jpg') or rel.target_ref.endswith('.jpeg'):
                            img_format = "jpg"

                        # Save image
                        img_filename = f"word_image_{len(images)+1}.{img_format}"
                        img_path = Path(tempfile.gettempdir()) / img_filename

                        with open(img_path, "wb") as img_file:
                            img_file.write(img_data)

                        self.temp_files.append(str(img_path))
                        images.append({
                            'path': str(img_path),
                            'source': 'word_document'
                        })

                    except Exception as e:
                        print(f"⚠️  Error extracting Word image: {e}")
                        continue

            print(f"✅ Extracted {len(images)} images from Word document")
            return images

        except Exception as e:
            print(f"❌ Word image extraction error: {e}")
            return []

    def _extract_images_from_powerpoint(self, file_path: Path) -> list:
        """Extract images from PowerPoint presentations."""
        if not PPTX_LIBS_AVAILABLE:
            print("⚠️  python-pptx not available for PowerPoint image extraction")
            return []

        try:
            print("🖼️  Extracting images from PowerPoint...")
            prs = Presentation(file_path)
            images = []

            for slide_num, slide in enumerate(prs.slides):
                for shape in slide.shapes:
                    if hasattr(shape, "image"):
                        try:
                            img_data = shape.image.blob

                            # Save image
                            img_filename = f"ppt_slide{slide_num+1}_img{len(images)+1}.png"
                            img_path = Path(tempfile.gettempdir()) / img_filename

                            with open(img_path, "wb") as img_file:
                                img_file.write(img_data)

                            self.temp_files.append(str(img_path))
                            images.append({
                                'path': str(img_path),
                                'slide': slide_num + 1
                            })

                        except Exception as e:
                            print(f"⚠️  Error extracting PowerPoint image: {e}")
                            continue

            print(f"✅ Extracted {len(images)} images from PowerPoint")
            return images

        except Exception as e:
            print(f"❌ PowerPoint image extraction error: {e}")
            return []
    
    def ask_question(self, question: str) -> str:
        """Ask a question about the processed document."""
        if not self.processed:
            return "❌ No document has been processed yet."
        
        try:
            # For S3 documents, use existing docqa.py
            if self.file_type == "s3_document":
                result = subprocess.run([
                    "python", "docqa.py", "ask", question, "--no-stream"
                ], capture_output=True, text=True, env={**os.environ})
                
                if result.returncode == 0:
                    return result.stdout.strip()
                else:
                    return f"❌ Error: {result.stderr.strip()}"
            
            # For other documents, use OpenAI directly
            prompt = f"""
Based on the following document content, please answer the question accurately:

DOCUMENT CONTENT:
{self.document_content[:4000]}...

QUESTION: {question}

Please provide a detailed answer based on the document content above.

ANSWER:"""
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": f"You are an expert document analyst. Answer questions about {self.file_type} documents accurately based on the provided content."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=1000,
                temperature=0.1,
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            return f"❌ Error answering question: {e}"
    
    def cleanup(self):
        """Clean up temporary files."""
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Universal Document QnA System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("url", help="Document URL (S3, HTTP, or local file path)")
    parser.add_argument("question", nargs='?', help="Question to ask (optional, interactive mode if not provided)")
    
    args = parser.parse_args()
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Initialize system
    qa_system = UniversalDocumentQA(api_key)
    
    try:
        # Process document
        if not qa_system.process_url(args.url):
            return
        
        # Handle question
        if args.question:
            # Single question mode
            print(f"\n❓ Question: {args.question}")
            print("\n💭 Answer:")
            answer = qa_system.ask_question(args.question)
            print(answer)
        else:
            # Interactive mode
            print("\n🤖 Interactive Q&A Mode")
            print("💡 Ask questions about the document. Type 'quit' to exit.")
            print("=" * 50)
            
            while True:
                try:
                    question = input("\n❓ Your question: ").strip()
                    
                    if question.lower() in ['quit', 'exit', 'q']:
                        print("👋 Goodbye!")
                        break
                    
                    if not question:
                        continue
                    
                    print("\n💭 Answer:")
                    answer = qa_system.ask_question(question)
                    print(answer)
                    print("\n" + "-" * 50)
                    
                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    break
    
    finally:
        # Cleanup
        qa_system.cleanup()


if __name__ == "__main__":
    main()
