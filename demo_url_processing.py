#!/usr/bin/env python3
"""
Demo: URL-based Document Processing with Maximum OpenAI Integration

This script demonstrates how to process documents from URLs using the enhanced
ingestion system with comprehensive OpenAI features.
"""

import os
import sys
import requests
import tempfile
from pathlib import Path

# Add the ingest package to the path
sys.path.insert(0, str(Path(__file__).parent))

from ingest.core import IngestionConfig, LanguageCode

# Import DocumentIngestionSystem from the main ingest.py file
import importlib.util
spec = importlib.util.spec_from_file_location("ingest_main", "ingest.py")
ingest_main = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ingest_main)
DocumentIngestionSystem = ingest_main.DocumentIngestionSystem

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False


class URLDocumentProcessor:
    """
    URL-based document processor with maximum OpenAI integration.
    """
    
    def __init__(self, api_key: str):
        """Initialize the processor."""
        self.api_key = api_key
        self.temp_files = []
        
        print("🌐 URL Document Processor with Maximum OpenAI Integration")
        print("=" * 65)
    
    def download_from_url(self, url: str) -> str:
        """Download document from URL to temporary file."""
        try:
            print("🔄 Downloading document from URL...")
            print(f"   📍 URL: {url}")
            
            # Make request with timeout
            response = requests.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            # Determine file extension from URL or content type
            file_ext = self._get_file_extension(url, response.headers.get('content-type', ''))
            
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_ext)
            temp_filename = temp_file.name
            self.temp_files.append(temp_filename)
            
            # Download with progress
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with temp_file as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            print(f"\r   📊 Progress: {progress:.1f}% ({downloaded:,}/{total_size:,} bytes)", end="")
            
            print(f"\n✅ Download completed: {temp_filename}")
            print(f"   📊 File size: {downloaded:,} bytes")
            
            return temp_filename
            
        except Exception as e:
            print(f"❌ Download failed: {e}")
            raise
    
    def _get_file_extension(self, url: str, content_type: str) -> str:
        """Determine file extension from URL or content type."""
        # Try to get extension from URL
        url_path = Path(url).suffix
        if url_path:
            return url_path
        
        # Fallback to content type
        content_type_map = {
            'application/pdf': '.pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
            'text/plain': '.txt',
            'text/csv': '.csv',
            'text/html': '.html',
            'image/jpeg': '.jpg',
            'image/png': '.png',
        }
        
        return content_type_map.get(content_type, '.bin')
    
    def process_document(self, file_path: str) -> bool:
        """Process document with maximum OpenAI integration."""
        try:
            print("\n🔧 Processing document with Enhanced AI System...")
            print(f"   📄 File: {Path(file_path).name}")
            
            # Create configuration with maximum OpenAI usage
            config = IngestionConfig(
                # OpenAI Configuration
                openai_api_key=self.api_key,
                openai_model="gpt-4o",
                openai_vision_model="gpt-4o",
                openai_embedding_model="text-embedding-3-small",
                
                # AI Features (all enabled for maximum OpenAI usage)
                extract_charts=True,
                translate=True,
                target_language=LanguageCode.ENGLISH,
                use_gpt_for_text_analysis=True,
                use_gpt_for_summarization=True,
                use_gpt_for_classification=True,
                use_gpt_for_metadata=True,
                use_gpt_for_quality_check=True,
                use_gpt_for_content_enhancement=True,
                
                # Vector Configuration
                generate_embeddings=True,
                embedding_batch_size=50,
                vector_similarity_threshold=0.7,
                use_semantic_search=True,
                store_vectors=True,
                
                # Processing Parameters
                chunk_size=300,  # Smaller for demo
                chunk_overlap=40,
                max_file_size_mb=50,
                timeout_seconds=120,
                concurrent_workers=2,
            )
            
            print("🤖 AI Configuration:")
            print(f"   🧠 GPT Model: {config.openai_model}")
            print(f"   👁️  Vision Model: {config.openai_vision_model}")
            print(f"   🔢 Embedding Model: {config.openai_embedding_model}")
            print(f"   📊 Chart Extraction: {config.extract_charts}")
            print(f"   🌐 Translation: {config.translate}")
            print(f"   🧠 GPT Analysis: {config.use_gpt_for_text_analysis}")
            print(f"   📝 GPT Summarization: {config.use_gpt_for_summarization}")
            print(f"   🏷️  GPT Classification: {config.use_gpt_for_classification}")
            print(f"   📋 GPT Metadata: {config.use_gpt_for_metadata}")
            print(f"   🔍 Vector Embeddings: {config.generate_embeddings}")
            
            # Initialize ingestion system
            print("\n🔧 Initializing Enhanced Ingestion System...")
            system = DocumentIngestionSystem(config)
            
            # Process the document
            print("🚀 Starting document processing...")
            result = system.ingest_document(file_path)
            
            if result.success:
                print("\n✅ Document processed successfully!")
                print(f"   📊 Chunks created: {len(result.chunks)}")
                print(f"   ⏱️  Processing time: {result.processing_time:.2f}s")
                print(f"   📈 Charts extracted: {result.charts_extracted}")
                print(f"   📄 Pages processed: {result.pages_processed}")
                
                # Show sample enhanced chunks
                self._display_enhanced_chunks(result.chunks[:2])
                
                # Demonstrate vector search
                self._demonstrate_vector_search(system)
                
                return True
            else:
                print(f"❌ Document processing failed: {result.error_message}")
                return False
                
        except Exception as e:
            print(f"❌ Error processing document: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _display_enhanced_chunks(self, chunks):
        """Display enhanced chunk information."""
        print(f"\n🧠 Enhanced Chunk Analysis (showing first {len(chunks)} chunks):")
        print("-" * 60)
        
        for i, chunk in enumerate(chunks):
            print(f"\n📝 Chunk {i+1}:")
            print(f"   Text Preview: {chunk.text[:100]}...")
            print(f"   Language: {chunk.language}")
            print(f"   Token Count: {chunk.token_count}")
            
            # Show GPT enhancements if available
            if 'gpt_analysis' in chunk.metadata:
                analysis = chunk.metadata['gpt_analysis']
                print("   🧠 GPT Analysis:")
                print(f"      Content Type: {analysis.get('content_type', 'N/A')}")
                print(f"      Sentiment: {analysis.get('sentiment', 'N/A')}")
                print(f"      Complexity: {analysis.get('complexity_level', 'N/A')}")
            
            if 'gpt_summary' in chunk.metadata:
                summary = chunk.metadata['gpt_summary']
                print(f"   📝 GPT Summary: {summary[:80]}...")
            
            if 'gpt_classification' in chunk.metadata:
                classification = chunk.metadata['gpt_classification']
                print(f"   🏷️  Classification: {classification.get('document_category', 'N/A')}")
    
    def _demonstrate_vector_search(self, system):
        """Demonstrate vector search capabilities."""
        print("\n🔍 Vector Search Demonstration:")
        print("-" * 40)
        
        # Test queries
        test_queries = [
            "What is this document about?",
            "Financial information and costs",
            "Business model and services"
        ]
        
        for query in test_queries:
            print(f"\n❓ Query: {query}")
            try:
                results = system.vector_store.search(query, top_k=2)
                
                for j, (entry, score) in enumerate(results):
                    print(f"   📄 Result {j+1} (Similarity: {score:.3f}):")
                    print(f"      Source: {entry.source_file}")
                    print(f"      Page: {entry.page_number}")
                    
            except Exception as e:
                print(f"   ❌ Search failed: {e}")
    
    def cleanup(self):
        """Clean up temporary files."""
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
                print(f"🗑️  Cleaned up: {temp_file}")
            except Exception as e:
                print(f"⚠️  Failed to clean up {temp_file}: {e}")


def main():
    """Main demonstration function."""
    print("🌐 URL Document Processing Demo")
    print("=" * 40)
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    print(f"✅ OpenAI API Key found: {api_key[:10]}...")
    
    # Example URL
    example_url = "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/brochure/20250701_113703_07ce376fa750.pdf"
    
    # Initialize processor
    processor = URLDocumentProcessor(api_key)
    
    try:
        # Download document
        temp_file = processor.download_from_url(example_url)
        
        # Process document
        success = processor.process_document(temp_file)
        
        if success:
            print("\n🎉 URL document processing completed successfully!")
            print("📊 The document has been processed with maximum OpenAI integration")
            print("🔍 Vector embeddings are ready for semantic search")
        else:
            print("\n❌ URL document processing failed!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
    
    finally:
        # Cleanup
        processor.cleanup()


if __name__ == "__main__":
    main()
