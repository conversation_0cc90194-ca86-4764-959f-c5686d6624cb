#!/usr/bin/env python3
"""
Analyze the actual content in the Coochie Hydrogreen document and test answerable questions
"""

import asyncio
import sys
import re

# Add the project root to the path
sys.path.append('.')

async def analyze_document_content():
    """Analyze what content is actually in the document"""
    try:
        print("🔍 Analyzing Coochie Hydrogreen document content...")
        
        # Get the actual content from the database
        from docqa.vector_store import PgVectorStore
        
        vector_store = PgVectorStore()
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Search for all content with very low threshold
        search_results = await vector_store.search_franchisor_embeddings(
            query_embedding=[0.0] * 1536,  # Dummy embedding
            franchisor_id=franchisor_id,
            top_k=10,
            similarity_threshold=0.0  # Get everything
        )
        
        if search_results:
            print(f"📊 Found {len(search_results)} content chunks")
            
            for i, result in enumerate(search_results):
                content = result.get('content', result.get('text', ''))
                print(f"\n--- CHUNK {i+1} ---")
                print(f"Length: {len(content)} characters")
                print(f"Preview: {content[:500]}...")
                
                # Analyze content for key topics
                content_lower = content.lower()
                
                # Look for business-related keywords
                keywords = {
                    'business': content_lower.count('business'),
                    'franchise': content_lower.count('franchise'),
                    'coochie': content_lower.count('coochie'),
                    'hydrogreen': content_lower.count('hydrogreen'),
                    'australia': content_lower.count('australia'),
                    'service': content_lower.count('service'),
                    'water': content_lower.count('water'),
                    'cleaning': content_lower.count('cleaning'),
                    'company': content_lower.count('company'),
                    'location': content_lower.count('location'),
                    'contact': content_lower.count('contact'),
                    'phone': content_lower.count('phone'),
                    'email': content_lower.count('email'),
                    'website': content_lower.count('website'),
                    'address': content_lower.count('address')
                }
                
                print(f"Keywords found: {dict(sorted(keywords.items(), key=lambda x: x[1], reverse=True))}")
        else:
            print("❌ No content found in database")
            
    except Exception as e:
        print(f"💥 Error analyzing content: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_specific_questions():
    """Test questions that might have answers based on common brochure content"""
    try:
        from docqa.central_api import ask_question
        
        # Questions that are likely to have answers in a business brochure
        potential_questions = [
            "What is Coochie Hydrogreen?",
            "Where is Coochie Hydrogreen located?",
            "What services does Coochie Hydrogreen provide?",
            "What type of business is Coochie Hydrogreen?",
            "Who is the contact person for Coochie Hydrogreen?",
            "What is the phone number for Coochie Hydrogreen?",
            "What is the email address for Coochie Hydrogreen?",
            "What is the website for Coochie Hydrogreen?",
            "What is the address of Coochie Hydrogreen?",
            "What industry is Coochie Hydrogreen in?",
            "What does Coochie Hydrogreen do?",
            "Tell me about Coochie Hydrogreen company",
            "What is the business description of Coochie Hydrogreen?",
            "What are the main services offered?",
            "What type of franchise is this?",
            "What is the company name?",
            "Where is the business based?",
            "What is the nature of the business?",
            "What products or services are offered?",
            "What is the business category?"
        ]
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        print("\n📝 Testing questions that might have answers:")
        answerable_questions = []
        
        for i, question in enumerate(potential_questions, 1):
            print(f"\n{i}. ❓ Question: {question}")
            
            request = {
                "question": question,
                "top_k": 5,
                "similarity_threshold": 0.1,
                "franchisor_id": franchisor_id
            }
            
            try:
                result = await ask_question(request)
                
                if result.get('success'):
                    answer = result.get('answer', 'No answer provided')
                    sources = result.get('sources', [])
                    
                    # Check if the answer contains actual information (not just "context does not include")
                    if not any(phrase in answer.lower() for phrase in [
                        'does not include', 'does not contain', 'unable to provide',
                        'not available', 'cannot provide', 'no information',
                        'more information would be required', 'additional information'
                    ]):
                        print(f"   ✅ ANSWERABLE: {answer[:200]}{'...' if len(answer) > 200 else ''}")
                        answerable_questions.append({
                            'question': question,
                            'answer': answer,
                            'sources_count': len(sources)
                        })
                    else:
                        print(f"   ❌ No specific info: {answer[:100]}...")
                else:
                    print(f"   💥 Error: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   💥 Exception: {str(e)}")
        
        print(f"\n🎯 Summary: Found {len(answerable_questions)} answerable questions")
        
        if answerable_questions:
            print("\n📋 QUESTIONS THE SYSTEM CAN ANSWER:")
            print("=" * 60)
            
            for i, qa in enumerate(answerable_questions, 1):
                print(f"\n{i}. Q: {qa['question']}")
                print(f"   A: {qa['answer']}")
                print(f"   Sources: {qa['sources_count']}")
                print("-" * 60)
        else:
            print("\n⚠️ No questions found that the system can answer with specific information.")
            print("This suggests the document may not contain detailed business information.")
        
        return answerable_questions
        
    except Exception as e:
        print(f"💥 Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


async def extract_key_information():
    """Try to extract key information directly from the content"""
    try:
        print("\n🔍 Extracting key information from document content...")
        
        from docqa.vector_store import PgVectorStore
        
        vector_store = PgVectorStore()
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Get all content
        search_results = await vector_store.search_franchisor_embeddings(
            query_embedding=[0.0] * 1536,
            franchisor_id=franchisor_id,
            top_k=10,
            similarity_threshold=0.0
        )
        
        if search_results:
            full_content = ""
            for result in search_results:
                content = result.get('content', result.get('text', ''))
                full_content += content + "\n"
            
            print(f"📄 Full content length: {len(full_content)} characters")
            
            # Extract specific information patterns
            info_patterns = {
                'Phone Numbers': r'(\+?[\d\s\-\(\)]{10,})',
                'Email Addresses': r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                'Websites': r'((?:https?://)?(?:www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                'Addresses': r'(\d+\s+[A-Za-z\s,]+(?:Street|St|Road|Rd|Avenue|Ave|Drive|Dr|Lane|Ln|Boulevard|Blvd)[A-Za-z\s,]*)',
                'Business Names': r'(Coochie\s+Hydrogreen[A-Za-z\s]*)',
                'Locations': r'(Australia|Queensland|QLD|NSW|Victoria|VIC|[A-Z][a-z]+\s+\d{4})'
            }
            
            extracted_info = {}
            for category, pattern in info_patterns.items():
                matches = re.findall(pattern, full_content, re.IGNORECASE)
                if matches:
                    # Clean and deduplicate matches
                    cleaned_matches = list(set([match.strip() for match in matches if len(match.strip()) > 3]))
                    if cleaned_matches:
                        extracted_info[category] = cleaned_matches
            
            if extracted_info:
                print("\n📋 EXTRACTED INFORMATION:")
                print("=" * 50)
                for category, items in extracted_info.items():
                    print(f"\n{category}:")
                    for item in items[:5]:  # Show first 5 items
                        print(f"  • {item}")
            else:
                print("\n⚠️ No structured information could be extracted using common patterns")
            
            # Show a sample of the content
            print(f"\n📄 CONTENT SAMPLE (first 1000 characters):")
            print("-" * 50)
            print(full_content[:1000])
            print("..." if len(full_content) > 1000 else "")
            
        else:
            print("❌ No content found to analyze")
            
    except Exception as e:
        print(f"💥 Error extracting information: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """Main function to run all analysis"""
    print("🚀 Starting comprehensive document analysis...")
    
    # Step 1: Analyze raw content
    await analyze_document_content()
    
    # Step 2: Extract key information
    await extract_key_information()
    
    # Step 3: Test specific questions
    answerable_questions = await test_specific_questions()
    
    print("\n🎉 Analysis completed!")
    return answerable_questions


if __name__ == "__main__":
    asyncio.run(main())
