#!/usr/bin/env python3
"""
Comprehensive test for file handlers with real file creation
"""

import sys
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

# Add project root to path
sys.path.append('.')

from docqa.file_handlers import (
    get_file_handler_factory, 
    process_file, 
    get_supported_extensions,
    is_supported_file
)


def create_test_pdf():
    """Create a simple test PDF using reportlab"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            c = canvas.Canvas(tmp_file.name, pagesize=letter)
            
            # Add some text content
            c.drawString(100, 750, "Test PDF Document")
            c.drawString(100, 720, "This is a test PDF created for file handler testing.")
            c.drawString(100, 690, "It contains multiple lines of text.")
            c.drawString(100, 660, "The PDF handler should extract this content.")
            
            # Add a simple table-like structure
            c.drawString(100, 600, "Name        Age    City")
            c.drawString(100, 580, "John Doe    25     New York")
            c.drawString(100, 560, "Jane Smith  30     Los Angeles")
            
            c.save()
            return Path(tmp_file.name)
            
    except ImportError:
        print("⚠️  reportlab not available, skipping PDF creation test")
        return None


def create_test_docx():
    """Create a simple test DOCX using python-docx"""
    try:
        from docx import Document
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp_file:
            doc = Document()
            
            # Add content
            doc.add_heading('Test DOCX Document', 0)
            doc.add_paragraph('This is a test DOCX document created for file handler testing.')
            doc.add_paragraph('It contains multiple paragraphs and a table.')
            
            # Add a table
            table = doc.add_table(rows=3, cols=3)
            table.style = 'Table Grid'
            
            # Add table content
            cells = table.rows[0].cells
            cells[0].text = 'Name'
            cells[1].text = 'Age'
            cells[2].text = 'City'
            
            cells = table.rows[1].cells
            cells[0].text = 'John Doe'
            cells[1].text = '25'
            cells[2].text = 'New York'
            
            cells = table.rows[2].cells
            cells[0].text = 'Jane Smith'
            cells[1].text = '30'
            cells[2].text = 'Los Angeles'
            
            doc.save(tmp_file.name)
            return Path(tmp_file.name)
            
    except ImportError:
        print("⚠️  python-docx not available, skipping DOCX creation test")
        return None


def create_test_image_with_text():
    """Create a test image with clear text for OCR"""
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
        # Create a larger image with clear text
        img = Image.new('RGB', (800, 400), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a better font
        try:
            # Try to load a system font
            font_size = 24
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
        except:
            try:
                font = ImageFont.load_default()
            except:
                font = None
        
        # Add clear, readable text
        text_lines = [
            "TEST IMAGE DOCUMENT",
            "This is a test image with text",
            "for OCR testing purposes.",
            "",
            "Key Information:",
            "- Franchise Name: Coffee Express",
            "- Location: Melbourne, Australia", 
            "- Investment: $50,000 - $100,000",
            "- Contact: <EMAIL>"
        ]
        
        y_position = 50
        for line in text_lines:
            if line.strip():  # Skip empty lines
                draw.text((50, y_position), line, fill='black', font=font)
            y_position += 35
        
        img.save(tmp_file.name)
        return Path(tmp_file.name)


def test_factory_functionality():
    """Test the file handler factory"""
    print("\n🏭 Testing File Handler Factory...")
    
    try:
        factory = get_file_handler_factory()
        
        # Test supported extensions
        extensions = get_supported_extensions()
        print(f"✅ Supported extensions: {extensions}")
        
        # Test file support detection
        test_files = [
            "test.pdf",
            "test.docx", 
            "test.doc",
            "test.jpg",
            "test.png",
            "test.txt",  # Should not be supported
            "test.xyz"   # Should not be supported
        ]
        
        for filename in test_files:
            path = Path(filename)
            supported = is_supported_file(path)
            expected = path.suffix.lower() in extensions
            
            if supported == expected:
                print(f"✅ {filename}: {supported} (correct)")
            else:
                print(f"❌ {filename}: {supported} (expected {expected})")
        
        # Test handler info
        info = factory.get_handler_info()
        print(f"✅ Available handlers: {list(info.keys())}")
        
    except Exception as e:
        print(f"❌ Factory test failed: {e}")


def test_real_file_processing():
    """Test processing with real files"""
    print("\n📄 Testing Real File Processing...")
    
    test_files = []
    
    # Create test files
    pdf_file = create_test_pdf()
    if pdf_file:
        test_files.append(("PDF", pdf_file))
    
    docx_file = create_test_docx()
    if docx_file:
        test_files.append(("DOCX", docx_file))
    
    image_file = create_test_image_with_text()
    if image_file:
        test_files.append(("Image", image_file))
    
    # Process each file
    for file_type, file_path in test_files:
        try:
            print(f"\n📋 Processing {file_type} file: {file_path.name}")
            
            result = process_file(file_path)
            
            if result.success:
                print(f"✅ {file_type} processing successful")
                print(f"   Text length: {len(result.text_content)}")
                print(f"   Images extracted: {len(result.images_extracted or [])}")
                print(f"   Tables extracted: {len(result.tables_extracted or [])}")
                
                # Show preview of extracted text
                preview = result.text_content[:200].replace('\n', ' ')
                print(f"   Text preview: {preview}...")
                
            else:
                print(f"❌ {file_type} processing failed: {result.error_message}")
                
        except Exception as e:
            print(f"❌ {file_type} processing error: {e}")
        
        finally:
            # Clean up
            try:
                file_path.unlink()
            except:
                pass


def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n🔍 Testing Edge Cases...")
    
    # Test with non-existent file
    fake_file = Path("nonexistent.pdf")
    result = process_file(fake_file)
    if not result.success:
        print("✅ Non-existent file handled correctly")
    else:
        print("❌ Non-existent file should fail")
    
    # Test with empty file
    try:
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            empty_file = Path(tmp_file.name)
        
        result = process_file(empty_file)
        if not result.success:
            print("✅ Empty file handled correctly")
        else:
            print("❌ Empty file should fail")
        
        empty_file.unlink()
        
    except Exception as e:
        print(f"⚠️  Empty file test failed: {e}")
    
    # Test with unsupported file type
    try:
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as tmp_file:
            tmp_file.write(b"Some content")
            unsupported_file = Path(tmp_file.name)
        
        result = process_file(unsupported_file)
        if not result.success and "No handler available" in result.error_message:
            print("✅ Unsupported file type handled correctly")
        else:
            print("❌ Unsupported file type should fail with specific message")
        
        unsupported_file.unlink()
        
    except Exception as e:
        print(f"⚠️  Unsupported file test failed: {e}")


def main():
    """Run comprehensive file handler tests"""
    print("🚀 Comprehensive File Handler Test Suite")
    print("=" * 60)
    
    # Check environment
    print("\n📋 Environment Check:")
    
    required_packages = [
        ("PyMuPDF", "fitz"),
        ("python-docx", "docx"),
        ("Pillow", "PIL"),
        ("OpenCV", "cv2"),
        ("pytesseract", "pytesseract"),
        ("numpy", "numpy")
    ]
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} available")
        except ImportError:
            print(f"⚠️  {package_name} not available")
    
    # Run tests
    test_factory_functionality()
    test_real_file_processing()
    test_edge_cases()
    
    print("\n🎉 Comprehensive File Handler Tests Completed!")
    print("\nFile handlers are ready for:")
    print("- PDF documents with text, tables, and images")
    print("- DOCX/DOC documents with text and tables")
    print("- Image files with OCR text extraction")
    print("- Robust error handling and edge cases")


if __name__ == "__main__":
    main()
