#!/usr/bin/env python3
"""
Test Brochure-Optimized RAG Integration
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the project root to the path
sys.path.append('.')

async def test_brochure_ask_function():
    """Test the integrated brochure ask function"""
    print("🚀 Testing Integrated Brochure Ask Function")
    print("=" * 60)
    
    try:
        # Import the brochure ask function
        from docqa.serve import ask_brochure_question
        
        # Test questions optimized for brochures
        test_questions = [
            "What is Coochie Hydrogreen?",
            "What services does the company provide?",
            "Where is the company located?",
            "How can I contact the company?",
            "Is this a franchise opportunity?",
            "What makes this company different?",
            "What are the company's contact details?",
            "What areas does the company serve?",
            "What are the benefits of choosing this company?",
            "What experience does the company have?"
        ]
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}. ❓ Brochure Question: {question}")
            
            # Test with brochure-optimized parameters
            result = await ask_brochure_question(
                question=question,
                franchisor_id=franchisor_id,
                top_k=5,
                similarity_threshold=0.4,  # Lower threshold for brochures
                temperature=0.2,           # Balanced for marketing content
                include_metadata=True,
                format='json'
            )
            
            # Parse and display result
            try:
                parsed_result = json.loads(result)
                answer = parsed_result.get('answer', 'No answer')
                sources_count = len(parsed_result.get('sources', []))
                processing_time = parsed_result.get('metadata', {}).get('processing_time', 0)
                question_type = parsed_result.get('metadata', {}).get('question_type', 'unknown')
                
                print(f"   ✅ Brochure Answer ({processing_time:.2f}s, {sources_count} sources, {question_type}):")
                print(f"   {answer}")
                
                # Show top source if available
                sources = parsed_result.get('sources', [])
                if sources:
                    top_source = sources[0]
                    score = top_source.get('similarity_score', 0)
                    text_preview = top_source.get('text', '')[:100] + "..."
                    print(f"   📚 Top Source (Score: {score:.3f}): {text_preview}")
                
            except json.JSONDecodeError:
                print(f"   ✅ Answer: {result}")
        
        print(f"\n🎉 Brochure integration test completed!")
        
    except Exception as e:
        print(f"❌ Error in brochure integration test: {e}")
        import traceback
        traceback.print_exc()

async def test_brochure_rag_system():
    """Test the direct brochure RAG system"""
    print("\n🔧 Testing Direct Brochure RAG System")
    print("=" * 40)
    
    try:
        from docqa.production_integration import brochure_rag
        
        # Test question
        question = "What is Coochie Hydrogreen?"
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        print(f"Question: {question}")
        
        # Answer using brochure RAG
        result = await brochure_rag.answer_brochure_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=0.4,
            top_k=5,
            temperature=0.2
        )
        
        if result['success']:
            print(f"✅ Answer: {result['answer']}")
            print(f"📊 Processing Time: {result['metadata']['processing_time']:.2f}s")
            print(f"📊 Chunks Found: {result['metadata']['chunks_found']}")
            print(f"📊 Model Used: {result['metadata']['model_used']}")
            print(f"📊 Question Type: {result['metadata']['question_type']}")
            
            if result['sources']:
                top_source = result['sources'][0]
                print(f"📚 Top Source Score: {top_source['similarity_score']:.3f}")
        else:
            print(f"❌ Error: {result['error']}")
        
    except Exception as e:
        print(f"❌ Error in direct brochure RAG test: {e}")
        import traceback
        traceback.print_exc()

async def test_brochure_processing():
    """Test brochure processing capabilities"""
    print("\n📄 Testing Brochure Processing")
    print("=" * 35)
    
    try:
        from docqa.production_integration import brochure_rag
        
        # Sample brochure text
        sample_text = """
        Coochie Hydrogreen - Premium Lawn Care Services
        
        About Us:
        Coochie Hydrogreen is a leading franchisor in Australia specializing in professional lawn care and maintenance services.
        
        Services:
        • Lawn fertilization and treatment
        • Weed control and pest management
        • Soil analysis and improvement
        • Regular maintenance programs
        
        Contact:
        Phone: 1800-HYDROGREEN
        Email: <EMAIL>
        Website: www.coochiehydrogreen.com.au
        
        Franchise Opportunity:
        Join our growing network of successful franchisees across Australia.
        """
        
        print(f"Processing sample brochure text ({len(sample_text)} characters)")
        
        # Extract structure
        sections, metadata = brochure_rag.extract_brochure_structure(sample_text)
        
        print(f"✅ Extracted {len(sections)} sections:")
        for section in sections[:5]:  # Show first 5
            print(f"   - {section.section_type}: {section.title}")
        
        print(f"✅ Extracted metadata:")
        print(f"   - Company: {metadata.company_name}")
        print(f"   - Industry: {metadata.industry}")
        print(f"   - Location: {metadata.location}")
        print(f"   - Services: {len(metadata.services)} identified")
        print(f"   - Contact Info: {len(metadata.contact_info)} items")
        
        # Process into chunks
        chunks = brochure_rag.process_brochure(sample_text, sections, metadata)
        print(f"✅ Created {len(chunks)} brochure chunks")
        
    except Exception as e:
        print(f"❌ Error in brochure processing test: {e}")
        import traceback
        traceback.print_exc()

async def test_health_check():
    """Test the health check with brochure system"""
    print("\n🔍 Testing Health Check with Brochure System")
    print("=" * 45)
    
    try:
        from docqa.serve import health_check
        
        health_status = health_check()
        
        print("Health Check Results:")
        print(json.dumps(health_status, indent=2))
        
        # Check if brochure components are healthy
        status = health_status.get('status', 'unknown')
        if status == 'healthy':
            print("✅ Brochure RAG system is healthy")
        else:
            print("❌ Brochure RAG system has issues")
        
    except Exception as e:
        print(f"❌ Error in health check: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all brochure integration tests"""
    print("🚀 Brochure-Optimized RAG Integration Tests")
    print("=" * 60)
    
    # Run tests
    await test_brochure_ask_function()
    await test_brochure_rag_system()
    await test_brochure_processing()
    await test_health_check()
    
    print("\n✅ All brochure integration tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
