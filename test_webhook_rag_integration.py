#!/usr/bin/env python3
"""
Test Webhook RAG Integration
Test the integration of brochure-optimized RAG system with webhook endpoints
"""

import asyncio
import sys
import json
import httpx
from datetime import datetime
from typing import Dict, Any

# Add the project root to the path
sys.path.append('.')

async def test_webhook_health():
    """Test the webhook health endpoint"""
    print("🔍 Testing Webhook Health Endpoint")
    print("=" * 40)
    
    try:
        # Import the health function directly
        from app.api.v1.endpoints.webhooks import webhook_health
        
        # Call the health check
        health_response = await webhook_health()
        
        print("✅ Webhook Health Check Results:")
        print(f"   Status: {health_response.status}")
        print(f"   QnA Available: {health_response.qna_available}")
        print(f"   Timestamp: {health_response.timestamp}")
        
        if health_response.details:
            print("   Details:")
            for key, value in health_response.details.items():
                print(f"     {key}: {value}")
        
        return health_response.qna_available
        
    except Exception as e:
        print(f"❌ Error testing webhook health: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_generate_ai_answer():
    """Test the generate_ai_answer function"""
    print("\n🤖 Testing Generate AI Answer Function")
    print("=" * 45)
    
    try:
        # Import the function
        from app.api.v1.endpoints.webhooks import generate_ai_answer
        
        # Test questions for brochure content
        test_questions = [
            "What is Coochie Hydrogreen?",
            "What services does the company provide?",
            "Where is the company located?",
            "How can I contact the company?",
            "Is this a franchise opportunity?"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}. ❓ Question: {question}")
            
            # Generate AI answer
            result = await generate_ai_answer(question)
            
            if result.get('success'):
                answer = result.get('answer', 'No answer')
                system_info = result.get('metadata', {}).get('system_info', {})
                rag_type = system_info.get('rag_type', 'unknown')
                
                print(f"   ✅ Answer ({rag_type} RAG): {answer}")
                
                # Show metadata if available
                if 'metadata' in result:
                    metadata = result['metadata']
                    if 'processing_time' in metadata:
                        print(f"   ⏱️  Processing Time: {metadata['processing_time']:.2f}s")
                    if 'chunks_found' in metadata:
                        print(f"   📊 Chunks Found: {metadata['chunks_found']}")
            else:
                error = result.get('error', 'Unknown error')
                print(f"   ❌ Error: {error}")
        
        print("\n✅ Generate AI Answer test completed")
        
    except Exception as e:
        print(f"❌ Error testing generate AI answer: {e}")
        import traceback
        traceback.print_exc()

async def test_process_inbound_message():
    """Test the process_inbound_message function"""
    print("\n📨 Testing Process Inbound Message Function")
    print("=" * 50)
    
    try:
        # Import the function
        from app.api.v1.endpoints.webhooks import process_inbound_message
        
        # Test messages
        test_messages = [
            "What is Coochie Hydrogreen?",
            "Tell me about your services",
            "How do I contact you?",
            "Are you a franchise?",
            "Where are you located?"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}. 📱 Message: {message}")
            
            # Process inbound message
            result = await process_inbound_message(message)
            
            if result.get('success'):
                answer = result.get('answer', 'No answer')
                
                print(f"   ✅ Response: {answer}")
                
                # Show franchisor info
                franchisor_info = result.get('metadata', {}).get('franchisor_info', {})
                if franchisor_info:
                    print(f"   🏢 Franchisor: {franchisor_info.get('franchisor_name', 'Unknown')}")
                    print(f"   🎯 Detection: {franchisor_info.get('detection_method', 'Unknown')}")
                
                # Show processing info
                processing_info = result.get('metadata', {}).get('processing_info', {})
                if processing_info:
                    print(f"   ⚙️  Method: {processing_info.get('processing_method', 'Unknown')}")
            else:
                error = result.get('error', 'Unknown error')
                print(f"   ❌ Error: {error}")
        
        print("\n✅ Process Inbound Message test completed")
        
    except Exception as e:
        print(f"❌ Error testing process inbound message: {e}")
        import traceback
        traceback.print_exc()

async def simulate_webhook_request():
    """Simulate a webhook request"""
    print("\n🔗 Simulating Webhook Request")
    print("=" * 35)
    
    try:
        # Create a sample webhook payload
        webhook_payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": datetime.now().isoformat(),
            "mo": {
                "id": "test-message-123",
                "sender": "+61400000000",
                "recipient": "+61400000001",
                "message": "What services does Coochie Hydrogreen provide?",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        print(f"📤 Webhook Payload:")
        print(json.dumps(webhook_payload, indent=2))
        
        # Import and test the webhook processing logic
        from app.api.v1.endpoints.webhooks import process_inbound_message
        
        # Process the message
        message = webhook_payload["mo"]["message"]
        result = await process_inbound_message(message)
        
        print(f"\n📥 Webhook Response:")
        if result.get('success'):
            print(f"✅ Success: {result.get('answer', 'No answer')}")
            
            # Show metadata
            metadata = result.get('metadata', {})
            if metadata:
                print(f"📊 Metadata:")
                for key, value in metadata.items():
                    if isinstance(value, dict):
                        print(f"   {key}:")
                        for sub_key, sub_value in value.items():
                            print(f"     {sub_key}: {sub_value}")
                    else:
                        print(f"   {key}: {value}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
        
        print("\n✅ Webhook simulation completed")
        
    except Exception as e:
        print(f"❌ Error simulating webhook request: {e}")
        import traceback
        traceback.print_exc()

async def test_rag_system_availability():
    """Test RAG system availability"""
    print("\n🧠 Testing RAG System Availability")
    print("=" * 40)
    
    try:
        # Test brochure-optimized RAG
        print("Testing brochure-optimized RAG...")
        try:
            from docqa import ask_brochure_question
            
            result = await ask_brochure_question(
                question="What is Coochie Hydrogreen?",
                franchisor_id="569976f2-d845-4615-8a91-96e18086adbe",
                format='json'
            )
            
            print("✅ Brochure-optimized RAG is available")
            
            # Parse result
            try:
                parsed_result = json.loads(result)
                if parsed_result.get('success'):
                    print(f"   Answer: {parsed_result.get('answer', 'No answer')[:100]}...")
                else:
                    print(f"   Error: {parsed_result.get('error', 'Unknown error')}")
            except json.JSONDecodeError:
                print(f"   Answer: {result[:100]}...")
                
        except Exception as e:
            print(f"❌ Brochure-optimized RAG not available: {e}")
        
        # Test standard RAG
        print("\nTesting standard RAG...")
        try:
            from docqa.central_api import ask_question
            
            result = await ask_question({
                "question": "What is Coochie Hydrogreen?",
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
                "top_k": 5
            })
            
            print("✅ Standard RAG is available")
            
            if result.get('success'):
                print(f"   Answer: {result.get('answer', 'No answer')[:100]}...")
            else:
                print(f"   Error: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Standard RAG not available: {e}")
        
        print("\n✅ RAG system availability test completed")
        
    except Exception as e:
        print(f"❌ Error testing RAG system availability: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all webhook RAG integration tests"""
    print("🚀 Webhook RAG Integration Tests")
    print("=" * 50)
    
    # Test RAG system availability first
    await test_rag_system_availability()
    
    # Test webhook health
    health_ok = await test_webhook_health()
    
    if health_ok:
        # Test AI answer generation
        await test_generate_ai_answer()
        
        # Test inbound message processing
        await test_process_inbound_message()
        
        # Simulate webhook request
        await simulate_webhook_request()
    else:
        print("\n⚠️  Skipping detailed tests due to health check failure")
    
    print("\n🎉 All webhook RAG integration tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
