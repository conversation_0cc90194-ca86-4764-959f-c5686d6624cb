#!/usr/bin/env python3
"""
DocQA CLI - Question Answering on Documents stored on AWS S3

This CLI tool provides functionality to:
1. Ingest documents from AWS S3 and create vector embeddings
2. Answer questions based on the ingested documents using RAG

Usage:
    poetry run python -m app.cli.main ingest s3://bucket/document.pdf
    poetry run python -m app.cli.main ask "What is the refund policy?"
"""

import os
import sys
from pathlib import Path
from typing import Optional

import typer
from dotenv import load_dotenv
import structlog

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.cli.services.ingestion_service import IngestionService
from app.cli.services.qa_service import QAService

# Load environment variables
load_dotenv()

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Create Typer app
app = typer.Typer(
    name="docqa",
    help="DocQA CLI - Question Answering on Documents stored on AWS S3",
    add_completion=False,
)


@app.command()
def ingest(
    s3_url: str = typer.Argument(..., help="S3 URL of the document to ingest (e.g., s3://bucket/document.pdf)"),
    force: bool = typer.Option(False, "--force", "-f", help="Force re-ingestion even if document already exists"),
    comprehensive: bool = typer.Option(False, "--comprehensive", "-c", help="Enable comprehensive analysis (images, charts, graphs)"),
    chunk_size: Optional[int] = typer.Option(None, "--chunk-size", help="Override default chunk size (default: 400)"),
    chunk_overlap: Optional[int] = typer.Option(None, "--chunk-overlap", help="Override default chunk overlap (default: 50)"),
) -> None:
    """
    Ingest a document from AWS S3 and create vector embeddings.

    BASIC MODE (default):
    1. Download the document from S3
    2. Extract text content (supports PDF, DOC, DOCX, images)
    3. Chunk the text into manageable pieces
    4. Generate embeddings using OpenAI
    5. Store vectors in FAISS index

    COMPREHENSIVE MODE (--comprehensive):
    1. All basic mode features PLUS:
    2. Analyze every image, chart, and graph using OpenAI Vision
    3. Extract data from tables and visual elements
    4. Understand pictures, diagrams, and infographics
    5. Include ALL visual information in searchable content

    Use --comprehensive for documents with important visual elements!
    """
    try:
        logger.info("Starting document ingestion", s3_url=s3_url)
        
        # Validate S3 URL format
        if not s3_url.startswith("s3://"):
            typer.echo("❌ Error: S3 URL must start with 's3://'", err=True)
            raise typer.Exit(1)
        
        # Initialize ingestion service
        ingestion_service = IngestionService()
        
        # Set custom parameters if provided
        if chunk_size:
            ingestion_service.chunk_size = chunk_size
        if chunk_overlap:
            ingestion_service.chunk_overlap = chunk_overlap
        
        # Start ingestion process
        if comprehensive:
            typer.echo(f"🧠 Starting COMPREHENSIVE ingestion of: {s3_url}")
            typer.echo("   📊 Will analyze: Text + Images + Charts + Graphs + Tables")
            typer.echo("   ⏳ This may take 30-60 seconds for visual analysis...")
        else:
            typer.echo(f"🚀 Starting basic ingestion of: {s3_url}")
            typer.echo("   📄 Will analyze: Text content only")
            typer.echo("   💡 Use --comprehensive for images/charts analysis")

        result = ingestion_service.ingest_document(s3_url, force=force, comprehensive=comprehensive)
        
        if result["success"]:
            typer.echo("✅ Successfully ingested document!")
            typer.echo(f"   📄 Document: {result['document_name']}")
            typer.echo(f"   📊 Chunks created: {result['chunks_count']}")
            typer.echo(f"   🔍 Embeddings generated: {result['embeddings_count']}")
            typer.echo(f"   💾 Index updated: {result['index_path']}")
        else:
            typer.echo(f"❌ Ingestion failed: {result['error']}", err=True)
            raise typer.Exit(1)
            
    except Exception as e:
        logger.error("Ingestion failed", error=str(e), s3_url=s3_url)
        typer.echo(f"❌ Ingestion failed: {str(e)}", err=True)
        raise typer.Exit(1)


@app.command()
def ask(
    question: str = typer.Argument(..., help="Question to ask about the ingested documents"),
    top_k: Optional[int] = typer.Option(None, "--top-k", help="Number of relevant chunks to retrieve (default: 6)"),
    model: Optional[str] = typer.Option(None, "--model", help="OpenAI model to use (default: gpt-4-turbo)"),
    max_tokens: Optional[int] = typer.Option(None, "--max-tokens", help="Maximum tokens in response (default: 1000)"),
    stream: bool = typer.Option(True, "--stream/--no-stream", help="Stream the response (default: True)"),
) -> None:
    """
    Ask a question about the ingested documents.
    
    This command will:
    1. Load the FAISS vector index
    2. Retrieve relevant document chunks
    3. Generate a response using OpenAI GPT
    4. Stream the answer to the console
    """
    try:
        logger.info("Processing question", question=question)
        
        # Initialize QA service
        qa_service = QAService()
        
        # Set custom parameters if provided
        if top_k:
            qa_service.top_k = top_k
        if model:
            qa_service.model = model
        if max_tokens:
            qa_service.max_tokens = max_tokens
        
        # Check if index exists
        if not qa_service.index_exists():
            typer.echo("❌ No documents have been ingested yet. Please run 'docqa ingest' first.", err=True)
            raise typer.Exit(1)
        
        typer.echo(f"🤔 Question: {question}")
        typer.echo("🔍 Searching relevant documents...")
        
        # Get answer
        if stream:
            typer.echo("💭 Answer:")
            typer.echo("-" * 50)
            
            for chunk in qa_service.ask_streaming(question):
                typer.echo(chunk, nl=False)
            
            typer.echo("\n" + "-" * 50)
        else:
            answer = qa_service.ask(question)
            typer.echo("💭 Answer:")
            typer.echo("-" * 50)
            typer.echo(answer)
            typer.echo("-" * 50)
            
    except Exception as e:
        logger.error("Question answering failed", error=str(e), question=question)
        typer.echo(f"❌ Failed to answer question: {str(e)}", err=True)
        raise typer.Exit(1)


@app.command()
def status() -> None:
    """Show the current status of the DocQA system."""
    try:
        qa_service = QAService()
        
        typer.echo("📊 DocQA System Status")
        typer.echo("=" * 30)
        
        if qa_service.index_exists():
            stats = qa_service.get_index_stats()
            typer.echo("✅ FAISS Index: Available")
            typer.echo(f"📄 Documents indexed: {stats['document_count']}")
            typer.echo(f"📊 Total chunks: {stats['chunk_count']}")
            typer.echo(f"📅 Last updated: {stats['last_updated']}")
            typer.echo(f"💾 Index size: {stats['index_size']}")
        else:
            typer.echo("❌ FAISS Index: Not found")
            typer.echo("💡 Run 'docqa ingest <s3-url>' to create the index")
        
        # Check OpenAI API key
        if os.getenv("OPENAI_API_KEY"):
            typer.echo("✅ OpenAI API Key: Configured")
        else:
            typer.echo("❌ OpenAI API Key: Not configured")
            typer.echo("💡 Set OPENAI_API_KEY environment variable")
        
        # Check AWS credentials
        if os.getenv("AWS_ACCESS_KEY_ID") or os.path.exists(os.path.expanduser("~/.aws/credentials")):
            typer.echo("✅ AWS Credentials: Available")
        else:
            typer.echo("❌ AWS Credentials: Not configured")
            typer.echo("💡 Configure AWS credentials or set AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY")
            
    except Exception as e:
        logger.error("Status check failed", error=str(e))
        typer.echo(f"❌ Status check failed: {str(e)}", err=True)
        raise typer.Exit(1)


@app.command()
def clear() -> None:
    """Clear the FAISS index and all ingested documents."""
    confirm = typer.confirm("Are you sure you want to clear all ingested documents?")
    if not confirm:
        typer.echo("Operation cancelled.")
        return
    
    try:
        qa_service = QAService()
        qa_service.clear_index()
        typer.echo("✅ Successfully cleared all ingested documents.")
        
    except Exception as e:
        logger.error("Clear operation failed", error=str(e))
        typer.echo(f"❌ Clear operation failed: {str(e)}", err=True)
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
