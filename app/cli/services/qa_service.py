"""
Question Answering Service for DocQA CLI

This service handles:
- Loading FAISS index and retrieving relevant chunks
- Creating RAG prompts with question + context
- Calling OpenAI GPT-4-Turbo for answer generation
- Streaming responses to the CLI
"""

import os
from typing import List, Dict, Any, Iterator, Optional
import structlog

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from app.cli.services.vector_store import VectorStore

logger = structlog.get_logger()


class QAService:
    """Service for question answering using RAG (Retrieval Augmented Generation)."""
    
    def __init__(self):
        self.top_k = int(os.getenv("TOP_K_RETRIEVAL", "6"))
        self.model = os.getenv("OPENAI_MODEL", "gpt-4-turbo")
        self.max_tokens = int(os.getenv("MAX_TOKENS_RESPONSE", "1000"))
        self.temperature = 0.1  # Low temperature for factual responses
        
        # OpenAI configuration
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        if not OPENAI_AVAILABLE:
            raise ValueError("OpenAI library is required. Install with: pip install openai")
        
        # Initialize OpenAI client
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        
        # Initialize vector store
        data_dir = os.getenv("FAISS_INDEX_PATH", "./data")
        self.vector_store = VectorStore(data_dir)
    
    def ask(self, question: str) -> str:
        """
        Ask a question and get a complete answer.
        
        Args:
            question: The question to ask
            
        Returns:
            The complete answer as a string
        """
        try:
            logger.info("Processing question", question=question)
            
            # Retrieve relevant documents
            relevant_docs = self.vector_store.search(question, top_k=self.top_k)
            
            if not relevant_docs:
                return "I couldn't find any relevant information to answer your question. Please make sure documents have been ingested using 'docqa ingest'."
            
            # Create RAG prompt
            prompt = self._create_rag_prompt(question, relevant_docs)
            
            # Get response from OpenAI
            response = self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                stream=False
            )
            
            answer = response.choices[0].message.content
            
            logger.info("Question answered successfully", 
                       question_length=len(question),
                       answer_length=len(answer),
                       relevant_docs_count=len(relevant_docs))
            
            return answer
            
        except Exception as e:
            logger.error("Failed to answer question", question=question, error=str(e))
            raise ValueError(f"Failed to answer question: {str(e)}")
    
    def ask_streaming(self, question: str) -> Iterator[str]:
        """
        Ask a question and get a streaming answer.
        
        Args:
            question: The question to ask
            
        Yields:
            Chunks of the answer as they are generated
        """
        try:
            logger.info("Processing streaming question", question=question)
            
            # Retrieve relevant documents
            relevant_docs = self.vector_store.search(question, top_k=self.top_k)
            
            if not relevant_docs:
                yield "I couldn't find any relevant information to answer your question. Please make sure documents have been ingested using 'docqa ingest'."
                return
            
            # Create RAG prompt
            prompt = self._create_rag_prompt(question, relevant_docs)
            
            # Get streaming response from OpenAI
            stream = self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                stream=True
            )
            
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
            
            logger.info("Streaming question answered successfully", 
                       question_length=len(question),
                       relevant_docs_count=len(relevant_docs))
            
        except Exception as e:
            logger.error("Failed to answer streaming question", question=question, error=str(e))
            yield f"Error: Failed to answer question: {str(e)}"
    
    def _create_rag_prompt(self, question: str, relevant_docs: List[Dict[str, Any]]) -> str:
        """Create a RAG prompt with question and relevant context."""
        
        # Build context from relevant documents
        context_parts = []
        for i, doc in enumerate(relevant_docs, 1):
            doc_name = doc.get("document_name", "Unknown Document")
            text = doc.get("text", "")
            score = doc.get("score", 0.0)
            
            context_parts.append(f"[Document {i}: {doc_name} (Relevance: {score:.3f})]")
            context_parts.append(text)
            context_parts.append("")  # Empty line for separation
        
        context = "\n".join(context_parts)
        
        # Create the full prompt
        prompt = f"""Based on the following context from the documents, please answer the question. If the answer cannot be found in the provided context, please say so clearly.

CONTEXT:
{context}

QUESTION: {question}

ANSWER:"""
        
        return prompt
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the AI assistant."""
        return """You are a helpful AI assistant that answers questions based on provided document context. 

Instructions:
1. Answer questions accurately based only on the provided context
2. If the answer is not in the context, clearly state that you cannot find the information
3. Be concise but comprehensive in your responses
4. If multiple documents contain relevant information, synthesize the information appropriately
5. Always cite which document(s) your answer comes from when possible
6. If there are conflicting information in different documents, mention this
7. Use a professional and helpful tone

Remember: Only use information from the provided context. Do not use your general knowledge to answer questions about specific documents."""
    
    def index_exists(self) -> bool:
        """Check if the FAISS index exists and has data."""
        return self.vector_store.exists()
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get statistics about the current index."""
        return self.vector_store.get_stats()
    
    def clear_index(self) -> None:
        """Clear the FAISS index and all data."""
        self.vector_store.clear_index()
    
    def get_relevant_documents(self, question: str, top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get relevant documents for a question without generating an answer.
        
        Args:
            question: The question to search for
            top_k: Number of documents to retrieve (uses default if None)
            
        Returns:
            List of relevant documents with metadata
        """
        search_k = top_k if top_k is not None else self.top_k
        return self.vector_store.search(question, top_k=search_k)
    
    def test_connection(self) -> Dict[str, bool]:
        """Test connections to required services."""
        results = {
            "openai_api": False,
            "vector_store": False,
        }
        
        # Test OpenAI API
        try:
            response = self.openai_client.models.list()
            results["openai_api"] = True
        except Exception as e:
            logger.error("OpenAI API test failed", error=str(e))
        
        # Test vector store
        try:
            results["vector_store"] = self.vector_store.exists()
        except Exception as e:
            logger.error("Vector store test failed", error=str(e))
        
        return results
