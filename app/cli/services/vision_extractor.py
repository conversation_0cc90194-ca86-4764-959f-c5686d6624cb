"""
Enhanced Vision-based Text and Graph Extraction Service

This service extends the basic text extractor with advanced capabilities:
- OpenAI Vision API for chart/graph analysis
- Image-based document processing
- Chart data extraction and interpretation
- Visual element detection and description
"""

import os
import base64
from pathlib import Path
from typing import List, Dict, Any, Optional
import structlog
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import time

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from PIL import Image
    import fitz  # PyMuPDF for PDF image extraction
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from app.cli.services.text_extractor import TextExtractor

logger = structlog.get_logger()


class VisionExtractor(TextExtractor):
    """Enhanced text extractor with vision capabilities for graphs and charts."""
    
    def __init__(self):
        super().__init__()
        
        # OpenAI Vision configuration
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.vision_model = os.getenv("OPENAI_VISION_MODEL", "gpt-4o")
        
        if not self.openai_api_key:
            logger.warning("OpenAI API key not found. Vision capabilities disabled.")
            self.vision_available = False
        elif not OPENAI_AVAILABLE:
            logger.warning("OpenAI library not available. Vision capabilities disabled.")
            self.vision_available = False
        else:
            self.vision_available = True
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
            logger.info("Vision capabilities enabled", model=self.vision_model)
    
    def extract_text_with_vision(self, file_path: str, original_filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract text and analyze visual elements including graphs and charts.
        
        Returns:
            Dictionary containing:
            - text: Extracted text content
            - visual_elements: List of detected charts/graphs with descriptions
            - chart_data: Extracted data from charts (if any)
        """
        result = {
            "text": "",
            "visual_elements": [],
            "chart_data": [],
            "processing_notes": []
        }
        
        try:
            # First, extract text using standard method
            result["text"] = self.extract_text(file_path, original_filename)
            result["processing_notes"].append("Text extraction completed")
            
            # Then, analyze visual elements if vision is available
            if self.vision_available:
                visual_analysis = self._analyze_visual_elements(file_path, original_filename)
                result.update(visual_analysis)
            else:
                result["processing_notes"].append("Vision analysis skipped - not available")
            
            return result
            
        except Exception as e:
            logger.error("Enhanced extraction failed", error=str(e), file_path=file_path)
            # Fallback to basic text extraction
            try:
                result["text"] = self.extract_text(file_path, original_filename)
                result["processing_notes"].append(f"Vision analysis failed: {str(e)}")
            except Exception as fallback_error:
                result["processing_notes"].append(f"All extraction failed: {str(fallback_error)}")
            
            return result
    
    def _analyze_visual_elements(self, file_path: str, original_filename: Optional[str] = None) -> Dict[str, Any]:
        """Analyze visual elements in the document using OpenAI Vision API."""
        
        file_path = Path(file_path)
        file_extension = Path(original_filename).suffix.lower() if original_filename else file_path.suffix.lower()
        
        visual_result = {
            "visual_elements": [],
            "chart_data": [],
            "processing_notes": []
        }
        
        try:
            if file_extension == '.pdf':
                # Extract images from PDF and analyze in parallel
                start_time = time.time()
                logger.info("Starting parallel image extraction and analysis")

                images = self._extract_images_from_pdf(file_path)
                logger.info(f"Extracted {len(images)} images in {time.time() - start_time:.2f}s")

                if images:
                    # Process images in parallel for speed
                    visual_result["visual_elements"] = self._analyze_images_parallel(images)

                logger.info(f"Total vision analysis completed in {time.time() - start_time:.2f}s")
                        
            elif file_extension in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']:
                # Analyze the image directly
                with open(file_path, 'rb') as f:
                    image_data = f.read()
                analysis = self._analyze_image_with_vision(image_data, str(file_path))
                if analysis:
                    visual_result["visual_elements"].append(analysis)
            
            # Extract structured data from visual elements
            visual_result["chart_data"] = self._extract_chart_data(visual_result["visual_elements"])
            visual_result["processing_notes"].append(f"Analyzed {len(visual_result['visual_elements'])} visual elements")
            
        except Exception as e:
            logger.error("Visual analysis failed", error=str(e))
            visual_result["processing_notes"].append(f"Visual analysis error: {str(e)}")
        
        return visual_result

    def _analyze_images_parallel(self, images: List[bytes], max_workers: int = 6) -> List[Dict[str, Any]]:
        """Analyze multiple images in parallel for faster processing."""
        visual_elements = []

        if not images:
            return visual_elements

        logger.info(f"Starting parallel analysis of {len(images)} images with {max_workers} workers")

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all image analysis tasks
            future_to_index = {
                executor.submit(self._analyze_image_with_vision, image_data, f"Image {i+1}"): i
                for i, image_data in enumerate(images)
            }

            # Collect results as they complete
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    analysis = future.result(timeout=30)  # 30 second timeout per image
                    if analysis:
                        # Add index for ordering
                        analysis['image_index'] = index
                        visual_elements.append(analysis)
                        logger.debug(f"Completed analysis for image {index + 1}")
                except Exception as e:
                    logger.warning(f"Failed to analyze image {index + 1}: {str(e)}")

        # Sort by original index to maintain order
        visual_elements.sort(key=lambda x: x.get('image_index', 0))

        logger.info(f"Parallel analysis completed: {len(visual_elements)} successful analyses")
        return visual_elements

    def _extract_images_from_pdf(self, pdf_path: Path) -> List[bytes]:
        """Extract images from PDF pages."""
        images = []
        
        try:
            # Open PDF
            pdf_document = fitz.open(str(pdf_path))
            total_pages = len(pdf_document)

            # Limit to first 10 pages for speed (most important content usually at start)
            max_pages = min(10, total_pages)
            logger.info(f"Processing {max_pages}/{total_pages} pages for faster extraction")

            for page_num in range(max_pages):
                page = pdf_document[page_num]

                # Extract embedded images first (usually higher quality)
                image_list = page.get_images()
                page_has_images = False

                for img in image_list:
                    try:
                        xref = img[0]
                        base_image = pdf_document.extract_image(xref)
                        image_bytes = base_image["image"]

                        # Skip very small images (likely decorative)
                        if len(image_bytes) > 5000:  # 5KB minimum
                            images.append(image_bytes)
                            page_has_images = True

                    except Exception as e:
                        logger.debug("Skipped embedded image", error=str(e))

                # If no embedded images found, convert page to image (lower resolution for speed)
                if not page_has_images:
                    try:
                        mat = fitz.Matrix(1.2, 1.2)  # Reduced from 2x to 1.2x for speed
                        pix = page.get_pixmap(matrix=mat)
                        img_data = pix.tobytes("png")
                        images.append(img_data)
                    except Exception as e:
                        logger.debug(f"Failed to convert page {page_num} to image", error=str(e))

            pdf_document.close()

            # Limit total images for speed (most relevant ones first)
            max_images = 12
            if len(images) > max_images:
                logger.info(f"Limiting to first {max_images} images for faster processing")
                images = images[:max_images]
            
        except Exception as e:
            logger.error("PDF image extraction failed", error=str(e))
        
        return images
    
    def _analyze_image_with_vision(self, image_data: bytes, source: str) -> Optional[Dict[str, Any]]:
        """Analyze a single image using OpenAI Vision API."""
        
        try:
            # Encode image to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            # Create optimized vision prompt for speed
            prompt = f"""
            Extract key information from this document image ({source}):

            1. TEXT: All visible text, headings, numbers, financial data
            2. CHARTS/GRAPHS: Describe any visual data with values
            3. TABLES: Extract table content and structure
            4. KEY DETAILS: Business info, costs, services, contact details

            Provide clear, searchable text covering all visible content.
            Be concise but thorough - focus on factual information.
            """
            
            # Call OpenAI Vision API
            response = self.openai_client.chat.completions.create(
                model=self.vision_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500,  # Reduced for faster processing
                temperature=0.1
            )
            
            # Parse response
            content = response.choices[0].message.content
            
            # Try to parse as JSON, fallback to structured text description
            try:
                import json
                analysis = json.loads(content)
                analysis["source"] = source
                analysis["has_visual_data"] = True  # Assume true if we got a response
                return analysis
            except json.JSONDecodeError:
                # Fallback to structured text description
                return {
                    "has_visual_data": True,
                    "source": source,
                    "page_content": {
                        "text_content": content,
                        "headings": [],
                        "key_points": []
                    },
                    "visual_elements": [{
                        "type": "mixed_content",
                        "title": "Page Analysis",
                        "description": content,
                        "data_extracted": {
                            "values": [],
                            "labels": [],
                            "trends": content
                        },
                        "text_in_element": content
                    }],
                    "tables": [],
                    "financial_data": {
                        "currencies": [],
                        "percentages": [],
                        "metrics": []
                    },
                    "overall_summary": content
                }
                
        except Exception as e:
            logger.error("Vision API analysis failed", error=str(e), source=source)
            return None
    
    def _extract_chart_data(self, visual_elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract comprehensive structured data from visual analysis."""

        all_data = []

        for element in visual_elements:
            if not element.get("has_visual_data", False):
                continue

            source = element.get("source", "unknown")

            # Extract page content
            page_content = element.get("page_content", {})
            if page_content.get("text_content"):
                all_data.append({
                    "source": source,
                    "type": "page_text",
                    "title": "Page Text Content",
                    "description": page_content.get("text_content", ""),
                    "headings": page_content.get("headings", []),
                    "key_points": page_content.get("key_points", []),
                    "searchable_text": self._create_page_searchable_text(page_content)
                })

            # Extract visual elements
            for visual in element.get("visual_elements", []):
                data_entry = {
                    "source": source,
                    "type": visual.get("type", "unknown"),
                    "title": visual.get("title", ""),
                    "description": visual.get("description", ""),
                    "data_extracted": visual.get("data_extracted", {}),
                    "text_in_element": visual.get("text_in_element", ""),
                    "searchable_text": self._create_visual_searchable_text(visual)
                }
                all_data.append(data_entry)

            # Extract table data
            for table in element.get("tables", []):
                table_entry = {
                    "source": source,
                    "type": "table",
                    "title": table.get("title", ""),
                    "headers": table.get("headers", []),
                    "data": table.get("data", []),
                    "summary": table.get("summary", ""),
                    "searchable_text": self._create_table_searchable_text(table)
                }
                all_data.append(table_entry)

            # Extract financial data
            financial = element.get("financial_data", {})
            if any(financial.values()):
                financial_entry = {
                    "source": source,
                    "type": "financial_data",
                    "title": "Financial Information",
                    "currencies": financial.get("currencies", []),
                    "percentages": financial.get("percentages", []),
                    "metrics": financial.get("metrics", []),
                    "searchable_text": self._create_financial_searchable_text(financial)
                }
                all_data.append(financial_entry)

            # Add overall summary
            if element.get("overall_summary"):
                all_data.append({
                    "source": source,
                    "type": "page_summary",
                    "title": "Page Summary",
                    "description": element.get("overall_summary", ""),
                    "searchable_text": f"Page summary: {element.get('overall_summary', '')}"
                })

        return all_data
    
    def _create_page_searchable_text(self, page_content: Dict[str, Any]) -> str:
        """Create searchable text from page content."""
        parts = []

        if page_content.get("text_content"):
            parts.append(f"Page text: {page_content['text_content']}")

        if page_content.get("headings"):
            parts.append(f"Headings: {', '.join(page_content['headings'])}")

        if page_content.get("key_points"):
            parts.append(f"Key points: {', '.join(page_content['key_points'])}")

        return " | ".join(parts)

    def _create_visual_searchable_text(self, visual: Dict[str, Any]) -> str:
        """Create searchable text from visual elements."""
        parts = []

        if visual.get("title"):
            parts.append(f"Visual title: {visual['title']}")

        if visual.get("type"):
            parts.append(f"Visual type: {visual['type']}")

        if visual.get("description"):
            parts.append(f"Description: {visual['description']}")

        if visual.get("text_in_element"):
            parts.append(f"Text in visual: {visual['text_in_element']}")

        data_extracted = visual.get("data_extracted", {})
        if data_extracted.get("values"):
            parts.append(f"Values: {', '.join(map(str, data_extracted['values']))}")

        if data_extracted.get("labels"):
            parts.append(f"Labels: {', '.join(data_extracted['labels'])}")

        if data_extracted.get("trends"):
            parts.append(f"Trends: {data_extracted['trends']}")

        return " | ".join(parts)

    def _create_table_searchable_text(self, table: Dict[str, Any]) -> str:
        """Create searchable text from table data."""
        parts = []

        if table.get("title"):
            parts.append(f"Table title: {table['title']}")

        if table.get("headers"):
            parts.append(f"Table headers: {', '.join(table['headers'])}")

        if table.get("data"):
            # Flatten table data for searching
            flat_data = []
            for row in table['data']:
                if isinstance(row, list):
                    flat_data.extend([str(cell) for cell in row])
                else:
                    flat_data.append(str(row))
            parts.append(f"Table data: {', '.join(flat_data)}")

        if table.get("summary"):
            parts.append(f"Table summary: {table['summary']}")

        return " | ".join(parts)

    def _create_financial_searchable_text(self, financial: Dict[str, Any]) -> str:
        """Create searchable text from financial data."""
        parts = []

        if financial.get("currencies"):
            parts.append(f"Currencies: {', '.join(financial['currencies'])}")

        if financial.get("percentages"):
            parts.append(f"Percentages: {', '.join(financial['percentages'])}")

        if financial.get("metrics"):
            parts.append(f"Metrics: {', '.join(financial['metrics'])}")

        return " | ".join(parts)
    
    def get_enhanced_capabilities(self) -> Dict[str, bool]:
        """Get information about available capabilities."""
        return {
            "basic_text_extraction": True,
            "ocr_available": self.ocr_available,
            "vision_api_available": self.vision_available,
            "pdf_image_extraction": PIL_AVAILABLE,
            "chart_analysis": self.vision_available and PIL_AVAILABLE,
            "graph_data_extraction": self.vision_available
        }
