"""
Text Chunking Service for DocQA CLI

This service handles intelligent text chunking with:
- Token-based chunking using tiktoken
- Configurable chunk size and overlap
- Preservation of sentence boundaries
- Metadata tracking for each chunk
"""

import re
from typing import List
import structlog

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False

logger = structlog.get_logger()


class TextChunker:
    """Service for chunking text into manageable pieces for embedding."""
    
    def __init__(self, chunk_size: int = 400, chunk_overlap: int = 50, model_name: str = "gpt-3.5-turbo"):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.model_name = model_name
        
        # Initialize tokenizer
        if TIKTOKEN_AVAILABLE:
            try:
                self.tokenizer = tiktoken.encoding_for_model(model_name)
                self.use_tiktoken = True
                logger.info("Using tiktoken for tokenization", model=model_name)
            except Exception as e:
                logger.warning("Failed to initialize tiktoken, falling back to character-based chunking", error=str(e))
                self.use_tiktoken = False
        else:
            logger.warning("tiktoken not available, using character-based chunking")
            self.use_tiktoken = False
        
        # Sentence boundary patterns
        self.sentence_endings = re.compile(r'[.!?]+\s+')
        self.paragraph_breaks = re.compile(r'\n\s*\n')
    
    def chunk_text(self, text: str) -> List[str]:
        """
        Chunk text into pieces of approximately chunk_size tokens.
        
        Args:
            text: Input text to chunk
            
        Returns:
            List of text chunks
        """
        if not text.strip():
            return []
        
        logger.info("Chunking text", 
                   text_length=len(text),
                   chunk_size=self.chunk_size,
                   chunk_overlap=self.chunk_overlap,
                   use_tiktoken=self.use_tiktoken)
        
        # Clean and normalize text
        text = self._clean_text(text)
        
        if self.use_tiktoken:
            return self._chunk_by_tokens(text)
        else:
            return self._chunk_by_characters(text)
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove excessive newlines but preserve paragraph breaks
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def _chunk_by_tokens(self, text: str) -> List[str]:
        """Chunk text based on token count using tiktoken."""
        try:
            # Tokenize the entire text
            tokens = self.tokenizer.encode(text)
            
            if len(tokens) <= self.chunk_size:
                return [text]
            
            chunks = []
            start_idx = 0
            
            while start_idx < len(tokens):
                # Calculate end index for this chunk
                end_idx = min(start_idx + self.chunk_size, len(tokens))
                
                # Extract tokens for this chunk
                chunk_tokens = tokens[start_idx:end_idx]
                
                # Decode tokens back to text
                chunk_text = self.tokenizer.decode(chunk_tokens)
                
                # Try to end at sentence boundary if possible
                if end_idx < len(tokens):  # Not the last chunk
                    chunk_text = self._adjust_chunk_boundary(chunk_text)
                
                chunks.append(chunk_text.strip())
                
                # Calculate next start position with overlap
                if end_idx >= len(tokens):
                    break
                
                # Move start position back by overlap amount
                start_idx = end_idx - self.chunk_overlap
                
                # Ensure we don't go backwards
                if start_idx <= chunks.__len__() * (self.chunk_size - self.chunk_overlap):
                    start_idx = end_idx - self.chunk_overlap
            
            return [chunk for chunk in chunks if chunk.strip()]
            
        except Exception as e:
            logger.error("Token-based chunking failed, falling back to character-based", error=str(e))
            return self._chunk_by_characters(text)
    
    def _chunk_by_characters(self, text: str) -> List[str]:
        """Chunk text based on character count (fallback method)."""
        # Approximate tokens to characters ratio (rough estimate)
        char_chunk_size = self.chunk_size * 4  # ~4 chars per token on average
        char_overlap = self.chunk_overlap * 4
        
        if len(text) <= char_chunk_size:
            return [text]
        
        chunks = []
        start_idx = 0
        
        while start_idx < len(text):
            # Calculate end index for this chunk
            end_idx = min(start_idx + char_chunk_size, len(text))
            
            # Extract text for this chunk
            chunk_text = text[start_idx:end_idx]
            
            # Try to end at sentence boundary if possible
            if end_idx < len(text):  # Not the last chunk
                chunk_text = self._adjust_chunk_boundary(chunk_text)
            
            chunks.append(chunk_text.strip())
            
            # Calculate next start position with overlap
            if end_idx >= len(text):
                break
            
            start_idx = end_idx - char_overlap
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def _adjust_chunk_boundary(self, chunk_text: str) -> str:
        """Adjust chunk boundary to end at sentence boundary if possible."""
        # Look for sentence endings in the last 20% of the chunk
        search_start = max(0, int(len(chunk_text) * 0.8))
        search_text = chunk_text[search_start:]
        
        # Find the last sentence ending
        sentence_matches = list(self.sentence_endings.finditer(search_text))
        
        if sentence_matches:
            # Use the last sentence ending
            last_match = sentence_matches[-1]
            cut_point = search_start + last_match.end()
            return chunk_text[:cut_point].strip()
        
        # If no sentence ending found, look for paragraph breaks
        paragraph_matches = list(self.paragraph_breaks.finditer(search_text))
        
        if paragraph_matches:
            # Use the last paragraph break
            last_match = paragraph_matches[-1]
            cut_point = search_start + last_match.start()
            return chunk_text[:cut_point].strip()
        
        # If no good boundary found, return as is
        return chunk_text
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text."""
        if self.use_tiktoken:
            try:
                return len(self.tokenizer.encode(text))
            except Exception:
                pass
        
        # Fallback: rough estimate
        return len(text.split()) * 1.3  # Approximate tokens per word
    
    def get_chunk_info(self, chunks: List[str]) -> dict:
        """Get information about the chunks."""
        if not chunks:
            return {
                "total_chunks": 0,
                "total_tokens": 0,
                "avg_tokens_per_chunk": 0,
                "min_tokens": 0,
                "max_tokens": 0,
            }
        
        token_counts = [self.count_tokens(chunk) for chunk in chunks]
        
        return {
            "total_chunks": len(chunks),
            "total_tokens": sum(token_counts),
            "avg_tokens_per_chunk": sum(token_counts) / len(token_counts),
            "min_tokens": min(token_counts),
            "max_tokens": max(token_counts),
        }
