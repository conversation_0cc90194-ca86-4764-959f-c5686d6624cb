"""
Text Extraction Service for DocQA CLI

This service handles text extraction from various file formats:
- PDF files
- Microsoft Word documents (DOC, DOCX)
- Images (PNG, JPG, JPEG) using OCR
- Plain text files

Uses the unstructured library for document processing and pytesseract for OCR.
"""

from pathlib import Path
from typing import Optional
import mimetypes

import structlog
from PIL import Image
import pytesseract

# Import unstructured components
try:
    from unstructured.partition.auto import partition
    from unstructured.partition.pdf import partition_pdf
    from unstructured.partition.docx import partition_docx
    from unstructured.partition.doc import partition_doc
    from unstructured.partition.text import partition_text
    UNSTRUCTURED_AVAILABLE = True
except ImportError:
    UNSTRUCTURED_AVAILABLE = False

logger = structlog.get_logger()


class TextExtractor:
    """Service for extracting text from various document formats."""
    
    def __init__(self):
        self.supported_extensions = {
            '.pdf': 'pdf',
            '.docx': 'docx',
            '.doc': 'doc',
            '.txt': 'text',
            '.png': 'image',
            '.jpg': 'image',
            '.jpeg': 'image',
            '.gif': 'image',
            '.bmp': 'image',
            '.tiff': 'image',
            '.tif': 'image',
        }
        
        # Check if required dependencies are available
        if not UNSTRUCTURED_AVAILABLE:
            logger.warning("Unstructured library not available. Document processing will be limited.")
        
        # Check if tesseract is available for OCR
        try:
            pytesseract.get_tesseract_version()
            self.ocr_available = True
            logger.info("Tesseract OCR is available")
        except Exception:
            self.ocr_available = False
            logger.warning("Tesseract OCR not available. Image processing will be disabled.")
    
    def extract_text(self, file_path: str, original_filename: Optional[str] = None) -> str:
        """
        Extract text from a file.
        
        Args:
            file_path: Path to the file to extract text from
            original_filename: Original filename (used for extension detection)
            
        Returns:
            Extracted text content
            
        Raises:
            ValueError: If file format is not supported or extraction fails
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise ValueError(f"File does not exist: {file_path}")
        
        # Determine file type
        if original_filename:
            file_extension = Path(original_filename).suffix.lower()
        else:
            file_extension = file_path.suffix.lower()
        
        if not file_extension:
            # Try to detect MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type:
                file_extension = self._mime_to_extension(mime_type)
        
        if file_extension not in self.supported_extensions:
            raise ValueError(f"Unsupported file format: {file_extension}")
        
        file_type = self.supported_extensions[file_extension]
        
        logger.info("Extracting text from file", 
                   file_path=str(file_path),
                   file_type=file_type,
                   file_extension=file_extension)
        
        try:
            if file_type == 'image':
                return self._extract_from_image(file_path)
            elif file_type == 'pdf':
                return self._extract_from_pdf(file_path)
            elif file_type == 'docx':
                return self._extract_from_docx(file_path)
            elif file_type == 'doc':
                return self._extract_from_doc(file_path)
            elif file_type == 'text':
                return self._extract_from_text(file_path)
            else:
                # Fallback to auto-detection
                return self._extract_auto(file_path)
                
        except Exception as e:
            logger.error("Text extraction failed", 
                        file_path=str(file_path),
                        file_type=file_type,
                        error=str(e))
            raise ValueError(f"Failed to extract text from {file_type} file: {str(e)}")
    
    def _extract_from_image(self, file_path: Path) -> str:
        """Extract text from image using OCR."""
        if not self.ocr_available:
            raise ValueError("OCR not available. Please install tesseract.")
        
        try:
            # Open and process image
            with Image.open(file_path) as image:
                # Convert to RGB if necessary
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # Extract text using pytesseract
                text = pytesseract.image_to_string(image, lang='eng')
                
                if not text.strip():
                    logger.warning("No text found in image", file_path=str(file_path))
                    return ""
                
                return text.strip()
                
        except Exception as e:
            raise ValueError(f"OCR extraction failed: {str(e)}")
    
    def _extract_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF file."""
        if not UNSTRUCTURED_AVAILABLE:
            raise ValueError("Unstructured library not available for PDF processing.")
        
        try:
            elements = partition_pdf(str(file_path))
            text_content = []
            
            for element in elements:
                if hasattr(element, 'text') and element.text.strip():
                    text_content.append(element.text.strip())
            
            return '\n\n'.join(text_content)
            
        except Exception as e:
            raise ValueError(f"PDF extraction failed: {str(e)}")
    
    def _extract_from_docx(self, file_path: Path) -> str:
        """Extract text from DOCX file."""
        if not UNSTRUCTURED_AVAILABLE:
            raise ValueError("Unstructured library not available for DOCX processing.")
        
        try:
            elements = partition_docx(str(file_path))
            text_content = []
            
            for element in elements:
                if hasattr(element, 'text') and element.text.strip():
                    text_content.append(element.text.strip())
            
            return '\n\n'.join(text_content)
            
        except Exception as e:
            raise ValueError(f"DOCX extraction failed: {str(e)}")
    
    def _extract_from_doc(self, file_path: Path) -> str:
        """Extract text from DOC file."""
        if not UNSTRUCTURED_AVAILABLE:
            raise ValueError("Unstructured library not available for DOC processing.")
        
        try:
            elements = partition_doc(str(file_path))
            text_content = []
            
            for element in elements:
                if hasattr(element, 'text') and element.text.strip():
                    text_content.append(element.text.strip())
            
            return '\n\n'.join(text_content)
            
        except Exception as e:
            raise ValueError(f"DOC extraction failed: {str(e)}")
    
    def _extract_from_text(self, file_path: Path) -> str:
        """Extract text from plain text file."""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("Could not decode text file with any supported encoding")
            
        except Exception as e:
            raise ValueError(f"Text file extraction failed: {str(e)}")
    
    def _extract_auto(self, file_path: Path) -> str:
        """Extract text using auto-detection."""
        if not UNSTRUCTURED_AVAILABLE:
            raise ValueError("Unstructured library not available for auto-detection.")
        
        try:
            elements = partition(str(file_path))
            text_content = []
            
            for element in elements:
                if hasattr(element, 'text') and element.text.strip():
                    text_content.append(element.text.strip())
            
            return '\n\n'.join(text_content)
            
        except Exception as e:
            raise ValueError(f"Auto extraction failed: {str(e)}")
    
    def _mime_to_extension(self, mime_type: str) -> str:
        """Convert MIME type to file extension."""
        mime_to_ext = {
            'application/pdf': '.pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/msword': '.doc',
            'text/plain': '.txt',
            'image/png': '.png',
            'image/jpeg': '.jpg',
            'image/gif': '.gif',
            'image/bmp': '.bmp',
            'image/tiff': '.tiff',
        }
        
        return mime_to_ext.get(mime_type, '')
    
    def is_supported_file(self, filename: str) -> bool:
        """Check if a file format is supported."""
        extension = Path(filename).suffix.lower()
        return extension in self.supported_extensions
    
    def get_supported_formats(self) -> list:
        """Get list of supported file formats."""
        return list(self.supported_extensions.keys())
