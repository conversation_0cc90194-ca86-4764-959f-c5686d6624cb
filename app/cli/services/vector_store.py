"""
Vector Store Service for DocQA CLI

This service handles:
- Generating embeddings using OpenAI text-embedding-3-small
- Storing vectors with metadata in FAISS
- Retrieving similar documents using cosine similarity + MMR
- Managing the FAISS index lifecycle
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
import numpy as np
import structlog

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = structlog.get_logger()


class VectorStore:
    """Service for managing vector embeddings and FAISS index."""
    
    def __init__(self, data_dir: str):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # FAISS index files
        self.index_file = self.data_dir / "faiss_index.bin"
        self.metadata_file = self.data_dir / "metadata.json"
        self.config_file = self.data_dir / "config.json"
        
        # OpenAI configuration
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        if not FAISS_AVAILABLE:
            raise ValueError("FAISS library is required. Install with: pip install faiss-cpu")
        
        if not OPENAI_AVAILABLE:
            raise ValueError("OpenAI library is required. Install with: pip install openai")
        
        # Initialize OpenAI client
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        
        # FAISS index and metadata
        self.index = None
        self.metadata = []
        self.dimension = None
        
        # Load existing index if available
        self._load_index()
    
    def add_documents(self, texts: List[str], metadatas: List[Dict[str, Any]], document_id: str) -> int:
        """
        Add documents to the vector store.
        
        Args:
            texts: List of text chunks to add
            metadatas: List of metadata dictionaries for each chunk
            document_id: Unique identifier for the document
            
        Returns:
            Number of embeddings created
        """
        if not texts:
            return 0
        
        logger.info("Adding documents to vector store", 
                   document_id=document_id,
                   chunk_count=len(texts))
        
        try:
            # Remove existing document if it exists
            self._remove_document(document_id)
            
            # Generate embeddings
            embeddings = self._generate_embeddings(texts)
            
            if not embeddings:
                raise ValueError("Failed to generate embeddings")
            
            # Initialize index if needed
            if self.index is None:
                self.dimension = len(embeddings[0])
                self._initialize_index()
            
            # Convert embeddings to numpy array
            embeddings_array = np.array(embeddings, dtype=np.float32)
            
            # Add to FAISS index
            start_id = len(self.metadata)
            self.index.add(embeddings_array)
            
            # Add metadata
            for i, (text, metadata) in enumerate(zip(texts, metadatas)):
                metadata_entry = {
                    **metadata,
                    "text": text,
                    "vector_id": start_id + i,
                }
                self.metadata.append(metadata_entry)
            
            # Save index and metadata
            self._save_index()
            
            logger.info("Successfully added documents to vector store",
                       document_id=document_id,
                       embeddings_count=len(embeddings))
            
            return len(embeddings)
            
        except Exception as e:
            logger.error("Failed to add documents to vector store",
                        document_id=document_id,
                        error=str(e))
            raise
    
    def search(self, query: str, top_k: int = 6, use_mmr: bool = True, mmr_lambda: float = 0.5) -> List[Dict[str, Any]]:
        """
        Search for similar documents.
        
        Args:
            query: Search query
            top_k: Number of results to return
            use_mmr: Whether to use Maximum Marginal Relevance for diversity
            mmr_lambda: MMR lambda parameter (0.0 = diversity, 1.0 = similarity)
            
        Returns:
            List of search results with metadata and scores
        """
        if self.index is None or len(self.metadata) == 0:
            logger.warning("No documents in vector store")
            return []
        
        try:
            # Generate query embedding
            query_embedding = self._generate_embeddings([query])[0]
            query_vector = np.array([query_embedding], dtype=np.float32)
            
            # Search FAISS index - use simple similarity search for speed
            scores, indices = self.index.search(query_vector, top_k)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.metadata):
                    result = self.metadata[idx].copy()
                    result["score"] = float(score)
                    results.append(result)
            
            logger.info("Search completed", 
                       query_length=len(query),
                       results_count=len(results),
                       use_mmr=use_mmr)
            
            return results
            
        except Exception as e:
            logger.error("Search failed", query=query, error=str(e))
            raise
    
    def _generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using OpenAI API."""
        try:
            logger.info("Generating embeddings", 
                       text_count=len(texts),
                       model=self.embedding_model)
            
            # Batch process texts to avoid API limits
            batch_size = 100  # OpenAI's batch limit
            all_embeddings = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                response = self.openai_client.embeddings.create(
                    model=self.embedding_model,
                    input=batch_texts
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
            
            return all_embeddings
            
        except Exception as e:
            logger.error("Failed to generate embeddings", error=str(e))
            raise ValueError(f"Failed to generate embeddings: {str(e)}")
    
    def _initialize_index(self):
        """Initialize FAISS index."""
        if self.dimension is None:
            raise ValueError("Dimension not set")
        
        # Use IndexFlatIP for cosine similarity (after normalization)
        self.index = faiss.IndexFlatIP(self.dimension)
        
        logger.info("Initialized FAISS index", dimension=self.dimension)
    
    def _save_index(self):
        """Save FAISS index and metadata to disk."""
        try:
            # Save FAISS index
            faiss.write_index(self.index, str(self.index_file))
            
            # Save metadata
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
            
            # Save configuration
            config = {
                "dimension": self.dimension,
                "embedding_model": self.embedding_model,
                "document_count": len(set(item.get("document_id") for item in self.metadata)),
                "chunk_count": len(self.metadata),
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info("Saved FAISS index and metadata")
            
        except Exception as e:
            logger.error("Failed to save index", error=str(e))
            raise
    
    def _load_index(self):
        """Load FAISS index and metadata from disk."""
        try:
            if self.index_file.exists() and self.metadata_file.exists():
                # Load FAISS index
                self.index = faiss.read_index(str(self.index_file))
                
                # Load metadata
                with open(self.metadata_file, 'r') as f:
                    self.metadata = json.load(f)
                
                # Load configuration
                if self.config_file.exists():
                    with open(self.config_file, 'r') as f:
                        config = json.load(f)
                        self.dimension = config.get("dimension")
                
                logger.info("Loaded existing FAISS index", 
                           chunk_count=len(self.metadata),
                           dimension=self.dimension)
            
        except Exception as e:
            logger.error("Failed to load index", error=str(e))
            # Reset to empty state
            self.index = None
            self.metadata = []
            self.dimension = None
    
    def _remove_document(self, document_id: str):
        """Remove a document from the index (rebuild required)."""
        # Filter out metadata for the document
        old_count = len(self.metadata)
        self.metadata = [item for item in self.metadata if item.get("document_id") != document_id]
        
        if len(self.metadata) < old_count:
            logger.info("Removed document from metadata", 
                       document_id=document_id,
                       removed_chunks=old_count - len(self.metadata))
            
            # Rebuild index if we removed items
            if self.metadata:
                self._rebuild_index()
            else:
                # No documents left, reset index
                self.index = None
                self.dimension = None
    
    def _rebuild_index(self):
        """Rebuild FAISS index from existing metadata."""
        if not self.metadata:
            return
        
        logger.info("Rebuilding FAISS index", chunk_count=len(self.metadata))
        
        # Extract texts and regenerate embeddings
        texts = [item["text"] for item in self.metadata]
        embeddings = self._generate_embeddings(texts)
        
        # Reinitialize index
        self.dimension = len(embeddings[0])
        self._initialize_index()
        
        # Add embeddings
        embeddings_array = np.array(embeddings, dtype=np.float32)
        self.index.add(embeddings_array)
        
        # Update vector IDs in metadata
        for i, item in enumerate(self.metadata):
            item["vector_id"] = i

    def _apply_mmr(self, query_embedding: List[float], candidate_indices: np.ndarray,
                   candidate_scores: np.ndarray, top_k: int, lambda_param: float) -> List[int]:
        """Apply Maximum Marginal Relevance for diverse results."""
        if len(candidate_indices) <= top_k:
            return candidate_indices.tolist()

        selected = []
        remaining = list(candidate_indices)

        # Select first item (highest similarity)
        selected.append(remaining.pop(0))

        while len(selected) < top_k and remaining:
            best_score = float('-inf')
            best_idx = None
            best_pos = None

            for pos, idx in enumerate(remaining):
                if idx >= len(self.metadata):
                    continue

                # Get document embedding
                doc_embedding = self._get_embedding_by_index(idx)
                if doc_embedding is None:
                    continue

                # Calculate similarity to query
                query_sim = self._cosine_similarity(query_embedding, doc_embedding)

                # Calculate maximum similarity to already selected documents
                max_selected_sim = 0.0
                for selected_idx in selected:
                    selected_embedding = self._get_embedding_by_index(selected_idx)
                    if selected_embedding is not None:
                        sim = self._cosine_similarity(doc_embedding, selected_embedding)
                        max_selected_sim = max(max_selected_sim, sim)

                # MMR score
                mmr_score = lambda_param * query_sim - (1 - lambda_param) * max_selected_sim

                if mmr_score > best_score:
                    best_score = mmr_score
                    best_idx = idx
                    best_pos = pos

            if best_idx is not None:
                selected.append(best_idx)
                remaining.pop(best_pos)
            else:
                break

        return selected

    def _get_embedding_by_index(self, idx: int) -> Optional[List[float]]:
        """Get embedding for a document by index from FAISS index."""
        if idx >= len(self.metadata) or self.index is None:
            return None

        try:
            # Get the embedding directly from FAISS index
            embedding_vector = self.index.reconstruct(idx)
            return embedding_vector.tolist()
        except Exception:
            return None

    def _cosine_similarity(self, a: List[float], b: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        a_np = np.array(a)
        b_np = np.array(b)

        # Normalize vectors
        a_norm = a_np / np.linalg.norm(a_np)
        b_norm = b_np / np.linalg.norm(b_np)

        # Calculate cosine similarity
        return float(np.dot(a_norm, b_norm))

    def clear_index(self):
        """Clear all data from the vector store."""
        self.index = None
        self.metadata = []
        self.dimension = None

        # Remove files
        for file_path in [self.index_file, self.metadata_file, self.config_file]:
            if file_path.exists():
                file_path.unlink()

        logger.info("Cleared vector store")

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store."""
        if not self.metadata:
            return {
                "document_count": 0,
                "chunk_count": 0,
                "index_size": "0 MB",
                "last_updated": None,
            }

        document_ids = set(item.get("document_id") for item in self.metadata)

        # Get index file size
        index_size = 0
        if self.index_file.exists():
            index_size = self.index_file.stat().st_size

        # Get last updated timestamp
        last_updated = None
        if self.metadata:
            timestamps = [item.get("ingestion_timestamp") for item in self.metadata if item.get("ingestion_timestamp")]
            if timestamps:
                last_updated = max(timestamps)

        return {
            "document_count": len(document_ids),
            "chunk_count": len(self.metadata),
            "index_size": f"{index_size / (1024 * 1024):.2f} MB",
            "last_updated": last_updated,
        }

    def exists(self) -> bool:
        """Check if the vector store exists and has data."""
        return self.index is not None and len(self.metadata) > 0
