"""
Document Ingestion Service for DocQA CLI

This service handles:
1. Downloading documents from AWS S3
2. Detecting file types and extracting text
3. Chunking text into manageable pieces
4. Generating embeddings using OpenAI
5. Storing vectors in FAISS index
"""

import os
import tempfile
from pathlib import Path
from typing import Dict, List, Any
from urllib.parse import urlparse
import hashlib
import json
from datetime import datetime

import boto3
import structlog
from botocore.exceptions import ClientError, NoCredentialsError

from app.cli.services.text_extractor import TextExtractor
from app.cli.services.vector_store import VectorStore
from app.cli.services.text_chunker import TextChunker

# Try to import enhanced vision extractor
try:
    from app.cli.services.vision_extractor import VisionExtractor
    VISION_AVAILABLE = True
except ImportError:
    VISION_AVAILABLE = False

logger = structlog.get_logger()


class IngestionService:
    """Service for ingesting documents from S3 and creating vector embeddings."""
    
    def __init__(self):
        self.chunk_size = int(os.getenv("CHUNK_SIZE", "400"))
        self.chunk_overlap = int(os.getenv("CHUNK_OVERLAP", "50"))
        self.data_dir = Path(os.getenv("FAISS_INDEX_PATH", "./data"))
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize services with vision capabilities if available
        if VISION_AVAILABLE:
            self.text_extractor = VisionExtractor()
            logger.info("Using enhanced vision extractor for graph/chart analysis")
        else:
            self.text_extractor = TextExtractor()
            logger.info("Using basic text extractor")

        self.text_chunker = TextChunker(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )
        self.vector_store = VectorStore(str(self.data_dir))
        
        # Initialize S3 client
        try:
            self.s3_client = boto3.client('s3')
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            raise ValueError("AWS credentials not configured. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY or configure AWS CLI.")
    
    def ingest_document(self, s3_url: str, force: bool = False, comprehensive: bool = False) -> Dict[str, Any]:
        """
        Ingest a document from S3 URL.

        Args:
            s3_url: S3 URL of the document (e.g., s3://bucket/document.pdf)
            force: Force re-ingestion even if document already exists
            comprehensive: Enable comprehensive analysis with vision capabilities

        Returns:
            Dictionary with ingestion results
        """
        try:
            # Parse S3 URL
            parsed_url = urlparse(s3_url)
            if parsed_url.scheme != 's3':
                raise ValueError("Invalid S3 URL format")
            
            bucket_name = parsed_url.netloc
            object_key = parsed_url.path.lstrip('/')
            
            if not bucket_name or not object_key:
                raise ValueError("Invalid S3 URL: missing bucket or object key")
            
            logger.info("Starting document ingestion", 
                       bucket=bucket_name, 
                       key=object_key)
            
            # Generate document ID based on S3 URL
            doc_id = self._generate_document_id(s3_url)
            
            # Check if document already exists (unless force is True)
            if not force and self._document_exists(doc_id):
                logger.info("Document already ingested", doc_id=doc_id)
                return {
                    "success": True,
                    "document_name": object_key,
                    "chunks_count": 0,
                    "embeddings_count": 0,
                    "index_path": str(self.data_dir),
                    "message": "Document already exists (use --force to re-ingest)"
                }
            
            # Download document from S3
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                try:
                    logger.info("Downloading document from S3", 
                               bucket=bucket_name, 
                               key=object_key)
                    
                    self.s3_client.download_file(bucket_name, object_key, temp_file.name)
                    temp_file_path = temp_file.name
                    
                except ClientError as e:
                    error_code = e.response['Error']['Code']
                    if error_code == 'NoSuchBucket':
                        raise ValueError(f"S3 bucket '{bucket_name}' does not exist")
                    elif error_code == 'NoSuchKey':
                        raise ValueError(f"S3 object '{object_key}' does not exist in bucket '{bucket_name}'")
                    else:
                        raise ValueError(f"Failed to download from S3: {str(e)}")
            
            try:
                # Extract text from document (with vision analysis if comprehensive mode is enabled)
                logger.info("Extracting text from document", file_path=temp_file_path, comprehensive=comprehensive)

                if comprehensive and VISION_AVAILABLE and hasattr(self.text_extractor, 'extract_text_with_vision'):
                    # Use enhanced extraction with vision capabilities
                    logger.info("Using comprehensive vision analysis for charts and graphs")
                    extraction_result = self.text_extractor.extract_text_with_vision(temp_file_path, object_key)
                    text_content = extraction_result.get("text", "")

                    # Add visual elements to text content for better searchability
                    if extraction_result.get("chart_data"):
                        chart_descriptions = []
                        for chart in extraction_result["chart_data"]:
                            chart_descriptions.append(chart.get("searchable_text", ""))

                        if chart_descriptions:
                            text_content += "\n\nVISUAL ELEMENTS AND CHARTS:\n" + "\n".join(chart_descriptions)
                            logger.info("Added visual element descriptions to text content",
                                       charts_found=len(chart_descriptions))
                else:
                    # Use basic text extraction
                    logger.info("Using basic text extraction")
                    text_content = self.text_extractor.extract_text(temp_file_path, object_key)

                if not text_content.strip():
                    raise ValueError("No text content could be extracted from the document")
                
                # Chunk the text
                logger.info("Chunking text content", 
                           text_length=len(text_content),
                           chunk_size=self.chunk_size,
                           chunk_overlap=self.chunk_overlap)
                
                chunks = self.text_chunker.chunk_text(text_content)
                
                if not chunks:
                    raise ValueError("No chunks could be created from the text content")
                
                # Create metadata for chunks
                chunk_metadata = []
                for i, chunk in enumerate(chunks):
                    metadata = {
                        "document_id": doc_id,
                        "document_name": object_key,
                        "s3_url": s3_url,
                        "chunk_index": i,
                        "chunk_size": len(chunk),
                        "ingestion_timestamp": datetime.utcnow().isoformat(),
                    }
                    chunk_metadata.append(metadata)
                
                # Store in vector database
                logger.info("Generating embeddings and storing in vector database", 
                           chunks_count=len(chunks))
                
                embeddings_count = self.vector_store.add_documents(
                    texts=chunks,
                    metadatas=chunk_metadata,
                    document_id=doc_id
                )
                
                # Save document metadata
                self._save_document_metadata(doc_id, {
                    "s3_url": s3_url,
                    "document_name": object_key,
                    "chunks_count": len(chunks),
                    "embeddings_count": embeddings_count,
                    "ingestion_timestamp": datetime.utcnow().isoformat(),
                    "text_length": len(text_content),
                    "chunk_size": self.chunk_size,
                    "chunk_overlap": self.chunk_overlap,
                })
                
                logger.info("Document ingestion completed successfully",
                           doc_id=doc_id,
                           chunks_count=len(chunks),
                           embeddings_count=embeddings_count)
                
                return {
                    "success": True,
                    "document_name": object_key,
                    "chunks_count": len(chunks),
                    "embeddings_count": embeddings_count,
                    "index_path": str(self.data_dir),
                }
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error("Document ingestion failed", 
                        s3_url=s3_url, 
                        error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    def _generate_document_id(self, s3_url: str) -> str:
        """Generate a unique document ID based on S3 URL."""
        return hashlib.md5(s3_url.encode()).hexdigest()
    
    def _document_exists(self, doc_id: str) -> bool:
        """Check if a document has already been ingested."""
        metadata_file = self.data_dir / f"{doc_id}_metadata.json"
        return metadata_file.exists()
    
    def _save_document_metadata(self, doc_id: str, metadata: Dict[str, Any]) -> None:
        """Save document metadata to disk."""
        metadata_file = self.data_dir / f"{doc_id}_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def get_ingested_documents(self) -> List[Dict[str, Any]]:
        """Get list of all ingested documents."""
        documents = []
        for metadata_file in self.data_dir.glob("*_metadata.json"):
            try:
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                    documents.append(metadata)
            except Exception as e:
                logger.warning("Failed to load document metadata", 
                              file=str(metadata_file), 
                              error=str(e))
        
        return sorted(documents, key=lambda x: x.get('ingestion_timestamp', ''))
