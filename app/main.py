"""
GrowthHive FastAPI Application
AI-enabled prospect outreach system backend with comprehensive Swagger documentation
"""

# Load environment variables first
from dotenv import load_dotenv
load_dotenv()

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from pydantic import ValidationError
from app.core.config.settings import settings
from app.core.logging import setup_logging
from app.core.cache.memory import clear_cache
from app.api.v1 import api_router
from app.core.exceptions import (
    SecurityException,
    AuthenticationError,
    AuthorizationError,
    TokenError,
    SessionError,
    RefreshTokenError,
    pydantic_validation_exception_handler
)
from app.middleware.cors import setup_cors
from app.middleware.security import setup_security_headers
from app.core.app_rules import (
    ApiResponse,
    success_response,
    error_response
)
from app.middleware.request_logging_middleware import RequestLoggingMiddleware
from app.core.responses.models import ErrorCodes
import logging
from fastapi.openapi.utils import get_openapi
from fastapi.security import HTT<PERSON><PERSON>earer
from app.api.middleware.jwt_refresh_middleware import JWTRefreshMiddleware
from fastapi.exceptions import RequestValidationError

# Setup logging
logger = setup_logging()

# Create FastAPI app with default documentation settings
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description=settings.PROJECT_DESCRIPTION,
    # Use default docs URLs
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    debug=settings.DEBUG
)

# Setup middleware
setup_cors(app)
setup_security_headers(app)
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Allow all hosts in development
)

# Add request logging middleware
app.add_middleware(RequestLoggingMiddleware)

# Add JWT refresh middleware for dual-token authentication
app.add_middleware(JWTRefreshMiddleware)

# Add API router
app.include_router(api_router, prefix="/api")

# Add Enhanced DocQA router
from app.api.docqa_enhanced import router as docqa_enhanced_router
app.include_router(docqa_enhanced_router)

# Add startup event
@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    logger.info("Starting up application...")
    clear_cache()

# Add shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler"""
    logger.info("Shutting down application...")

# Add exception handlers
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    """Pydantic validation error handler"""
    return await pydantic_validation_exception_handler(request, exc)

@app.exception_handler(SecurityException)
async def security_exception_handler(request: Request, exc: SecurityException):
    """Security exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            error_code=1001,
            title="Security Error",
            description=exc.detail
        ).model_dump()
    )

@app.exception_handler(AuthenticationError)
async def authentication_error_handler(request: Request, exc: AuthenticationError):
    """Authentication error handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            error_code=1002,
            title="Authentication Error",
            description=exc.detail
        ).model_dump()
    )

@app.exception_handler(AuthorizationError)
async def authorization_error_handler(request: Request, exc: AuthorizationError):
    """Authorization error handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            error_code=1003,
            title="Authorization Error",
            description=exc.detail
        ).model_dump()
    )

@app.exception_handler(TokenError)
async def token_error_handler(request: Request, exc: TokenError):
    """Token error handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            error_code=1004,
            title="Token Error",
            description=exc.detail
        ).model_dump()
    )

@app.exception_handler(SessionError)
async def session_error_handler(request: Request, exc: SessionError):
    """Session error handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            error_code=1005,
            title="Session Error",
            description=exc.detail
        ).model_dump()
    )

@app.exception_handler(RefreshTokenError)
async def refresh_token_error_handler(request: Request, exc: RefreshTokenError):
    """Refresh token error handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            error_code=1006,
            title="Refresh Token Error",
            description=exc.detail
        ).model_dump()
    )

@app.exception_handler(RequestValidationError)
async def request_validation_exception_handler(request, exc):
    """
    Global handler for FastAPI request validation errors (422 Unprocessable Entity).
    Returns standardized error response format (StandardResponse style).
    """
    errors = []
    for error in exc.errors():
        field = " -> ".join(str(x) for x in error["loc"])
        message = error["msg"]
        errors.append(f"{field}: {message}")
    error_message = "; ".join(errors)
    return JSONResponse(
        status_code=422,
        content=error_response(
            error_code=422,
            title="Validation Error",
            description=error_message
        ).model_dump()
    )

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logging.exception("Unhandled exception: %s", exc)
    # HTTPException: use its status code and detail if possible
    if isinstance(exc, HTTPException):
        detail = exc.detail if isinstance(exc.detail, dict) else {"title": "Error", "description": str(exc.detail)}
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title=detail.get("title", "Internal Server Error"),
                description=detail.get("description", "An unexpected error occurred"),
            ).model_dump()
        )
    # Custom exceptions: try to extract title/description
    detail = getattr(exc, "detail", None)
    if isinstance(detail, dict):
        title = detail.get("title", "Internal Server Error")
        description = detail.get("description", str(exc))
    else:
        title = getattr(exc, "title", "Internal Server Error")
        description = getattr(exc, "description", str(exc))
    return JSONResponse(
        status_code=500,
        content=error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            title=title,
            description=description,
        ).model_dump()
    )

# Add health check endpoint
@app.get(
    "/health",
    tags=["system"],
    summary="Health Check",
    description="Check if the API is running properly",
    response_model=ApiResponse
)
async def health_check():
    """Health check endpoint"""
    return success_response(
        details={"status": "healthy"},
        title="Health Check",
        description="API is running properly"
    )

# Add metrics endpoint
@app.get(
    "/metrics",
    tags=["system"],
    summary="System Metrics",
    description="Get system performance metrics",
    response_model=ApiResponse
)
async def metrics():
    """Metrics endpoint"""
    return success_response(
        details={"status": "ok"},
        title="System Metrics",
        description="System metrics retrieved successfully"
    )

# Add root endpoint
@app.get(
    "/",
    tags=["system"],
    summary="API Root",
    description="Welcome endpoint with API information",
    response_model=ApiResponse
)
async def root():
    """Root endpoint"""
    return success_response(
        details={
            "message": "Welcome to GrowthHive API",
            "docs": "/api/docs",
            "redoc": "/api/redoc",
            "openapi": "/api/openapi.json"
        },
        title="Welcome",
        description="Welcome to GrowthHive API"
    )

bearer_scheme = HTTPBearer()

# Patch OpenAPI schema to add global security
openapi_schema = None

def custom_openapi():
    global openapi_schema
    if openapi_schema:
        return openapi_schema
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "HTTPBearer": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT"
        },
        "X-Remember-Token": {
            "type": "apiKey",
            "in": "header",
            "name": "X-Remember-Token",
            "description": "Remember me token for automatic JWT refresh"
        }
    }
    # Apply globally except for public endpoints
    public_endpoints = [
        "/api/auth/login",
        "/api/auth/register",
        "/api/auth/remember-me-login",
        "/api/logs",
        "/api/logs/",
        "/api/logs/download/{log_type}",
        "/api/logs/download/{log_type}/info",
        "/api/logs/download/zip",
        "/api/logs/health",
        "/api/webhooks/webhook",
        "/api/webhooks/webhook/health"
    ]
    for path, methods in openapi_schema["paths"].items():
        for method, details in methods.items():
            if path not in public_endpoints:
                details.setdefault("security", []).append({"HTTPBearer": []})
    return openapi_schema

app.openapi = custom_openapi

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=settings.WORKERS,
        log_level="info" if settings.DEBUG else "error"
    )
