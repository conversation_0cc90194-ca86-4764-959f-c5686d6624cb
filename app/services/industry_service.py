from app.repositories.industry_repository import IndustryRepository
from app.schemas.industry import IndustryCreateRequest, IndustryUpdateRequest
from app.models.industry import Industry
from typing import List, Optional, Union, Tuple
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select, or_, func
from app.core.api_standards import APIStandards
from fastapi import status
import uuid

class IndustryService:
    """Business logic for Industry management."""
    def __init__(self, repository: IndustryRepository):
        self.repository = repository

    async def create_industry(self, obj_in: IndustryCreateRequest) -> Union[Industry, dict]:
        try:
            existing = await self.repository.get_by_name(obj_in.name)
            if existing:
                return APIStandards.create_error_response(
                    error_message="Industry name already exists.",
                    error_title="Duplicate Industry",
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=4000
                )
            result = await self.repository.create(obj_in)
            # If result is not an Industry instance, return a generic error
            if not isinstance(result, Industry):
                return APIStandards.create_error_response(
                    error_message="Failed to create industry.",
                    error_title="Creation Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=5000
                )
            return result
        except IntegrityError:
            return APIStandards.create_error_response(
                error_message="Industry name already exists.",
                error_title="Duplicate Industry",
                status_code=status.HTTP_409_CONFLICT,
                error_code=4000
            )
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5000
            )

    async def get_industry(self, id: Union[str, uuid.UUID]) -> Union[Industry, dict]:
        try:
            industry = await self.repository.get(id)
            if not industry:
                return APIStandards.create_error_response(
                    error_message="Industry not found.",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND
                )
            return industry
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def list_industries(self, skip: int = 0, limit: int = 100, search: Optional[str] = None) -> Union[Tuple[List[Industry], int], dict]:
        try:
            # Build base query
            query = select(Industry)
            count_query = select(func.count(Industry.id))

            # Apply search filters if provided
            if search:
                search_filter = or_(
                    Industry.name.ilike(f"%{search}%"),
                    Industry.description.ilike(f"%{search}%")
                )
                query = query.where(search_filter)
                count_query = count_query.where(search_filter)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            # Execute queries
            result = await self.repository.db.execute(query)
            count_result = await self.repository.db.execute(count_query)

            industries = list(result.scalars().all())
            total_count = count_result.scalar()

            return industries, total_count
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5000
            )

    async def update_industry(self, id: Union[str, uuid.UUID], obj_in: IndustryUpdateRequest) -> Union[Industry, dict]:
        try:
            db_obj = await self.repository.get(id)
            if not db_obj:
                return APIStandards.create_error_response(
                    error_message="Industry not found.",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND
                )
            return await self.repository.update(db_obj, obj_in)
        except IntegrityError:
            return APIStandards.create_error_response(
                error_message="Industry name already exists.",
                error_title="Duplicate Industry",
                status_code=status.HTTP_409_CONFLICT
            )
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def delete_industry(self, id: Union[str, uuid.UUID]) -> Union[bool, dict]:
        try:
            db_obj = await self.repository.get(id)
            if not db_obj:
                return APIStandards.create_error_response(
                    error_message="Industry not found.",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND
                )
            return await self.repository.delete(id)
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
