"""
Webhook API endpoints for Kudosity webhook events
Handles all webhook event types and processes them according to business rules
"""

from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends, Request, status, HTTPException
from fastapi.responses import JSONResponse
from pydantic import ValidationError
import structlog
import json

from app.schemas.webhook import (
    WebhookRequest,
    WebhookEventType,
    WebhookReceiveResponse,
    WebhookHealthResponse
)
# Authentication removed for webhook endpoints
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.core.logging import logger
from docqa.central_api import ask_question
# Franchisor detection removed - using hardcoded Coochie Hydrogreen

# Import QnA/RAG functionality
try:
    from docqa.central_api import ask_question
    QNA_AVAILABLE = True
    logger.info("QnA/RAG functionality loaded successfully")
except ImportError as e:
    QNA_AVAILABLE = False
    logger.warning(f"QnA/RAG functionality not available: {e}")

# Initialize structured logger
logger = structlog.get_logger(__name__)

router = APIRouter(prefix="/webhooks", tags=["Webhooks"])


async def generate_ai_answer(question: str) -> Dict[str, Any]:
    """
    Generate AI-powered answer using QnA/RAG system for Coochie Hydrogreen

    Args:
        question: The question to answer

    Returns:
        Dict containing answer and metadata
    """
    if not QNA_AVAILABLE:
        return {
            "success": False,
            "answer": "❌ QnA/RAG system is not available. Please check the system configuration.",
            "error": "QnA system not available"
        }

    try:
        # Prepare request for Coochie Hydrogreen
        question_request = {
            "question": question,
            "top_k": 6,  # Number of relevant documents to retrieve
            "similarity_threshold": 0.5,  # Lower threshold to find more content
            "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",  # Coochie Hydrogreen
            "processing_options": {
                "priority": 5,  # Medium priority
                "triggered_via": "webhook"
            }
        }

        # Use the central RAG system to generate an answer
        result = await ask_question(question_request)

        return result

    except Exception as e:
        logger.error(f"Failed to generate AI answer for question: {question[:100]}... Error: {e}")
        return {
            "success": False,
            "answer": "❌ Sorry, I encountered an error while processing your question.",
            "error": str(e)
        }


async def process_inbound_message(message: str) -> Dict[str, Any]:
    """
    Process inbound message using RAG-based QnA system for Coochie Hydrogreen

    Args:
        message: The inbound message content

    Returns:
        Dict containing the answer and metadata
    """
    try:
        # Prepare question request for RAG system - hardcoded for Coochie Hydrogreen
        question_request = {
            "question": message,
            "top_k": 8,  # Increased number of relevant chunks to consider
            "similarity_threshold": 0.5,  # Lower threshold to find more content
            "include_metadata": True,  # Include source information
            "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe"  # Coochie Hydrogreen
        }

        # Use hardcoded Coochie Hydrogreen franchisor info
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        franchisor_name = "Coochie Hydrogreen"
        detection_confidence = 1.0  # Always 100% confidence since we're hardcoded

        # Get answer from RAG system for Coochie Hydrogreen
        answer_result = await ask_question(question_request)

        # Enhance the result with Coochie Hydrogreen metadata
        if "metadata" not in answer_result:
            answer_result["metadata"] = {}

        answer_result["metadata"].update({
            "franchisor_info": {
                "franchisor_id": franchisor_id,
                "franchisor_name": franchisor_name,
                "detection_method": "hardcoded_coochie_hydrogreen"
            }
        })

        # Log the interaction with enhanced details
        logger.info(
            "RAG answer generated with franchisor detection",
            question=message,
            answer=answer_result.get("answer", "No answer generated"),
            sources=answer_result.get("sources", []),
            processing_time=answer_result.get("processing_time_ms"),
            detected_franchisor=franchisor_name,
            detection_confidence=detection_confidence
        )

        return answer_result

    except Exception as e:
        logger.error(
            "Failed to process message with RAG",
            error=str(e),
            question=message,
            exc_info=True
        )
        raise


def print_webhook_beautifully(webhook_data: Dict[str, Any], answer_result: Dict[str, Any] = None) -> None:
    """
    Print webhook data in a beautiful format to the terminal
    
    Args:
        webhook_data: The webhook payload dictionary
        answer_result: Optional answer result from RAG system
    """
    print("\n" + "="*80)
    print("🔔 KUDOSITY WEBHOOK RECEIVED")
    print("="*80)
    
    # Print timestamp
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"📅 Received at: {current_time}")
    
    # Print event type prominently
    event_type = webhook_data.get("event_type", "UNKNOWN")
    print(f"📋 Event Type: {event_type}")
    
    # Print webhook timestamp if available
    webhook_timestamp = webhook_data.get("timestamp")
    if webhook_timestamp:
        print(f"⏰ Webhook Timestamp: {webhook_timestamp}")
    
    print("-" * 80)
    
    # Print specific details based on event type
    if event_type == WebhookEventType.SMS_INBOUND:
        mo = webhook_data.get("mo", {})
        print(f"📨 {event_type} DETAILS:")
        print(f"   Message ID: {mo.get('id', 'N/A')}")
        print(f"   From: {mo.get('sender', 'N/A')}")
        print(f"   To: {mo.get('recipient', 'N/A')}")
        print(f"   Message: {mo.get('message', 'N/A')}")
        
        # Print RAG answer if available
        if answer_result:
            print("\n🤖 AI ANSWER:")
            print("-" * 80)
            print(f"Question: {mo.get('message', 'N/A')}")
            print(f"Answer: {answer_result.get('answer', 'No answer generated')}")
            if answer_result.get('sources'):
                print("\nSources:")
                for source in answer_result.get('sources', []):
                    print(f"- {source}")
            print("-" * 80)
    
    print("="*80)
    print("✅ Webhook processing completed")
    print("="*80 + "\n")


@router.post(
    "/kudosity",
    response_model=WebhookReceiveResponse,
    summary="Receive Kudosity Webhook",
    description="Endpoint for receiving and processing Kudosity webhook events (no authentication required)",
    responses={
        200: {
            "description": "Webhook processed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Webhook Processed",
                            "description": "Webhook event processed successfully"
                        },
                        "data": {
                            "message": "Webhook received and processed successfully",
                            "webhook_id": "123e4567-e89b-12d3-a456-426614174000",
                            "event_type": "SMS_INBOUND"
                        }
                    }
                }
            }
        },
        400: {
            "description": "Invalid webhook payload",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Invalid Webhook",
                            "description": "The webhook payload is invalid or malformed"
                        },
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication credentials are required"
                        },
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Processing Error",
                            "description": "An error occurred while processing the webhook"
                        },
                        "error_code": "INTERNAL_SERVER_ERROR"
                    }
                }
            }
        }
    }
)
async def receive_webhook(
    request: Request
) -> JSONResponse:
    """
    Process incoming Kudosity webhook events

    Args:
        request: The incoming webhook request

    Returns:
        JSONResponse: Webhook processing result
    """
    try:
        # Get raw payload
        body = await request.body()
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse webhook JSON: {e}")
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid JSON",
                message_description="Failed to parse webhook payload JSON",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate webhook payload
        try:
            webhook_data = WebhookRequest(**payload)
        except ValidationError as e:
            logger.error(
                "Invalid webhook payload",
                error=str(e),
                payload=payload,
                exc_info=True
            )
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid Webhook",
                message_description="The webhook payload is invalid or malformed",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Log webhook receipt
        logger.info(
            "Webhook received",
            event_type=webhook_data.event_type,
            timestamp=webhook_data.timestamp
        )
        
        # Process SMS inbound events
        if webhook_data.event_type == WebhookEventType.SMS_INBOUND:
            if not webhook_data.mo or not webhook_data.mo.message:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="Invalid SMS Inbound",
                    message_description="Missing message content in SMS inbound webhook",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            # Process message with RAG system
            try:
                answer_result = await process_inbound_message(webhook_data.mo.message)
                
                # Print beautifully to terminal
                print_webhook_beautifully(payload, answer_result)
                
            except Exception as e:
                logger.error(
                    "Failed to process inbound message",
                    error=str(e),
                    message=webhook_data.mo.message,
                    exc_info=True
                )
                # Continue processing even if RAG fails
        
        return create_success_response(
            data=WebhookReceiveResponse(
                message="Webhook received and processed successfully",
                webhook_id=getattr(webhook_data, 'webhook_id', None),
                event_type=webhook_data.event_type
            ).model_dump(),
            message_title="Webhook Processed",
            message_description="Webhook event processed successfully",
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(
            "Webhook processing failed",
            error=str(e),
            exc_info=True
        )
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Processing Error",
            message_description="An error occurred while processing the webhook",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/health",
    response_model=WebhookHealthResponse,
    summary="Webhook Health Check",
    description="Check the health status of the webhook service and its dependencies (no authentication required)",
    responses={
        200: {
            "description": "Service health status",
            "content": {
                "application/json": {
                    "example": {
                        "status": "healthy",
                        "qna_available": True,
                        "timestamp": "2024-03-20T10:00:00Z"
                    }
                }
            }
        }
    }
)
async def webhook_health() -> WebhookHealthResponse:
    """
    Check webhook service health status

    Returns:
        WebhookHealthResponse: Health check result
    """
    try:
        # Check QnA/RAG system availability
        qna_available = True
        try:
            # Simple test question to verify RAG system
            await ask_question({"question": "test", "top_k": 1})
        except Exception:
            qna_available = False
        
        return WebhookHealthResponse(
            status="healthy",
            qna_available=qna_available,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to determine service health status")
