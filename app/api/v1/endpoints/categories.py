from fastapi import APIRouter, Depends, status, Query
from fastapi.responses import JSONResponse
from typing import Optional
from app.schemas.industry import (
    IndustryCreateRequest,
    IndustryUpdateRequest,
    IndustryResponse,
    IndustryListResponse,
    IndustryListSuccessResponse
)
from app.schemas.base_response import StandardResponse, PaginationInfo, ResponseMessage
from app.core.factory import get_industry_service
from app.services.industry_service import IndustryService
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.api_standards import APIStandards
from app.core.logging import logger
from app.core.responses.models import ErrorCodes

router = APIRouter()

@router.post("/industries", response_model=StandardResponse[IndustryResponse], status_code=status.HTTP_201_CREATED)
async def create_industry(
    industry_in: IndustryCreateRequest,
    service: IndustryService = Depends(get_industry_service),
    current_user: dict = Depends(get_current_user)
):
    """Create a new industry."""
    try:
        industry = await service.create_industry(industry_in)
        # If error response (dict or JSONResponse), return it directly
        if isinstance(industry, (dict, JSONResponse)):
            return industry

        # Log successful creation
        logger.info(f"Industry created successfully: {industry.name}", extra={
            "context": {"industry_id": str(industry.id), "user_id": str(current_user.id) if hasattr(current_user, 'id') else 'unknown'}
        })

        # Convert ORM object to response format
        industry_dict = industry.__dict__.copy()
        if 'id' in industry_dict and industry_dict['id'] is not None:
            industry_dict['id'] = str(industry_dict['id'])

        # Validate required fields are present
        required_fields = ["id", "name", "description", "is_active", "is_deleted", "created_at", "updated_at"]
        missing = [f for f in required_fields if f not in industry_dict]
        if missing:
            logger.error(f"Missing fields in industry ORM object: {missing}", extra={
                "context": {"industry_dict": industry_dict, "missing_fields": missing}
            })
            return APIStandards.create_error_response(
                error_message="Internal error: Missing required fields in industry data",
                error_title="Data Integrity Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.UNKNOWN_ERROR
            )

        return APIStandards.create_success_response(
            data=IndustryResponse(**industry_dict),
            message="Industry created successfully",
            title="Industry Created",
            status_code=status.HTTP_201_CREATED
        )
    except Exception as e:
        logger.error(f"Unexpected error creating industry: {str(e)}", exc_info=True, extra={
            "context": {"industry_name": industry_in.name}
        })
        return APIStandards.create_error_response(
            error_message="An error occurred while creating the industry",
            error_title="Creation Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

@router.get("/industries", response_model=IndustryListSuccessResponse)
async def list_industries(
    search: Optional[str] = Query(None, description="Search industries by name or description"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    service: IndustryService = Depends(get_industry_service),
    current_user: dict = Depends(get_current_user)
):
    """List all industries with pagination and optional search."""
    try:
        result = await service.list_industries(skip=skip, limit=limit, search=search)
        if isinstance(result, dict):
            return result

        categories, total_count = result

        # Convert categories to response format
        industry_list = []
        for c in categories:
            c_dict = c.__dict__.copy()
            if 'id' in c_dict and c_dict['id'] is not None:
                c_dict['id'] = str(c_dict['id'])
            industry_list.append(IndustryResponse(**c_dict))

        # Calculate pagination info
        page = (skip // limit) + 1
        pages = (total_count + limit - 1) // limit

        return IndustryListSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Industries Retrieved",
                description="Industries retrieved successfully"
            ),
            data=IndustryListResponse(
                items=industry_list,
                total_count=total_count,
                pagination=PaginationInfo(
                    current_page=page,
                    total_pages=pages,
                    items_per_page=limit,
                    total_items=total_count
                )
            )
        )

    except Exception as e:
        logger.error(f"Error in list_categories: {e}")
        return APIStandards.create_error_response(
            error_message="Failed to retrieve industries",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@router.get("/industries/{id}", response_model=StandardResponse[IndustryResponse])
async def get_industry(
    id: str,
    service: IndustryService = Depends(get_industry_service),
    current_user: dict = Depends(get_current_user)
):
    """Get a single industry by UUID."""
    industry = await service.get_industry(id)
    if isinstance(industry, dict):
        return industry
    industry_dict = industry.__dict__.copy()
    if 'id' in industry_dict and industry_dict['id'] is not None:
        industry_dict['id'] = str(industry_dict['id'])
    return APIStandards.create_success_response(
        data=IndustryResponse(**industry_dict),
        message="Industry retrieved successfully",
        title="Industry Details"
    )

@router.put("/industries/{id}", response_model=StandardResponse[IndustryResponse])
async def update_industry(
    id: str,
    industry_in: IndustryUpdateRequest,
    service: IndustryService = Depends(get_industry_service),
    current_user: dict = Depends(get_current_user)
):
    """Update an industry by UUID."""
    industry = await service.update_industry(id, industry_in)
    if isinstance(industry, dict):
        return industry
    industry_dict = industry.__dict__.copy()
    if 'id' in industry_dict and industry_dict['id'] is not None:
        industry_dict['id'] = str(industry_dict['id'])
    return APIStandards.create_success_response(
        data=IndustryResponse(**industry_dict),
        message="Industry updated successfully",
        title="Industry Updated"
    )