"""
Document API endpoints
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form

from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.factory import get_document_service
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.schemas.document import (
    DocumentCreateRequest,
    DocumentUpdateRequest,
    DocumentUploadRequest,
    DocumentBulkDeleteRequest,
    DocumentStatusUpdateRequest
)
from app.services.document_service import DocumentService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/",
    response_model=dict,
    responses={
        200: {
            "description": "Documents retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Documents Retrieved",
                            "description": "Successfully retrieved 2 documents"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                                    "name": "GrowthHive Signed Agreement",
                                    "description": "Official signed agreement document for GrowthHive project",
                                    "file_type": "application/pdf",
                                    "file_size": "7203206",
                                    "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                                    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                                    "is_active": True,
                                    "is_deleted": False,
                                    "created_at": "2025-06-27T16:23:20.742000+00:00",
                                    "updated_at": "2025-06-27T16:28:01.371000+00:00",
                                    "deleted_at": None
                                }
                            ],
                            "total_count": 2,
                            "pagination": {
                                "page": 1,
                                "limit": 20,
                                "total": 2,
                                "pages": 1
                            }
                        },
                        "error_code": None
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication token",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Retrieving Documents",
                            "description": "Database connection failed"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def get_documents(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term"),
    franchisor_id: Optional[str] = Query(None, description="Filter by franchisor ID"),
    file_type: Optional[str] = Query(None, description="Filter by file type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get documents with filtering, search, and pagination"""
    try:
        result = await document_service.get_documents_with_pagination(
            skip=skip,
            limit=limit,
            search=search,
            user_id=current_user.get("user_id"),
            franchisor_id=franchisor_id,
            file_type=file_type,
            is_active=is_active
        )
        
        return create_success_response(
            data=result.model_dump(),
            message_title="Documents Retrieved",
            message_description=f"Successfully retrieved {len(result.items)} documents"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Documents",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}",
    response_model=dict,
    responses={
        200: {
            "description": "Document retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Retrieved",
                            "description": "Document retrieved successfully"
                        },
                        "data": {
                            "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                            "name": "GrowthHive Signed Agreement",
                            "description": "Official signed agreement document for GrowthHive project",
                            "file_type": "application/pdf",
                            "file_size": "7203206",
                            "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                            "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-06-27T16:23:20.742000+00:00",
                            "updated_at": "2025-06-27T16:28:01.371000+00:00",
                            "deleted_at": None
                        },
                        "error_code": None
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        }
    }
)
async def get_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get document by ID"""
    try:
        document = await document_service.get_document_by_id(document_id)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Retrieved",
            message_description="Document retrieved successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Document",
            message_description=str(e),
            status_code=500
        )


@router.post("/",
    response_model=dict,
    responses={
        201: {
            "description": "Document created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Created",
                            "description": "Document created successfully"
                        },
                        "data": {
                            "id": "4b938d2c-c958-4e05-9a69-ed99fa4d340c",
                            "name": "Test Document",
                            "description": "A test document for the Document Management system",
                            "file_type": "application/pdf",
                            "file_size": "1024",
                            "file_path": "test/path/document.pdf",
                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/test/path/document.pdf",
                            "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-06-27T16:23:00.800000+00:00",
                            "updated_at": "2025-06-27T16:23:00.800000+00:00",
                            "deleted_at": None
                        },
                        "error_code": None
                    }
                }
            }
        },
        400: {
            "description": "Validation error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Validation Error",
                            "description": "Document name is required"
                        },
                        "data": None,
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Creating Document",
                            "description": "Failed to create document"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def create_document(
    document_data: DocumentCreateRequest,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Create a new document"""
    try:
        document = await document_service.create_document(document_data, current_user.get("user_id"))
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Created",
            message_description="Document created successfully",
            status_code=201
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Creating Document",
            message_description=str(e),
            status_code=500
        )


@router.post("/upload",
    response_model=dict,
    responses={
        201: {
            "description": "Document uploaded successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Uploaded",
                            "description": "Document uploaded successfully"
                        },
                        "data": {
                            "id": "ed8b831d-b082-4eea-962e-65082f2b1ef3",
                            "name": "Franchisor Agreement Document",
                            "description": "Agreement document linked to specific franchisor",
                            "file_type": "application/pdf",
                            "file_size": "7203206",
                            "file_path": "growthhive/document/20250627_162709_bdb21ea1656a.pdf",
                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162709_bdb21ea1656a.pdf",
                            "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-06-27T16:27:10.859000+00:00",
                            "updated_at": "2025-06-27T16:27:10.859000+00:00",
                            "deleted_at": None
                        },
                        "error_code": None
                    }
                }
            }
        },
        400: {
            "description": "Invalid file or validation error",
            "content": {
                "application/json": {
                    "examples": {
                        "no_file": {
                            "summary": "No file provided",
                            "value": {
                                "success": False,
                                "message": {
                                    "title": "Invalid File",
                                    "description": "No file provided"
                                },
                                "data": None,
                                "error_code": "VALIDATION_ERROR"
                            }
                        },
                        "invalid_file_type": {
                            "summary": "Invalid file type",
                            "value": {
                                "success": False,
                                "message": {
                                    "title": "Upload Failed",
                                    "description": "File type not allowed. Allowed types: .pdf, .png, .docx, .jpg, .doc, .jpeg"
                                },
                                "data": None,
                                "error_code": "UPLOAD_ERROR"
                            }
                        }
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Upload failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Upload Failed",
                            "description": "S3 service not available"
                        },
                        "data": None,
                        "error_code": "UPLOAD_ERROR"
                    }
                }
            }
        }
    }
)
async def upload_document(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: Optional[str] = Form(None),
    franchisor_id: Optional[str] = Form(None),
    is_active: bool = Form(True),
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Upload a document file"""
    try:
        # Validate file
        if not file.filename:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid File",
                message_description="No file provided",
                status_code=400
            )
        
        # Create upload request
        upload_data = DocumentUploadRequest(
            name=name,
            description=description,
            franchisor_id=franchisor_id,
            is_active=is_active
        )
        
        # Upload file and create document
        document = await document_service.upload_file(file, upload_data, current_user.get("user_id"))

        # 🆕 Queue background processing with Celery (silently)
        try:
            from app.services.background_task_manager import get_background_task_manager
            task_manager = get_background_task_manager()

            # Check if document is already being processed
            if document.processing_status == "processing":
                logger.info(f"Document {document.id} is already being processed, skipping task queue")
            else:
                # Queue processing task (completely separate thread)
                task_id = task_manager.queue_document_processing(
                    document_id=str(document.id),
                    file_url=document.file_path
                )

                logger.info(f"Document processing task queued for document {document.id}, task_id: {task_id}")

        except Exception as e:
            # Don't fail the upload if task queuing fails
            logger.error(f"Failed to queue document processing for document {document.id}: {e}")

        # Create clean response without processing status fields
        clean_document_data = {
            "id": str(document.id),
            "name": document.name,
            "description": document.description,
            "file_type": document.file_type,
            "file_size": document.file_size,
            "file_path": document.file_path,
            "file_url": document.file_url,
            "user_id": str(document.user_id) if document.user_id else None,
            "franchisor_id": str(document.franchisor_id) if document.franchisor_id else None,
            "is_active": document.is_active,
            "is_deleted": document.is_deleted,
            "created_at": document.created_at,
            "updated_at": document.updated_at
        }

        return create_success_response(
            data=clean_document_data,
            message_title="Document Uploaded",
            message_description="Document uploaded successfully",
            status_code=201
        )
        
    except HTTPException as e:
        return create_error_response(
            error_code=ErrorCodes.VALIDATION_ERROR,
            message_title="Upload Failed",
            message_description=e.detail,
            status_code=e.status_code
        )
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Upload Failed",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}/processing-status",
    response_model=dict,
    responses={
        200: {
            "description": "Processing status retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Processing Status",
                            "description": "Document processing status retrieved successfully"
                        },
                        "data": {
                            "document_id": "5f5d237d-6fa2-4b71-bcb6-a9bb8679d7c7",
                            "status": "processing",
                            "progress": 75,
                            "message": "Generating embeddings and storing vectors...",
                            "error": None,
                            "started_at": "2025-07-09T11:18:00.000Z",
                            "completed_at": None,
                            "is_processing": True
                        },
                        "error_code": None
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with specified ID not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        }
    }
)
async def get_document_processing_status(
    document_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get document processing status"""
    try:
        from app.services.async_document_processor import get_async_document_processor
        processor = get_async_document_processor()

        status = await processor.get_processing_status(document_id)

        if status is None:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description="Document with specified ID not found",
                status_code=404
            )

        return create_success_response(
            data=status,
            message_title="Processing Status",
            message_description="Document processing status retrieved successfully",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting processing status for document {document_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Status Check Failed",
            message_description=str(e),
            status_code=500
        )


@router.post("/{document_id}/reprocess",
    response_model=dict,
    responses={
        200: {
            "description": "Document reprocessing started successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Reprocessing Started",
                            "description": "Document reprocessing started successfully"
                        },
                        "data": {
                            "document_id": "5f5d237d-6fa2-4b71-bcb6-a9bb8679d7c7",
                            "status": "processing",
                            "message": "Reprocessing started"
                        },
                        "error_code": None
                    }
                }
            }
        },
        400: {
            "description": "Document already being processed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Already Processing",
                            "description": "Document is already being processed"
                        },
                        "data": None,
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        }
    }
)
async def reprocess_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Manually trigger document reprocessing"""
    try:
        # Get document to verify it exists and get file path
        document = await document_service.get_document_by_id(document_id)
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description="Document with specified ID not found",
                status_code=404
            )

        from app.services.async_document_processor import get_async_document_processor
        processor = get_async_document_processor()

        # Check if already processing
        if processor.is_processing(document_id):
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Already Processing",
                message_description="Document is already being processed",
                status_code=400
            )

        # Start processing
        processing_started = await processor.start_processing(
            document_id=document_id,
            file_url=document.file_path
        )

        if processing_started:
            return create_success_response(
                data={
                    "document_id": document_id,
                    "status": "processing",
                    "message": "Reprocessing started"
                },
                message_title="Reprocessing Started",
                message_description="Document reprocessing started successfully",
                status_code=200
            )
        else:
            return create_error_response(
                error_code=ErrorCodes.PROCESSING_ERROR,
                message_title="Processing Failed",
                message_description="Failed to start document reprocessing",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error reprocessing document {document_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Reprocessing Failed",
            message_description=str(e),
            status_code=500
        )


@router.post("/bulk-upload",
    response_model=dict,
    responses={
        201: {
            "description": "Bulk upload completed",
            "content": {
                "application/json": {
                    "examples": {
                        "all_success": {
                            "summary": "All files uploaded successfully",
                            "value": {
                                "success": True,
                                "message": {
                                    "title": "Bulk Upload Completed",
                                    "description": "Uploaded 2 documents, 0 failed"
                                },
                                "data": {
                                    "uploaded_documents": [
                                        {
                                            "id": "doc1-uuid",
                                            "name": "document1.pdf",
                                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162709_doc1.pdf",
                                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f"
                                        },
                                        {
                                            "id": "doc2-uuid",
                                            "name": "document2.pdf",
                                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162710_doc2.pdf",
                                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f"
                                        }
                                    ],
                                    "failed_uploads": [],
                                    "total_uploaded": 2,
                                    "total_failed": 0
                                },
                                "error_code": None
                            }
                        },
                        "partial_success": {
                            "summary": "Some files failed to upload",
                            "value": {
                                "success": True,
                                "message": {
                                    "title": "Bulk Upload Completed",
                                    "description": "Uploaded 1 documents, 1 failed"
                                },
                                "data": {
                                    "uploaded_documents": [
                                        {
                                            "id": "doc1-uuid",
                                            "name": "document1.pdf",
                                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162709_doc1.pdf"
                                        }
                                    ],
                                    "failed_uploads": [
                                        {
                                            "filename": "invalid_file.txt",
                                            "error": "File type not allowed. Allowed types: .pdf, .png, .docx, .jpg, .doc, .jpeg"
                                        }
                                    ],
                                    "total_uploaded": 1,
                                    "total_failed": 1
                                },
                                "error_code": None
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "No files provided",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Validation Error",
                            "description": "No files provided for upload"
                        },
                        "data": None,
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Bulk upload failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Bulk Upload Failed",
                            "description": "S3 service unavailable"
                        },
                        "data": None,
                        "error_code": "UPLOAD_ERROR"
                    }
                }
            }
        }
    }
)
async def bulk_upload_documents(
    files: List[UploadFile] = File(...),
    franchisor_id: Optional[str] = Form(None),
    is_active: bool = Form(True),
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Bulk upload multiple documents"""
    try:
        uploaded_documents = []
        failed_uploads = []
        
        for file in files:
            try:
                if not file.filename:
                    failed_uploads.append({"filename": "unknown", "error": "No filename provided"})
                    continue
                
                # Create upload request for each file
                upload_data = DocumentUploadRequest(
                    name=file.filename,
                    description=f"Bulk uploaded file: {file.filename}",
                    franchisor_id=franchisor_id,
                    is_active=is_active
                )
                
                # Upload file
                document = await document_service.upload_file(file, upload_data, current_user.get("user_id"))
                uploaded_documents.append(document.model_dump())
                
            except Exception as e:
                failed_uploads.append({"filename": file.filename, "error": str(e)})
        
        return create_success_response(
            data={
                "uploaded_documents": uploaded_documents,
                "failed_uploads": failed_uploads,
                "total_uploaded": len(uploaded_documents),
                "total_failed": len(failed_uploads)
            },
            message_title="Bulk Upload Completed",
            message_description=f"Uploaded {len(uploaded_documents)} documents, {len(failed_uploads)} failed",
            status_code=201
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Bulk Upload Failed",
            message_description=str(e),
            status_code=500
        )


@router.put("/{document_id}",
    response_model=dict,
    responses={
        200: {
            "description": "Document updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Updated",
                            "description": "Document updated successfully"
                        },
                        "data": {
                            "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                            "name": "Updated Document Name",
                            "description": "Updated to associate with franchisor",
                            "file_type": "application/pdf",
                            "file_size": "7203206",
                            "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                            "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-06-27T16:23:20.742000+00:00",
                            "updated_at": "2025-06-27T16:28:01.371000+00:00",
                            "deleted_at": None
                        },
                        "error_code": None
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Update failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Updating Document",
                            "description": "Failed to update document"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def update_document(
    document_id: str,
    update_data: DocumentUpdateRequest,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Update document"""
    try:
        document = await document_service.update_document(document_id, update_data)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Updated",
            message_description="Document updated successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Updating Document",
            message_description=str(e),
            status_code=500
        )


@router.delete("/{document_id}",
    response_model=dict,
    responses={
        200: {
            "description": "Document deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Deleted",
                            "description": "Document deleted successfully"
                        },
                        "data": {
                            "document_id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                            "deleted": True
                        },
                        "error_code": None
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Delete failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Deleting Document",
                            "description": "Failed to delete document"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def delete_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Delete document (soft delete)"""
    try:
        success = await document_service.delete_document(document_id)
        
        if not success:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data={"document_id": document_id, "deleted": True},
            message_title="Document Deleted",
            message_description="Document deleted successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Deleting Document",
            message_description=str(e),
            status_code=500
        )


@router.post("/bulk-delete",
    response_model=dict,
    responses={
        200: {
            "description": "Bulk delete completed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Bulk Delete Completed",
                            "description": "Successfully deleted 3 documents"
                        },
                        "data": {
                            "deleted_count": 3,
                            "requested_count": 3
                        },
                        "error_code": None
                    }
                }
            }
        },
        400: {
            "description": "Validation error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Validation Error",
                            "description": "Document IDs list cannot be empty"
                        },
                        "data": None,
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Bulk delete failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Bulk Deleting Documents",
                            "description": "Database error occurred"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def bulk_delete_documents(
    delete_request: DocumentBulkDeleteRequest,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Bulk delete documents"""
    try:
        deleted_count = await document_service.bulk_delete_documents(delete_request)
        
        return create_success_response(
            data={
                "deleted_count": deleted_count,
                "requested_count": len(delete_request.document_ids)
            },
            message_title="Bulk Delete Completed",
            message_description=f"Successfully deleted {deleted_count} documents"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Bulk Deleting Documents",
            message_description=str(e),
            status_code=500
        )


@router.patch("/{document_id}/status",
    response_model=dict,
    responses={
        200: {
            "description": "Document status updated successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "activated": {
                            "summary": "Document activated",
                            "value": {
                                "success": True,
                                "message": {
                                    "title": "Document Status Updated",
                                    "description": "Document activated successfully"
                                },
                                "data": {
                                    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                                    "name": "GrowthHive Signed Agreement",
                                    "description": "Official signed agreement document for GrowthHive project",
                                    "file_type": "application/pdf",
                                    "file_size": "7203206",
                                    "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                                    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                                    "is_active": True,
                                    "is_deleted": False,
                                    "created_at": "2025-06-27T16:23:20.742000+00:00",
                                    "updated_at": "2025-06-27T16:30:01.371000+00:00",
                                    "deleted_at": None
                                },
                                "error_code": None
                            }
                        },
                        "deactivated": {
                            "summary": "Document deactivated",
                            "value": {
                                "success": True,
                                "message": {
                                    "title": "Document Status Updated",
                                    "description": "Document deactivated successfully"
                                },
                                "data": {
                                    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                                    "name": "GrowthHive Signed Agreement",
                                    "is_active": False,
                                    "updated_at": "2025-06-27T16:30:01.371000+00:00"
                                },
                                "error_code": None
                            }
                        }
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        },
        400: {
            "description": "Validation error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Validation Error",
                            "description": "is_active field is required"
                        },
                        "data": None,
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Status update failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Updating Document Status",
                            "description": "Failed to update document status"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def update_document_status(
    document_id: str,
    status_data: DocumentStatusUpdateRequest,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Update document active/inactive status"""
    try:
        document = await document_service.update_document_status(document_id, status_data)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        status_text = "activated" if status_data.is_active else "deactivated"
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Status Updated",
            message_description=f"Document {status_text} successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Updating Document Status",
            message_description=str(e),
            status_code=500
        )
