"""
JWT Refresh Middleware for handling automatic token refresh via remember_me_token
"""

import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class JWTRefreshMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle JWT token refresh when remember_me_token is used for authentication
    """
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and handle JWT refresh if needed
        
        Args:
            request: FastAPI request
            call_next: Next middleware/endpoint
            
        Returns:
            Response: HTTP response with potential new JWT header
        """
        try:
            # Process the request
            response = await call_next(request)
            
            # Check if the request context contains a new JWT token
            # This would be set by the authentication middleware when remember_me_token is used
            if hasattr(request.state, 'new_jwt_token'):
                new_jwt = request.state.new_jwt_token
                
                # Add the new JWT token to response headers
                response.headers["X-New-JWT-Token"] = new_jwt
                response.headers["X-Token-Refreshed"] = "true"
                
                logger.info("Added new JWT token to response headers")
            
            return response
            
        except Exception as e:
            logger.error(f"Error in JWT refresh middleware: {e}")
            # Continue with normal response even if middleware fails
            return await call_next(request)


# Global instance
jwt_refresh_middleware = JWTRefreshMiddleware
