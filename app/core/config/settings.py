"""
Application Settings
Configuration settings for the GrowthHive application
"""

from typing import List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    """Application settings"""
    
    # Project Info
    PROJECT_NAME: str = "GrowthHive API"
    PROJECT_DESCRIPTION: str = "GrowthHive API for business growth"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 1
    
    # Database Configuration
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://root:root@localhost:5432/growthhive",
        description="Database connection URL"
    )
    DB_ECHO: bool = False
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    
    # JWT Configuration
    JWT_SECRET_KEY: str = Field(
        default="your-super-secret-jwt-key-change-in-production-32-chars",
        description="Secret key for JWT tokens",
        min_length=32,
        pattern=r"^[a-zA-Z0-9\-_\.]+$",
        alias="SECRET_KEY"
    )
    ALGORITHM: str = "HS256"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15  # 15 minutes for web application
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7     # 7 days for refresh tokens
    
    # Remember Me Token Configuration
    REMEMBER_ME_TOKEN_LENGTH: int = Field(
        default=32,
        description="Length of remember me tokens"
    )
    REMEMBER_ME_TOKEN_EXPIRE_DAYS: int = Field(
        default=30,
        description="Expiration days for remember me tokens"
    )
    
    # JWT Remember Me Settings
    JWT_REMEMBER_ME_TOKEN_EXPIRE_DAYS: int = Field(
        default=30,
        description="JWT token expiration days when remember me is enabled"
    )
    JWT_REMEMBER_ME_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(
        default=90,
        description="JWT refresh token expiration days when remember me is enabled"
    )
    JWT_SHORT_LIVED_TOKEN_MINUTES: int = Field(
        default=15,
        description="Short-lived token minutes for remember me fallback"
    )
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = ["*"]
    CORS_METHODS: List[str] = ["*"]
    CORS_HEADERS: List[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    
    # Security Configuration
    SECURITY_HEADERS: dict = {
        "X-Frame-Options": "DENY",
        "X-Content-Type-Options": "nosniff",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains"
    }
    
    # Cache Configuration
    CACHE_TTL: int = 3600  # 1 hour

    # RabbitMQ Configuration
    RABBITMQ_URL: str = Field(
        default="amqp://guest:guest@localhost:5672//",
        description="RabbitMQ connection URL"
    )

    # Celery Configuration
    CELERY_BROKER_URL: str = Field(
        default="amqp://guest:guest@localhost:5672//",
        description="Celery broker URL (RabbitMQ)"
    )
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/0",
        description="Celery result backend (Redis)"
    )
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: List[str] = ["json"]
    CELERY_TIMEZONE: str = "UTC"
    CELERY_ENABLE_UTC: bool = True

    # Document Processing Configuration
    DOCUMENT_PROCESSING_QUEUE: str = "document_processing"
    DOCUMENT_PROCESSING_RETRY_DELAY: int = 60  # seconds
    DOCUMENT_PROCESSING_MAX_RETRIES: int = 3

    # OpenAI Configuration
    OPENAI_API_KEY: str = Field(
        default="",
        description="OpenAI API key for AI-powered features"
    )

    # Franchisor Detection Configuration
    FRANCHISOR_DETECTION_MODEL: str = Field(
        default="gpt-4o",
        description="OpenAI model for franchisor detection"
    )
    FRANCHISOR_DETECTION_EMBEDDING_MODEL: str = Field(
        default="text-embedding-3-small",
        description="OpenAI embedding model for semantic similarity"
    )
    FRANCHISOR_DETECTION_CONFIDENCE_THRESHOLD: float = Field(
        default=0.75,
        description="Minimum confidence threshold for franchisor detection",
        ge=0.0,
        le=1.0
    )
    FRANCHISOR_DETECTION_SEMANTIC_THRESHOLD: float = Field(
        default=0.8,
        description="Minimum semantic similarity threshold for embedding-based detection",
        ge=0.0,
        le=1.0
    )
    FRANCHISOR_DETECTION_CACHE_TTL: int = Field(
        default=3600,
        description="Cache TTL for franchisor embeddings in seconds"
    )

    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    ENVIRONMENT: str = "development"
    
    # Model Configuration
    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=True,
        validate_assignment=True,
        extra="ignore"  # Ignore extra fields from .env
    )
    
    @field_validator("CORS_ORIGINS", "CORS_METHODS", "CORS_HEADERS", mode="before")
    @classmethod
    def parse_list_fields(cls, v: str | List[str]) -> List[str]:
        """Parse list fields from string or return as is if already a list"""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

# Create settings instance
settings = Settings()