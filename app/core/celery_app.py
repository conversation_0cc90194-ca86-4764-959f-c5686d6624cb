"""
Celery Application Configuration
Centralized Celery app for background task processing
"""

from celery import Celery
from app.core.config.settings import settings

def create_celery_app() -> Celery:
    """Create and configure Celery application"""
    
    celery_app = Celery(
        "growthhive",
        broker=settings.CELERY_BROKER_URL,
        backend=settings.CELERY_RESULT_BACKEND,
        include=[
            "app.tasks.document_processing",
            "app.tasks.docqa_processing",
        ]
    )
    
    # Configure Celery
    celery_app.conf.update(
        task_serializer=settings.CELERY_TASK_SERIALIZER,
        result_serializer=settings.CELERY_RESULT_SERIALIZER,
        accept_content=settings.CELERY_ACCEPT_CONTENT,
        timezone=settings.CELERY_TIMEZONE,
        enable_utc=settings.CELERY_ENABLE_UTC,
        
        # Task routing
        task_routes={
            "app.tasks.document_processing.*": {"queue": settings.DOCUMENT_PROCESSING_QUEUE},
            "app.tasks.docqa_processing.*": {"queue": settings.DOCUMENT_PROCESSING_QUEUE},
        },
        
        # Task execution settings
        task_acks_late=True,
        worker_prefetch_multiplier=1,
        
        # Retry settings
        task_default_retry_delay=settings.DOCUMENT_PROCESSING_RETRY_DELAY,
        task_max_retries=settings.DOCUMENT_PROCESSING_MAX_RETRIES,
        
        # Result settings
        result_expires=3600,  # 1 hour
        result_persistent=True,
        
        # Worker settings
        worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
        worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
        
        # Monitoring
        worker_send_task_events=True,
        task_send_sent_event=True,
    )
    
    return celery_app

# Create the Celery app instance
celery_app = create_celery_app()

# Auto-discover tasks
celery_app.autodiscover_tasks()

if __name__ == "__main__":
    celery_app.start()
