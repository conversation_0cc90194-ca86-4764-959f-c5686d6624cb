"""
Security Middleware Configuration
Handles security headers and CORS validation
"""

from fastapi import FastAPI, Request
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.config.settings import settings
from app.core.logging import setup_logging

# Setup logging
logger = setup_logging()

class SecurityMiddleware(BaseHTTPMiddleware):
    """Security middleware for handling security headers and CORS validation"""
    
    def __init__(
        self,
        app: FastAPI,
        cors_origin_regex: str = None
    ):
        super().__init__(app)
        self.cors_origin_regex = cors_origin_regex or r".*"  # Allow all origins by default
    
    async def dispatch(self, request: Request, call_next):
        """Process the request and add security headers"""
        response = await call_next(request)
        
        # Add security headers
        for header, value in settings.SECURITY_HEADERS.items():
            response.headers[header] = value
        
        return response

def setup_security_headers(app: FastAPI) -> None:
    """Configure security headers middleware"""
    app.add_middleware(SecurityMiddleware)

# Create middleware instance
security_middleware = SecurityMiddleware(app=None)  # Will be set by FastAPI 