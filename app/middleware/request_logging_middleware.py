import time
import uuid
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from app.core.logging import logger
from typing import Callable, Awaitable
import json
import os

SENSITIVE_FIELDS = {"password", "token", "access_token", "refresh_token"}

ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
APP_VERSION = os.getenv("APP_VERSION", "unknown")


def sanitize_payload(data):
    if isinstance(data, dict):
        return {k: ("***" if k in SENSITIVE_FIELDS else sanitize_payload(v)) for k, v in data.items()}
    if isinstance(data, list):
        return [sanitize_payload(item) for item in data]
    return data


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging request and response metadata, user info, sanitized payload, status, duration, and errors.
    """
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
        request_id = str(uuid.uuid4())
        start_time = time.time()
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent", "unknown")
        method = request.method
        path = request.url.path
        query_params = dict(request.query_params)
        user_id = None
        user_email = None
        user_role = None

        # Try to extract user info from request.state (if set by auth middleware)
        if hasattr(request.state, "user") and request.state.user:
            user = request.state.user
            user_id = getattr(user, "id", None)
            user_email = getattr(user, "email", None)
            user_role = getattr(user, "role", None)

        # Read and sanitize request body (if JSON)
        try:
            body = await request.body()
            if body:
                try:
                    json_body = json.loads(body)
                    sanitized_body = sanitize_payload(json_body)
                except Exception:
                    sanitized_body = "<non-JSON body>"
            else:
                sanitized_body = None
        except Exception:
            sanitized_body = "<error reading body>"

        log_context = {
            "request_id": request_id,
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "method": method,
            "path": path,
            "query_params": query_params,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "environment": ENVIRONMENT,
            "app_version": APP_VERSION,
            "user_id": user_id,
            "user_email": user_email,
            "user_role": user_role,
            "request_body": sanitized_body
        }

        logger.info("Request started", extra=log_context)

        try:
            response = await call_next(request)
            duration = time.time() - start_time
            log_context.update({
                "status_code": response.status_code,
                "duration": duration
            })
            logger.info("Request completed", extra=log_context)
            return response
        except Exception as exc:
            duration = time.time() - start_time
            log_context.update({
                "status_code": 500,
                "duration": duration,
                "error": str(exc)
            })
            logger.error("Request failed", extra=log_context, exc_info=True)
            raise 