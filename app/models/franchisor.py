"""
Franchisor model for database operations
"""

from sqlalchemy import Column, String, Boolean, Float, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database.connection import Base
from sqlalchemy.dialects.postgresql import UUID
from pgvector.sqlalchemy import Vector
import uuid


class Franchisor(Base):
    """Franchisor model for storing franchisor information"""

    __tablename__ = "franchisors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255), nullable=False, index=True)

    # Contact information (database columns are lowercase)
    contactfirstname = Column(String(100), nullable=True)
    contactlastname = Column(String(100), nullable=True)
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(20), nullable=True, index=True)

    # Foreign key relationship to Industry (formerly Category)
    industry_id = Column(UUID(as_uuid=True), Foreign<PERSON>ey("industry.id", ondelete="SET NULL"), nullable=True, index=True)

    region = Column(String(100), nullable=True, index=True)
    budget = Column(Float, nullable=True)
    brochure_url = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    embedding = Column(Vector(1536), nullable=True)

    # Relationships
    industry_rel = relationship("Industry", foreign_keys=[industry_id], lazy="select")

    def __repr__(self):
        return f"<Franchisor(id={self.id}, name={self.name}, industry_id={self.industry_id})>"