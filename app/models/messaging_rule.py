"""
Messaging Rule Model
SQLAlchemy model for messaging rule configuration
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Integer, func, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database.connection import Base


class MessagingRule(Base):
    """Messaging Rule model for GrowthHive General Settings"""

    __tablename__ = "messaging_rule"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Messaging configuration fields
    lead_init_delay_h: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="Initial delay in hours before first message to lead",
    )
    no_response_delay_h: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="Delay in hours between follow-up messages when no response",
    )
    max_followups: Mapped[int] = mapped_column(
        Integer, nullable=False, comment="Maximum number of follow-up messages allowed"
    )

    # Status fields
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        comment="Whether this messaging rule is active",
    )
    is_deleted: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, comment="Soft delete flag"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="Creation timestamp",
    )
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="Deletion timestamp"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="Last update timestamp",
    )

    # Table constraints
    __table_args__ = (
        CheckConstraint(
            "lead_init_delay_h >= 0", name="ck_lead_init_delay_non_negative"
        ),
        CheckConstraint(
            "no_response_delay_h > 0", name="ck_no_response_delay_positive"
        ),
        CheckConstraint("max_followups >= 0", name="ck_max_followups_non_negative"),
    )

    def __repr__(self) -> str:
        return (
            f"<MessagingRule(id={self.id}, lead_init_delay_h={self.lead_init_delay_h}, "
            f"no_response_delay_h={self.no_response_delay_h}, max_followups={self.max_followups}, "
            f"is_active={self.is_active})>"
        )

    def __str__(self) -> str:
        return (
            f"MessagingRule: {self.lead_init_delay_h}h init delay, "
            f"{self.no_response_delay_h}h follow-up delay, {self.max_followups} max follow-ups"
        )
