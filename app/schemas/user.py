"""User schemas for request/response validation"""
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, validator, constr
from datetime import datetime
from uuid import UUID
import re

# Custom validation messages
VALIDATION_MESSAGES = {
    "email_required": "Email address is required",
    "email_invalid": "Please provide a valid email address",
    "password_required": "Password is required",
    "password_length": "Password must be between 8 and 128 characters",
    "password_pattern": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
    "password_match": "Passwords do not match",
    "name_length": "Name must be between 2 and 50 characters",
    "name_pattern": "Name can only contain letters, spaces, and hyphens",
    "mobile_pattern": "Please provide a valid mobile number (e.g., +**********)",
    "role_invalid": "Invalid role. Must be one of: ADMIN, USER, GUEST"
}

class UserBase(BaseModel):
    """Base user schema"""
    email: EmailStr = Field(
        ...,
        description="User's email address",
        examples=["<EMAIL>"]
    )
    first_name: Optional[str] = Field(
        None,
        description="User's first name",
        min_length=2,
        max_length=50,
        examples=["John"]
    )
    last_name: Optional[str] = Field(
        None,
        description="User's last name",
        min_length=2,
        max_length=50,
        examples=["Doe"]
    )
    mobile: str = Field(
        ...,
        description="User's mobile number",
        examples=["+**********"]
    )
    role: Optional[str] = Field(
        "ADMIN",
        description="User's role (ADMIN, USER, GUEST, SUPERADMIN)",
        examples=["ADMIN"]
    )

    @validator('first_name', 'last_name')
    def validate_name(cls, v):
        """Validate name format"""
        if v is None:
            return v
        if not re.match(r'^[A-Za-z\s\-]+$', v):
            raise ValueError(VALIDATION_MESSAGES["name_pattern"])
        if len(v) < 2 or len(v) > 50:
            raise ValueError(VALIDATION_MESSAGES["name_length"])
        return v.title()  # Capitalize first letter of each word

    @validator('mobile')
    def validate_mobile(cls, v):
        """Validate mobile number format"""
        if v is None:
            return v
        if not re.match(r'^\+?[1-9]\d{1,14}$', v):
            raise ValueError(VALIDATION_MESSAGES["mobile_pattern"])
        return v

    @validator('role')
    def validate_role(cls, v):
        """Validate role"""
        valid_roles = ["ADMIN", "USER", "GUEST", "SUPERADMIN"]
        if v.upper() not in valid_roles:
            raise ValueError(VALIDATION_MESSAGES["role_invalid"])
        return v.upper()


class UserCreate(UserBase):
    """Schema for creating a new user"""
    password: constr(min_length=8, max_length=128) = Field(
        ...,
        description="User's password (min 8 chars, max 128 chars)",
        examples=["StrongP@ssw0rd"]
    )
    confirm_password: str = Field(
        ...,
        description="Password confirmation",
        examples=["StrongP@ssw0rd"]
    )

    @validator('password')
    def validate_password_strength(cls, v):
        """Validate password strength"""
        if not re.search(r'[A-Z]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        if not re.search(r'[a-z]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        if not re.search(r'[0-9]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        return v

    @validator('confirm_password')
    def validate_password_match(cls, v, values):
        """Validate password match"""
        if 'password' in values and v != values['password']:
            raise ValueError(VALIDATION_MESSAGES["password_match"])
        return v


class UserUpdate(BaseModel):
    """Schema for updating a user"""
    email: Optional[EmailStr] = Field(None, description="User email address")
    mobile: Optional[str] = Field(None, description="User mobile number")
    role: Optional[str] = Field(None, description="User role")
    password: Optional[str] = Field(None, min_length=8, description="User password")
    is_active: Optional[bool] = Field(None, description="User active status")


class UserResponse(UserBase):
    """Schema for user response"""
    id: UUID = Field(..., description="User's unique identifier")
    is_active: bool = Field(..., description="Whether the user is active")
    created_at: Optional[datetime] = Field(None, description="Account creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")

    class Config:
        """Pydantic config"""
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "mobile": "+**********",
                "role": "ADMIN",
                "is_active": True,
                "created_at": "2024-03-20T10:00:00Z",
                "updated_at": "2024-03-20T10:00:00Z"
            }
        }


class UserLoginRequest(BaseModel):
    """Schema for user login request"""
    email_or_mobile: str = Field(..., description="User email address or mobile number")
    password: str = Field(..., description="User password")
    remember_me: bool = Field(False, description="Whether to create a long-lived session")

    class Config:
        json_schema_extra = {
            "example": {
                "email_or_mobile": "<EMAIL>",
                "password": "securepassword123",
                "remember_me": True
            }
        }


class UserLoginResponse(BaseModel):
    """Schema for user login response"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserResponse = Field(..., description="User information")
    remember_me_token: Optional[str] = Field(None, description="Remember me token for persistent login")

    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 900,
                "user": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "email": "<EMAIL>",
                    "is_active": True,
                    "created_at": "2024-03-20T10:00:00Z",
                    "updated_at": "2024-03-20T10:00:00Z"
                },
                "remember_me_token": "randomly_generated_token_here"
            }
        }


class UserRegistrationRequest(BaseModel):
    """Schema for user registration request - role is always ADMIN"""
    email: EmailStr = Field(
        ...,
        description="User's email address",
        examples=["<EMAIL>"]
    )
    first_name: Optional[str] = Field(
        None,
        description="User's first name",
        min_length=2,
        max_length=50,
        examples=["John"]
    )
    last_name: Optional[str] = Field(
        None,
        description="User's last name",
        min_length=2,
        max_length=50,
        examples=["Doe"]
    )
    mobile: str = Field(
        ...,
        description="User's mobile number",
        examples=["+**********"]
    )
    password: constr(min_length=8, max_length=128) = Field(
        ...,
        description="User's password (min 8 chars, max 128 chars)",
        examples=["StrongP@ssw0rd"]
    )
    confirm_password: str = Field(
        ...,
        description="Password confirmation",
        examples=["StrongP@ssw0rd"]
    )

    @validator('first_name', 'last_name')
    def validate_name(cls, v):
        """Validate name format"""
        if v is None:
            return v
        if not re.match(r'^[A-Za-z\s\-]+$', v):
            raise ValueError(VALIDATION_MESSAGES["name_pattern"])
        if len(v) < 2 or len(v) > 50:
            raise ValueError(VALIDATION_MESSAGES["name_length"])
        return v.title()  # Capitalize first letter of each word

    @validator('mobile')
    def validate_mobile(cls, v):
        """Validate mobile number format"""
        if v is None:
            return v
        if not re.match(r'^\+?[1-9]\d{1,14}$', v):
            raise ValueError(VALIDATION_MESSAGES["mobile_pattern"])
        return v

    @validator('password')
    def validate_password_strength(cls, v):
        """Validate password strength"""
        if not re.search(r'[A-Z]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        if not re.search(r'[a-z]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        if not re.search(r'[0-9]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError(VALIDATION_MESSAGES["password_pattern"])
        return v

    @validator('confirm_password')
    def validate_password_match(cls, v, values):
        """Validate password match"""
        if 'password' in values and v != values['password']:
            raise ValueError(VALIDATION_MESSAGES["password_match"])
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "mobile": "+**********",
                "password": "StrongP@ssw0rd",
                "confirm_password": "StrongP@ssw0rd"
            }
        }


class UserRegisterResponse(BaseModel):
    """Schema for user registration response"""
    message: str = Field(..., description="Registration success message")
    user: UserResponse = Field(..., description="Registered user information")


class UserPasswordChangeRequest(BaseModel):
    """Schema for changing password"""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")

    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class UserPasswordResetRequest(BaseModel):
    """Schema for password reset request"""
    email: EmailStr = Field(..., description="User email address")


class UserPasswordResetConfirmRequest(BaseModel):
    """Schema for password reset confirmation"""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")

    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class UserPasswordResetResponse(BaseModel):
    """Schema for password reset response"""
    message: str = Field(..., description="Password reset success message")


class MessageResponse(BaseModel):
    """Schema for message response"""
    title: str = Field(..., description="Response title")
    description: str = Field(..., description="Response description")


class StandardResponse(BaseModel):
    """Schema for standard API response"""
    success: bool = Field(..., description="Operation success status")
    message: MessageResponse = Field(..., description="Response message")
    data: Optional[dict] = Field(None, description="Response data")
    error_code: int = Field(default=0, description="Error code (0 for success)")


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    email: Optional[str] = None


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class UserSessionResponse(BaseModel):
    """Schema for a single user session"""
    id: str = Field(..., description="Session ID")
    user_id: str = Field(..., description="User ID")
    device_info: Optional[dict] = Field(None, description="Device information")
    ip_address: Optional[str] = Field(None, description="IP address")
    last_activity: datetime = Field(..., description="Last activity timestamp")
    created_at: datetime = Field(..., description="Session creation timestamp")
    expires_at: datetime = Field(..., description="Session expiration timestamp")

    class Config:
        from_attributes = True


class UserSessionsResponse(BaseModel):
    """Schema for list of user sessions"""
    sessions: List[UserSessionResponse] = Field(..., description="List of user sessions")
    total: int = Field(..., description="Total number of sessions")


class RememberMeLoginRequest(BaseModel):
    """Schema for remember me login request"""
    remember_me_token: str = Field(..., description="Remember me token for persistent login")


class LogoutResponse(BaseModel):
    """Schema for logout response"""
    logout_successful: bool = Field(..., description="Whether logout was successful")
    remember_token_cleared: bool = Field(..., description="Whether remember me token was cleared")
    all_sessions_terminated: Optional[bool] = Field(None, description="Whether all sessions were terminated (for logout-all)")

    class Config:
        json_schema_extra = {
            "example": {
                "logout_successful": True,
                "remember_token_cleared": True,
                "all_sessions_terminated": True
            }
        }


class RefreshRememberTokenResponse(BaseModel):
    """Schema for refresh remember token response"""
    token_refreshed: bool = Field(..., description="Whether token was refreshed")
    new_remember_me_token: str = Field(..., description="New remember me token")

    class Config:
        json_schema_extra = {
            "example": {
                "token_refreshed": True,
                "new_remember_me_token": "new_secure_token_here"
            }
        }
