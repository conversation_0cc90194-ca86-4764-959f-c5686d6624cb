"""
Question Bank Schemas
Pydantic models for question bank API requests and responses
"""

from typing import List
from pydantic import BaseModel, Field
from datetime import datetime
import uuid




class FranchisorDropdownResponse(BaseModel):
    """Response model for franchisor dropdown."""
    id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    name: str = Field(..., description="Franchisor name", example="McDonald's")
    category: str = Field(..., description="Franchisor category", example="Food & Beverage")


class QuestionCreateRequest(BaseModel):
    """Request model for creating a new question."""
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
 

class QuestionUpdateRequest(BaseModel):
    """Request model for updating a question."""
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
  

class QuestionResponse(BaseModel):
    """Response model for question details."""
    id: uuid.UUID = Field(..., description="Question ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_name: str = Field(..., description="Franchisor name", example="McDonald's")
    category: str = Field(..., description="Franchisor category", example="Food & Beverage")
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_internal_text: str = Field(..., description="Original question text (immutable)", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
    order_sequence: int = Field(..., description="Question order", example=1)
    is_active: bool = Field(..., description="Question active status", example=True)
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class QuestionListResponse(BaseModel):
    """Response model for question list."""
    id: uuid.UUID = Field(..., description="Question ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    franchisor_name: str = Field(..., description="Franchisor name", example="McDonald's")
    category: str = Field(..., description="Franchisor category", example="Food & Beverage")
    question_text: str = Field(..., description="Question text", example="What is your investment budget?")
    question_type: str = Field(..., description="Type of question", example="multiple_choice")
    order_sequence: int = Field(..., description="Question order", example=1)
    is_active: bool = Field(..., description="Question active status", example=True)
    created_at: datetime = Field(..., description="Creation timestamp")


class QuestionReorderItem(BaseModel):
    """Model for reordering question items."""
    question_id: uuid.UUID = Field(..., description="Question ID", example="550e8400-e29b-41d4-a716-************")
    order_sequence: int = Field(..., description="New order position", example=1)


class QuestionReorderRequest(BaseModel):
    """Request model for reordering questions."""
    franchisor_id: uuid.UUID = Field(..., description="Franchisor ID", example="550e8400-e29b-41d4-a716-************")
    questions: List[QuestionReorderItem] = Field(..., description="List of questions with new orders")


class QuestionToggleRequest(BaseModel):
    """Request model for toggling question status."""
    question_id: uuid.UUID = Field(..., description="Question ID to toggle", example="550e8400-e29b-41d4-a716-************")


class QuestionDeleteRequest(BaseModel):
    """Request model for deleting a question."""
    question_id: uuid.UUID = Field(..., description="Question ID to delete", example="550e8400-e29b-41d4-a716-************")


class QuestionListPaginatedResponse(BaseModel):
    """Paginated response model for question list."""
    items: List[QuestionListResponse] = Field(..., description="List of questions")
    total: int = Field(..., description="Total number of questions", example=100)
    page: int = Field(..., description="Current page number", example=1)
    size: int = Field(..., description="Page size", example=10)
    pages: int = Field(..., description="Total number of pages", example=10) 