"""
Industry schemas for API requests and responses (formerly Category)
"""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from app.schemas.base_response import ResponseMessage


class IndustryBase(BaseModel):
    """Base industry schema"""
    name: str = Field(..., description="Industry name", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Industry description", max_length=255)
    is_active: bool = Field(True, description="Whether the industry is active")


class IndustryCreateRequest(BaseModel):
    """Request model for creating an industry"""
    name: str = Field(..., description="Industry name", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Industry description", max_length=255)
    is_active: bool = Field(True, description="Whether the industry is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Food & Beverage",
                "description": "Restaurants, cafes, and food service businesses",
                "is_active": True
            }
        }
    )


class IndustryUpdateRequest(BaseModel):
    """Request model for updating an industry"""
    name: Optional[str] = Field(None, description="Industry name", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Industry description", max_length=255)
    is_active: Optional[bool] = Field(None, description="Whether the industry is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Food & Beverage Updated",
                "description": "Updated description for food service businesses",
                "is_active": True
            }
        }
    )


class IndustryResponse(BaseModel):
    """Response model for industry data"""
    id: str = Field(..., description="Industry ID")
    name: str = Field(..., description="Industry name")
    description: Optional[str] = Field(None, description="Industry description")
    is_active: bool = Field(..., description="Whether the industry is active")
    is_deleted: bool = Field(..., description="Whether the industry is deleted")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "name": "Food & Beverage",
                "description": "Restaurants, cafes, and food service businesses",
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z"
            }
        }
    )


class IndustryListResponse(BaseModel):
    """Response model for industry list"""
    items: List[IndustryResponse] = Field(..., description="List of industries")
    total_count: int = Field(..., description="Total number of industries")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "items": [
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "name": "Food & Beverage",
                        "description": "Restaurants, cafes, and food service businesses",
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T12:00:00Z"
                    }
                ],
                "total_count": 1
            }
        }
    )


class IndustryListSuccessResponse(BaseModel):
    """Success response model for industry list"""
    success: bool = Field(True, description="Success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: IndustryListResponse = Field(..., description="Industry list data")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "status": "success",
                "message": {
                    "title": "Industries Retrieved",
                    "description": "Industries retrieved successfully"
                },
                "data": {
                    "items": [
                        {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "name": "Food & Beverage",
                            "description": "Restaurants, cafes, and food service businesses",
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T12:00:00Z"
                        }
                    ],
                    "total_count": 1
                }
            }
        }
    )


# Keep Category schemas as aliases for backward compatibility
CategoryBase = IndustryBase
CategoryCreateRequest = IndustryCreateRequest
CategoryUpdateRequest = IndustryUpdateRequest
CategoryResponse = IndustryResponse
CategoryListResponse = IndustryListResponse
CategoryListSuccessResponse = IndustryListSuccessResponse
