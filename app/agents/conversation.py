"""
Conversation Agent
Handles user greetings, general conversation, and out-of-scope queries
"""

from typing import Dict, Any, List
import structlog
from langchain_core.messages import AIMessage

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry

logger = structlog.get_logger()


class ConversationAgent(BaseAgent):
    """
    Agent responsible for handling general conversation,
    greetings, and out-of-scope queries
    """
    
    def _initialize_tools(self):
        """Initialize conversation-specific tools"""
        tool_names = ["store_memory", "retrieve_memory", "validate_email", "validate_phone"]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process conversation requests"""
        try:
            user_input = state.get("user_input", "")
            intent = state.get("intent", "")
            context = state.get("context", {})
            
            # Handle different conversation scenarios
            if intent == "greeting":
                response = await self._handle_greeting(user_input, context)
            elif intent == "out_of_scope":
                response = await self._handle_out_of_scope(user_input)
            else:
                response = await self._handle_general_conversation(user_input, context)
            
            # Update state
            state["response"] = response
            state["next_action"] = "end"
            state["messages"] = state.get("messages", []) + [AIMessage(content=response)]
            
            # Store conversation in memory
            await self._store_conversation_memory(state)
            
            logger.info("Conversation processed successfully")
            return state
            
        except Exception as e:
            logger.error(f"Error in conversation agent: {str(e)}")
            state["error"] = str(e)
            return state
    
    async def _handle_greeting(self, user_input: str, context: Dict[str, Any]) -> str:
        """Handle greeting messages"""
        greeting_prompt = f"""
        You are a friendly AI assistant for GrowthHive, a franchise consulting company.
        
        The user said: "{user_input}"
        
        Respond with a warm, professional greeting and briefly explain how you can help with:
        - Answering questions about franchise opportunities
        - Providing information from franchise brochures
        - Helping with lead qualification
        - Scheduling meetings with franchise consultants
        
        Keep the response conversational and inviting.
        """
        
        response = await self.llm.ainvoke(greeting_prompt)
        return response.content.strip()
    
    async def _handle_out_of_scope(self, user_input: str) -> str:
        """Handle out-of-scope queries"""
        out_of_scope_responses = [
            "I'm specialized in helping with franchise-related questions and opportunities. Is there anything about franchising I can help you with?",
            "I focus on franchise consulting and opportunities. Would you like to know about available franchise options or schedule a consultation?",
            "My expertise is in franchise opportunities and business consulting. How can I assist you with your franchise interests?",
            "I'm here to help with franchise-related inquiries. Would you like to explore franchise opportunities or get more information about specific brands?"
        ]
        
        # Use LLM to generate a contextual out-of-scope response
        out_of_scope_prompt = f"""
        The user asked: "{user_input}"
        
        This question is outside the scope of franchise consulting. 
        Politely redirect them to franchise-related topics while being helpful.
        
        Suggest they ask about:
        - Available franchise opportunities
        - Franchise investment requirements
        - Franchise support and training
        - Scheduling a consultation
        
        Be friendly but clear about your role.
        """
        
        response = await self.llm.ainvoke(out_of_scope_prompt)
        return response.content.strip()
    
    async def _handle_general_conversation(self, user_input: str, context: Dict[str, Any]) -> str:
        """Handle general conversation within scope"""
        conversation_prompt = f"""
        You are a knowledgeable franchise consultant AI assistant.
        
        User message: "{user_input}"
        Context: {context}
        
        Provide a helpful response that:
        1. Addresses their question or comment
        2. Keeps the conversation focused on franchising
        3. Offers to help with specific franchise-related tasks
        4. Is professional yet conversational
        
        If they seem interested in franchising, gently guide them toward:
        - Learning about specific franchise opportunities
        - Understanding investment requirements
        - Scheduling a consultation
        """
        
        response = await self.llm.ainvoke(conversation_prompt)
        return response.content.strip()
    
    async def _store_conversation_memory(self, state: AgentState):
        """Store conversation in memory for context"""
        try:
            memory_tool = tool_registry.get_tool("store_memory")
            if memory_tool:
                memory_data = {
                    "session_id": state.get("session_id"),
                    "user_input": state.get("user_input"),
                    "response": state.get("response"),
                    "intent": state.get("intent"),
                    "timestamp": state.get("metadata", {}).get("timestamp")
                }
                
                # Store memory (implementation depends on memory tool)
                # await memory_tool.arun(memory_data)
                
        except Exception as e:
            logger.warning(f"Failed to store conversation memory: {str(e)}")
    
    def get_conversation_capabilities(self) -> List[str]:
        """Get list of conversation capabilities"""
        return [
            "Greeting and welcome messages",
            "General franchise information",
            "Redirecting out-of-scope queries", 
            "Maintaining conversation context",
            "Guiding users to appropriate services"
        ]
