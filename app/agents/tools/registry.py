"""
Tool Registry for Agent System
Manages and provides access to all available tools
"""

from typing import Dict, List, Optional, Type
from langchain_core.tools import BaseTool
import structlog

logger = structlog.get_logger()


class ToolRegistry:
    """
    Central registry for all agent tools
    Provides tool discovery, instantiation, and management
    """
    
    def __init__(self):
        self._tools: Dict[str, Type[BaseTool]] = {}
        self._instances: Dict[str, BaseTool] = {}
        self._tool_categories: Dict[str, List[str]] = {}
        self._initialize_default_tools()
    
    def _initialize_default_tools(self):
        """Initialize default tools available to all agents"""
        from .database_tools import (
            CreateLeadTool,
            UpdateLeadStatusTool,
            GetLeadTool,
            CreateCommunicationTool
        )
        from .document_tools import (
            IngestDocumentTool,
            SearchDocumentsTool,
            ExtractTextTool
        )
        from .meeting_tools import (
            BookMeetingTool,
            CheckAvailabilityTool,
            CancelMeetingTool
        )
        from .memory_tools import (
            StoreMemoryTool,
            RetrieveMemoryTool,
            UpdateContextTool
        )
        from .validation_tools import (
            ValidateEmailTool,
            ValidatePhoneTool,
            ValidateLeadDataTool
        )
        
        # Register database tools
        self.register_tool("create_lead", CreateLeadTool)
        self.register_tool("update_lead_status", UpdateLeadStatusTool)
        self.register_tool("get_lead", GetLeadTool)
        self.register_tool("create_communication", CreateCommunicationTool)
        
        # Register document tools
        self.register_tool("ingest_document", IngestDocumentTool)
        self.register_tool("search_documents", SearchDocumentsTool)
        self.register_tool("extract_text", ExtractTextTool)
        
        # Register meeting tools
        self.register_tool("book_meeting", BookMeetingTool)
        self.register_tool("check_availability", CheckAvailabilityTool)
        self.register_tool("cancel_meeting", CancelMeetingTool)
        
        # Register memory tools
        self.register_tool("store_memory", StoreMemoryTool)
        self.register_tool("retrieve_memory", RetrieveMemoryTool)
        self.register_tool("update_context", UpdateContextTool)
        
        # Register validation tools
        self.register_tool("validate_email", ValidateEmailTool)
        self.register_tool("validate_phone", ValidatePhoneTool)
        self.register_tool("validate_lead_data", ValidateLeadDataTool)
        
        # Define tool categories
        self._tool_categories = {
            "database": ["create_lead", "update_lead_status", "get_lead", "create_communication"],
            "document": ["ingest_document", "search_documents", "extract_text"],
            "meeting": ["book_meeting", "check_availability", "cancel_meeting"],
            "memory": ["store_memory", "retrieve_memory", "update_context"],
            "validation": ["validate_email", "validate_phone", "validate_lead_data"]
        }
        
        logger.info(f"Initialized tool registry with {len(self._tools)} tools")
    
    def register_tool(self, name: str, tool_class: Type[BaseTool]):
        """Register a new tool in the registry"""
        self._tools[name] = tool_class
        logger.debug(f"Registered tool: {name}")
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool instance by name"""
        if name not in self._instances:
            if name in self._tools:
                self._instances[name] = self._tools[name]()
            else:
                logger.warning(f"Tool not found: {name}")
                return None
        
        return self._instances[name]
    
    def get_tools_by_category(self, category: str) -> List[BaseTool]:
        """Get all tools in a specific category"""
        tool_names = self._tool_categories.get(category, [])
        return [self.get_tool(name) for name in tool_names if self.get_tool(name)]
    
    def get_tools_by_names(self, names: List[str]) -> List[BaseTool]:
        """Get multiple tools by their names"""
        return [self.get_tool(name) for name in names if self.get_tool(name)]
    
    def list_available_tools(self) -> List[str]:
        """List all available tool names"""
        return list(self._tools.keys())
    
    def list_categories(self) -> List[str]:
        """List all available tool categories"""
        return list(self._tool_categories.keys())
    
    def get_tool_info(self, name: str) -> Optional[Dict[str, str]]:
        """Get information about a specific tool"""
        tool = self.get_tool(name)
        if tool:
            return {
                "name": tool.name,
                "description": tool.description,
                "args_schema": str(tool.args_schema) if tool.args_schema else None
            }
        return None


# Global tool registry instance
tool_registry = ToolRegistry()
