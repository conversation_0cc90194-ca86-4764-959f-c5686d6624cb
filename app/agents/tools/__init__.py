"""
Tool Registry and Management System
Centralized tool management for all agents
"""

from .registry import ToolRegistry
from .database_tools import (
    Create<PERSON>eadTool,
    UpdateLeadStatusTool,
    GetLeadTool,
    CreateCommunicationTool,
    SearchLeadsTool
)
from .document_tools import (
    IngestDocumentTool,
    SearchDocumentsTool,
    ExtractTextTool,
    SummarizeDocumentTool
)
from .meeting_tools import (
    BookMeetingTool,
    CheckAvailabilityTool,
    CancelMeetingTool,
    GetMeetingDetailsTool
)
from .memory_tools import (
    StoreMemoryTool,
    RetrieveMemoryTool,
    UpdateContextTool,
    GetConversationHistoryTool,
    AddToHistoryTool,
    ClearSessionTool
)
from .validation_tools import (
    ValidateEmailTool,
    ValidatePhoneTool,
    ValidateLeadDataTool,
    ValidateBudgetTool,
    ValidateTimelineTool
)

__all__ = [
    "ToolRegistry",
    # Database tools
    "CreateLeadTool",
    "UpdateLeadStatusTool",
    "GetLeadTool",
    "CreateCommunicationTool",
    "SearchLeadsTool",
    # Document tools
    "IngestDocumentTool",
    "SearchDocumentsTool",
    "ExtractTextTool",
    "SummarizeDocumentTool",
    # Meeting tools
    "BookMeetingTool",
    "CheckAvailabilityTool",
    "CancelMeetingTool",
    "GetMeetingDetailsTool",
    # Memory tools
    "StoreMemoryTool",
    "RetrieveMemoryTool",
    "UpdateContextTool",
    "GetConversationHistoryTool",
    "AddToHistoryTool",
    "ClearSessionTool",
    # Validation tools
    "ValidateEmailTool",
    "ValidatePhoneTool",
    "ValidateLeadDataTool",
    "ValidateBudgetTool",
    "ValidateTimelineTool"
]
