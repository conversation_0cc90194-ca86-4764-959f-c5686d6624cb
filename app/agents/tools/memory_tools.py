"""
Memory Tools for Agent System
Tools for managing conversation memory and context using Redis
"""

from typing import Dict, Any, Optional
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import structlog
import json
from datetime import datetime
import redis

logger = structlog.get_logger()

# Redis connection
try:
    redis_client = redis.Redis(
        host='localhost',
        port=6379,
        db=2,  # Use database 2 for session memory
        decode_responses=True
    )
except Exception as e:
    logger.warning(f"Redis connection failed: {str(e)}")
    redis_client = None


class StoreMemoryInput(BaseModel):
    """Input schema for storing memory"""
    session_id: str = Field(description="Session ID")
    key: str = Field(description="Memory key")
    value: Any = Field(description="Value to store")
    ttl: Optional[int] = Field(3600, description="Time to live in seconds (default 1 hour)")


class StoreMemoryTool(BaseTool):
    """Tool for storing information in session memory"""

    name: str = "store_memory"
    description: str = "Store information in session memory for later retrieval"
    args_schema: type = StoreMemoryInput

    def _run(self, **kwargs) -> str:
        """Store memory (sync version)"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Store memory"""
        try:
            if not redis_client:
                return "Memory storage not available (Redis not connected)"
            
            session_id = kwargs["session_id"]
            key = kwargs["key"]
            value = kwargs["value"]
            ttl = kwargs.get("ttl", 3600)
            
            # Create memory key
            memory_key = f"session:{session_id}:memory:{key}"
            
            # Store value as JSON
            redis_client.setex(
                memory_key,
                ttl,
                json.dumps({
                    "value": value,
                    "timestamp": datetime.utcnow().isoformat(),
                    "session_id": session_id
                })
            )
            
            logger.info(f"Stored memory for session {session_id}, key {key}")
            return f"Memory stored successfully for key: {key}"
            
        except Exception as e:
            logger.error(f"Error storing memory: {str(e)}")
            return f"Error storing memory: {str(e)}"


class RetrieveMemoryInput(BaseModel):
    """Input schema for retrieving memory"""
    session_id: str = Field(description="Session ID")
    key: str = Field(description="Memory key to retrieve")


class RetrieveMemoryTool(BaseTool):
    """Tool for retrieving information from session memory"""

    name: str = "retrieve_memory"
    description: str = "Retrieve previously stored information from session memory"
    args_schema: type = RetrieveMemoryInput

    def _run(self, **kwargs) -> str:
        """Retrieve memory (sync version)"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Retrieve memory"""
        try:
            if not redis_client:
                return "Memory retrieval not available (Redis not connected)"
            
            session_id = kwargs["session_id"]
            key = kwargs["key"]
            
            # Create memory key
            memory_key = f"session:{session_id}:memory:{key}"
            
            # Retrieve value
            stored_data = redis_client.get(memory_key)
            
            if stored_data:
                data = json.loads(stored_data)
                return str(data["value"])
            else:
                return f"No memory found for key: {key}"
                
        except Exception as e:
            logger.error(f"Error retrieving memory: {str(e)}")
            return f"Error retrieving memory: {str(e)}"


class UpdateContextInput(BaseModel):
    """Input schema for updating conversation context"""
    session_id: str = Field(description="Session ID")
    context_update: Dict[str, Any] = Field(description="Context updates")


class UpdateContextTool(BaseTool):
    """Tool for updating conversation context"""

    name: str = "update_context"
    description: str = "Update the conversation context with new information"
    args_schema: type = UpdateContextInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Update context"""
        try:
            if not redis_client:
                return "Context update not available (Redis not connected)"
            
            session_id = kwargs["session_id"]
            context_update = kwargs["context_update"]
            
            # Get current context
            context_key = f"session:{session_id}:context"
            current_context = redis_client.get(context_key)
            
            if current_context:
                context = json.loads(current_context)
            else:
                context = {}
            
            # Update context
            context.update(context_update)
            context["last_updated"] = datetime.utcnow().isoformat()
            
            # Store updated context (24 hour TTL)
            redis_client.setex(
                context_key,
                86400,  # 24 hours
                json.dumps(context)
            )
            
            logger.info(f"Updated context for session {session_id}")
            return "Context updated successfully"
            
        except Exception as e:
            logger.error(f"Error updating context: {str(e)}")
            return f"Error updating context: {str(e)}"


class GetConversationHistoryInput(BaseModel):
    """Input schema for getting conversation history"""
    session_id: str = Field(description="Session ID")
    limit: int = Field(10, description="Maximum number of messages to retrieve")


class GetConversationHistoryTool(BaseTool):
    """Tool for retrieving conversation history"""

    name: str = "get_conversation_history"
    description: str = "Get the conversation history for a session"
    args_schema: type = GetConversationHistoryInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Get conversation history"""
        try:
            if not redis_client:
                return "Conversation history not available (Redis not connected)"
            
            session_id = kwargs["session_id"]
            limit = kwargs.get("limit", 10)
            
            # Get conversation history
            history_key = f"session:{session_id}:history"
            history = redis_client.lrange(history_key, -limit, -1)
            
            if history:
                messages = [json.loads(msg) for msg in history]
                return str(messages)
            else:
                return "No conversation history found"
                
        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}")
            return f"Error getting conversation history: {str(e)}"


class AddToHistoryInput(BaseModel):
    """Input schema for adding to conversation history"""
    session_id: str = Field(description="Session ID")
    message: Dict[str, Any] = Field(description="Message to add to history")


class AddToHistoryTool(BaseTool):
    """Tool for adding messages to conversation history"""

    name: str = "add_to_history"
    description: str = "Add a message to the conversation history"
    args_schema: type = AddToHistoryInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Add to history"""
        try:
            if not redis_client:
                return "History storage not available (Redis not connected)"
            
            session_id = kwargs["session_id"]
            message = kwargs["message"]
            
            # Add timestamp if not present
            if "timestamp" not in message:
                message["timestamp"] = datetime.utcnow().isoformat()
            
            # Add to history list
            history_key = f"session:{session_id}:history"
            redis_client.lpush(history_key, json.dumps(message))
            
            # Keep only last 100 messages
            redis_client.ltrim(history_key, 0, 99)
            
            # Set expiry (7 days)
            redis_client.expire(history_key, 604800)
            
            return "Message added to history"
            
        except Exception as e:
            logger.error(f"Error adding to history: {str(e)}")
            return f"Error adding to history: {str(e)}"


class ClearSessionInput(BaseModel):
    """Input schema for clearing session data"""
    session_id: str = Field(description="Session ID")


class ClearSessionTool(BaseTool):
    """Tool for clearing all session data"""

    name: str = "clear_session"
    description: str = "Clear all data for a session (memory, context, history)"
    args_schema: type = ClearSessionInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Clear session"""
        try:
            if not redis_client:
                return "Session clearing not available (Redis not connected)"
            
            session_id = kwargs["session_id"]
            
            # Find all keys for this session
            pattern = f"session:{session_id}:*"
            keys = redis_client.keys(pattern)
            
            if keys:
                redis_client.delete(*keys)
                logger.info(f"Cleared {len(keys)} keys for session {session_id}")
                return f"Session cleared successfully ({len(keys)} keys removed)"
            else:
                return "No session data found to clear"
                
        except Exception as e:
            logger.error(f"Error clearing session: {str(e)}")
            return f"Error clearing session: {str(e)}"
