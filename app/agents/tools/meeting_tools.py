"""
Meeting Tools for Agent System
Tools for booking meetings and managing calendar with Zoho Meetings integration
"""

from typing import Dict, Any, Optional, List
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import structlog
import uuid
from datetime import datetime, timedelta
import httpx
import os

logger = structlog.get_logger()


class BookMeetingInput(BaseModel):
    """Input schema for booking meetings"""
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    datetime_str: str = Field(description="Meeting date and time (ISO format)")
    duration: int = Field(30, description="Meeting duration in minutes")
    meeting_type: str = Field("consultation", description="Type of meeting")
    attendee_email: Optional[str] = Field(None, description="Attendee email address")
    attendee_name: Optional[str] = Field(None, description="Attendee name")
    notes: Optional[str] = Field(None, description="Additional notes")


class BookMeetingTool(BaseTool):
    """Tool for booking meetings via Zoho Meetings"""
    
    name: str = "book_meeting"
    description: str = "Book a meeting/consultation via Zoho Meetings"
    args_schema: type = BookMeetingInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Book meeting"""
        try:
            # Extract parameters
            lead_id = kwargs.get("lead_id")
            datetime_str = kwargs["datetime_str"]
            duration = kwargs.get("duration", 30)
            meeting_type = kwargs.get("meeting_type", "consultation")
            attendee_email = kwargs.get("attendee_email")
            attendee_name = kwargs.get("attendee_name")
            notes = kwargs.get("notes", "")
            
            # Parse datetime
            meeting_datetime = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            
            # Create meeting via Zoho API
            meeting_data = await self._create_zoho_meeting(
                meeting_datetime=meeting_datetime,
                duration=duration,
                attendee_email=attendee_email,
                attendee_name=attendee_name,
                notes=notes
            )
            
            if meeting_data:
                # Store meeting in database if needed
                await self._store_meeting_record(
                    meeting_data=meeting_data,
                    lead_id=lead_id,
                    meeting_type=meeting_type
                )
                
                return f"Meeting booked successfully! Meeting ID: {meeting_data['meeting_id']}, Join URL: {meeting_data['join_url']}"
            else:
                return "Failed to book meeting. Please try again or contact support."
                
        except Exception as e:
            logger.error(f"Error booking meeting: {str(e)}")
            return f"Error booking meeting: {str(e)}"
    
    async def _create_zoho_meeting(
        self, 
        meeting_datetime: datetime, 
        duration: int,
        attendee_email: Optional[str] = None,
        attendee_name: Optional[str] = None,
        notes: str = ""
    ) -> Optional[Dict[str, Any]]:
        """Create meeting via Zoho Meetings API"""
        try:
            # Zoho Meetings API configuration
            client_id = os.getenv("ZOHO_CLIENT_ID")
            client_secret = os.getenv("ZOHO_CLIENT_SECRET")
            
            if not client_id or not client_secret:
                logger.warning("Zoho credentials not configured")
                # Return mock meeting data for development
                return {
                    "meeting_id": f"mock_meeting_{uuid.uuid4().hex[:8]}",
                    "join_url": f"https://meeting.zoho.com/meeting/mock_meeting_{uuid.uuid4().hex[:8]}",
                    "start_time": meeting_datetime.isoformat(),
                    "duration": duration,
                    "status": "scheduled"
                }
            
            # Get access token (this would typically be cached)
            access_token = await self._get_zoho_access_token(client_id, client_secret)
            
            if not access_token:
                return None
            
            # Prepare meeting data
            meeting_payload = {
                "session": {
                    "topic": "Franchise Consultation",
                    "agenda": f"Franchise consultation meeting. {notes}",
                    "presenter": attendee_email or "<EMAIL>",
                    "startTime": meeting_datetime.strftime("%b %d, %Y %H:%M:%S"),
                    "duration": duration,
                    "timezone": "Australia/Sydney",
                    "participants": [
                        {
                            "email": attendee_email or "<EMAIL>",
                            "name": attendee_name or "Participant"
                        }
                    ]
                }
            }
            
            # Make API request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://meeting.zoho.com/api/v2/sessions.json",
                    headers={
                        "Authorization": f"Zoho-oauthtoken {access_token}",
                        "Content-Type": "application/json"
                    },
                    json=meeting_payload,
                    timeout=30
                )
                
                if response.status_code == 201:
                    result = response.json()
                    session_data = result.get("session", {})
                    
                    return {
                        "meeting_id": session_data.get("sessionKey"),
                        "join_url": session_data.get("joinUrl"),
                        "start_time": meeting_datetime.isoformat(),
                        "duration": duration,
                        "status": "scheduled"
                    }
                else:
                    logger.error(f"Zoho API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error creating Zoho meeting: {str(e)}")
            return None
    
    async def _get_zoho_access_token(self, client_id: str, client_secret: str) -> Optional[str]:
        """Get Zoho access token (simplified - in production, implement proper OAuth flow)"""
        try:
            # This is a simplified version - in production, implement proper OAuth 2.0 flow
            # For now, return None to use mock data
            return None
            
        except Exception as e:
            logger.error(f"Error getting Zoho access token: {str(e)}")
            return None
    
    async def _store_meeting_record(
        self, 
        meeting_data: Dict[str, Any], 
        lead_id: Optional[str],
        meeting_type: str
    ):
        """Store meeting record in database"""
        try:
            # This would store the meeting in your database
            # For now, just log it
            logger.info(f"Meeting record stored: {meeting_data['meeting_id']} for lead {lead_id}")
            
        except Exception as e:
            logger.error(f"Error storing meeting record: {str(e)}")


class CheckAvailabilityInput(BaseModel):
    """Input schema for checking availability"""
    start_date: str = Field(description="Start date for availability check (ISO format)")
    end_date: str = Field(description="End date for availability check (ISO format)")
    duration: int = Field(30, description="Meeting duration in minutes")


class CheckAvailabilityTool(BaseTool):
    """Tool for checking calendar availability"""

    name: str = "check_availability"
    description: str = "Check calendar availability for meeting scheduling"
    args_schema: type = CheckAvailabilityInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Check availability"""
        try:
            start_date = datetime.fromisoformat(kwargs["start_date"].replace('Z', '+00:00'))
            end_date = datetime.fromisoformat(kwargs["end_date"].replace('Z', '+00:00'))
            duration = kwargs.get("duration", 30)
            
            # Generate available time slots
            available_slots = await self._generate_available_slots(start_date, end_date, duration)
            
            if available_slots:
                return f"Available time slots: {available_slots}"
            else:
                return "No available time slots found in the specified range"
                
        except Exception as e:
            logger.error(f"Error checking availability: {str(e)}")
            return f"Error checking availability: {str(e)}"
    
    async def _generate_available_slots(
        self, 
        start_date: datetime, 
        end_date: datetime, 
        duration: int
    ) -> List[Dict[str, Any]]:
        """Generate available time slots"""
        try:
            slots = []
            current_date = start_date.date()
            end_date_only = end_date.date()
            
            while current_date <= end_date_only:
                # Skip weekends
                if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                    # Business hours: 9 AM to 5 PM
                    for hour in range(9, 17):
                        slot_datetime = datetime.combine(current_date, datetime.min.time().replace(hour=hour))
                        
                        # Check if slot is in the future
                        if slot_datetime > datetime.now():
                            slots.append({
                                "datetime": slot_datetime.isoformat(),
                                "display_time": slot_datetime.strftime("%A, %B %d at %I:%M %p"),
                                "duration": duration,
                                "available": True
                            })
                
                current_date += timedelta(days=1)
            
            return slots[:20]  # Return first 20 slots
            
        except Exception as e:
            logger.error(f"Error generating available slots: {str(e)}")
            return []


class CancelMeetingInput(BaseModel):
    """Input schema for canceling meetings"""
    meeting_id: str = Field(description="Meeting ID to cancel")
    reason: Optional[str] = Field(None, description="Cancellation reason")


class CancelMeetingTool(BaseTool):
    """Tool for canceling meetings"""
    
    name: str = "cancel_meeting"
    description: str = "Cancel a scheduled meeting"
    args_schema: type = CancelMeetingInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Cancel meeting"""
        try:
            meeting_id = kwargs["meeting_id"]
            reason = kwargs.get("reason", "Cancelled by user")
            
            # Cancel meeting via Zoho API
            success = await self._cancel_zoho_meeting(meeting_id, reason)
            
            if success:
                # Update meeting record in database
                await self._update_meeting_status(meeting_id, "cancelled", reason)
                return f"Meeting {meeting_id} cancelled successfully"
            else:
                return f"Failed to cancel meeting {meeting_id}"
                
        except Exception as e:
            logger.error(f"Error canceling meeting: {str(e)}")
            return f"Error canceling meeting: {str(e)}"
    
    async def _cancel_zoho_meeting(self, meeting_id: str, reason: str) -> bool:
        """Cancel meeting via Zoho API"""
        try:
            # This would make API call to cancel the meeting
            # For now, just return True for mock implementation
            logger.info(f"Mock cancellation of meeting {meeting_id}: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"Error canceling Zoho meeting: {str(e)}")
            return False
    
    async def _update_meeting_status(self, meeting_id: str, status: str, reason: str):
        """Update meeting status in database"""
        try:
            # This would update the meeting status in your database
            logger.info(f"Updated meeting {meeting_id} status to {status}: {reason}")
            
        except Exception as e:
            logger.error(f"Error updating meeting status: {str(e)}")


class GetMeetingDetailsInput(BaseModel):
    """Input schema for getting meeting details"""
    meeting_id: str = Field(description="Meeting ID")


class GetMeetingDetailsTool(BaseTool):
    """Tool for getting meeting details"""
    
    name: str = "get_meeting_details"
    description: str = "Get details of a scheduled meeting"
    args_schema: type = GetMeetingDetailsInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Get meeting details"""
        try:
            meeting_id = kwargs["meeting_id"]
            
            # Retrieve meeting details from database or API
            meeting_details = await self._get_meeting_from_database(meeting_id)
            
            if meeting_details:
                return str(meeting_details)
            else:
                return f"Meeting {meeting_id} not found"
                
        except Exception as e:
            logger.error(f"Error getting meeting details: {str(e)}")
            return f"Error getting meeting details: {str(e)}"
    
    async def _get_meeting_from_database(self, meeting_id: str) -> Optional[Dict[str, Any]]:
        """Get meeting details from database"""
        try:
            # Mock meeting details
            return {
                "meeting_id": meeting_id,
                "status": "scheduled",
                "start_time": "2024-01-15T14:00:00",
                "duration": 30,
                "join_url": f"https://meeting.zoho.com/meeting/{meeting_id}",
                "attendees": ["<EMAIL>", "<EMAIL>"]
            }
            
        except Exception as e:
            logger.error(f"Error retrieving meeting from database: {str(e)}")
            return None
