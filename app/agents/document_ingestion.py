"""
Document Ingestion Agent
Handles document processing, text extraction, and embedding generation
"""

from typing import Dict, Any, List, Optional
import structlog
import uuid
from langchain_core.messages import AIMessage

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry

logger = structlog.get_logger()


class DocumentIngestionAgent(BaseAgent):
    """
    Agent responsible for ingesting documents, extracting text,
    and generating embeddings for the knowledge base
    """
    
    def _initialize_tools(self):
        """Initialize document ingestion tools"""
        tool_names = ["ingest_document", "extract_text", "store_memory"]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process document ingestion requests"""
        try:
            user_input = state.get("user_input", "")
            context = state.get("context", {})
            
            # Check if document is provided in context
            document_path = context.get("document_path")
            document_url = context.get("document_url")
            document_content = context.get("document_content")
            
            if document_path or document_url:
                # Process document from file/URL
                result = await self._process_document_file(document_path or document_url, context)
            elif document_content:
                # Process document content directly
                result = await self._process_document_content(document_content, context)
            else:
                # No document provided, ask for it
                result = await self._request_document_upload()
            
            # Update state
            state["response"] = result["message"]
            state["document_id"] = result.get("document_id")
            state["next_action"] = "end"
            state["messages"] = state.get("messages", []) + [AIMessage(content=result["message"])]
            
            logger.info("Document ingestion processed successfully")
            return state
            
        except Exception as e:
            logger.error(f"Error in document ingestion agent: {str(e)}")
            state["error"] = str(e)
            return state
    
    async def _process_document_file(self, file_path: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process document from file path or URL"""
        try:
            # Extract text from document
            extracted_text = await self._extract_text_from_file(file_path)
            
            if not extracted_text:
                return {
                    "success": False,
                    "message": "I wasn't able to extract text from that document. Please ensure it's a supported format (PDF, DOC, TXT, etc.)."
                }
            
            # Process and chunk the text
            chunks = await self._chunk_document(extracted_text)
            
            # Generate embeddings and store
            document_id = await self._store_document_embeddings(chunks, file_path, context)
            
            # Generate summary
            summary = await self._generate_document_summary(extracted_text)
            
            return {
                "success": True,
                "document_id": document_id,
                "message": f"Document processed successfully! I've analyzed the content and it's now available for questions.\n\nSummary: {summary}\n\nYou can now ask me questions about this document."
            }
            
        except Exception as e:
            logger.error(f"Error processing document file: {str(e)}")
            return {
                "success": False,
                "message": "I encountered an error processing your document. Please try again or contact support."
            }
    
    async def _process_document_content(self, content: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process document content directly"""
        try:
            # Process and chunk the text
            chunks = await self._chunk_document(content)
            
            # Generate embeddings and store
            document_id = await self._store_document_embeddings(chunks, "direct_input", context)
            
            # Generate summary
            summary = await self._generate_document_summary(content)
            
            return {
                "success": True,
                "document_id": document_id,
                "message": f"Content processed successfully! I've analyzed the information and it's now available for questions.\n\nSummary: {summary}\n\nYou can now ask me questions about this content."
            }
            
        except Exception as e:
            logger.error(f"Error processing document content: {str(e)}")
            return {
                "success": False,
                "message": "I encountered an error processing your content. Please try again."
            }
    
    async def _request_document_upload(self) -> Dict[str, Any]:
        """Request document upload from user"""
        return {
            "success": False,
            "message": """I'd be happy to help you process a document! You can:

1. Upload a PDF, Word document, or text file
2. Paste text content directly
3. Provide a URL to a document

Once I have your document, I'll:
- Extract and analyze the content
- Make it searchable for questions
- Provide a summary of key information

What document would you like me to process?"""
        }
    
    async def _extract_text_from_file(self, file_path: str) -> Optional[str]:
        """Extract text from various file formats"""
        try:
            extract_tool = tool_registry.get_tool("extract_text")
            if extract_tool:
                # Extract text using tool (implementation depends on tool)
                # extracted_text = await extract_tool.arun({"file_path": file_path})
                # return extracted_text
                
                # Mock extraction for now
                return f"Sample extracted text from {file_path}..."
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting text from file: {str(e)}")
            return None
    
    async def _chunk_document(self, text: str) -> List[Dict[str, Any]]:
        """Chunk document into smaller pieces for embedding"""
        # Simple chunking strategy (in production, use more sophisticated methods)
        chunk_size = 1000
        overlap = 200
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk_text = text[start:end]
            
            chunks.append({
                "text": chunk_text,
                "start_index": start,
                "end_index": end,
                "chunk_id": len(chunks)
            })
            
            start = end - overlap
        
        return chunks
    
    async def _store_document_embeddings(self, chunks: List[Dict[str, Any]], source: str, context: Dict[str, Any]) -> str:
        """Generate embeddings and store in vector database using existing DocQA system"""
        try:
            # Use existing DocQA integration service
            from app.services.docqa_integration_service import DocQAIntegrationService

            docqa_service = DocQAIntegrationService()
            franchisor_id = context.get("franchisor_id")

            if franchisor_id and source:
                # Process document through existing DocQA system
                result = await docqa_service.process_franchisor_brochure_async(
                    franchisor_id=franchisor_id,
                    brochure_url=source
                )

                if result and result.success:
                    logger.info(f"Successfully processed document through DocQA: {result.chunks_created} chunks created")
                    return str(result.document_id) if hasattr(result, 'document_id') else str(uuid.uuid4())
                else:
                    logger.error(f"DocQA processing failed: {result.error_message if result else 'Unknown error'}")

            # Fallback: generate a document ID for tracking
            import uuid
            document_id = str(uuid.uuid4())
            logger.info(f"Generated fallback document ID: {document_id}")
            return document_id

        except Exception as e:
            logger.error(f"Error storing document embeddings: {str(e)}")
            # Don't raise exception to avoid breaking the flow
            import uuid
            return str(uuid.uuid4())
    
    async def _generate_document_summary(self, text: str) -> str:
        """Generate a summary of the document"""
        # Truncate text if too long
        max_length = 4000
        if len(text) > max_length:
            text = text[:max_length] + "..."
        
        summary_prompt = f"""
        Provide a concise summary of this document content:
        
        {text}
        
        Focus on:
        - Main topics covered
        - Key information or data points
        - Type of document (brochure, manual, etc.)
        - Target audience
        
        Keep the summary to 2-3 sentences.
        """
        
        response = await self.llm.ainvoke(summary_prompt)
        return response.content.strip()
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported document formats"""
        return [
            "PDF (.pdf)"
        ]
    
    def get_processing_capabilities(self) -> List[str]:
        """Get list of processing capabilities"""
        return [
            "PDF text extraction",
            "Document chunking and segmentation",
            "Embedding generation for search",
            "Content summarization",
            "Metadata extraction",
            "Integration with existing DocQA system"
        ]
