"""
Meeting Booking Agent
Handles meeting scheduling and calendar management with Zoho Meetings integration
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog
from langchain_core.messages import AIMessage

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry

logger = structlog.get_logger()


class MeetingBookingAgent(BaseAgent):
    """
    Agent responsible for booking meetings and managing calendar
    integrations with Zoho Meetings
    """
    
    def _initialize_tools(self):
        """Initialize meeting booking tools"""
        tool_names = ["book_meeting", "check_availability", "cancel_meeting", "update_lead_status"]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process meeting booking requests"""
        try:
            user_input = state.get("user_input", "")
            lead_id = state.get("lead_id")
            context = state.get("context", {})
            
            # Determine the booking intent
            booking_intent = await self._determine_booking_intent(user_input)
            
            if booking_intent == "book_meeting":
                result = await self._handle_meeting_booking(user_input, lead_id, context)
            elif booking_intent == "check_availability":
                result = await self._handle_availability_check(user_input, context)
            elif booking_intent == "cancel_meeting":
                result = await self._handle_meeting_cancellation(user_input, context)
            else:
                result = await self._handle_general_meeting_inquiry(user_input)
            
            # Update state
            state["response"] = result["message"]
            state["meeting_data"] = result.get("meeting_data")
            state["availability"] = result.get("availability")
            state["next_action"] = result.get("next_action", "end")
            state["messages"] = state.get("messages", []) + [AIMessage(content=result["message"])]
            
            # Update lead status if meeting was booked
            if result.get("meeting_booked") and lead_id:
                await self._update_lead_after_booking(lead_id)
            
            logger.info("Meeting booking processed successfully")
            return state
            
        except Exception as e:
            logger.error(f"Error in meeting booking agent: {str(e)}")
            state["error"] = str(e)
            return state
    
    async def _determine_booking_intent(self, user_input: str) -> str:
        """Determine the user's meeting booking intent"""
        intent_prompt = f"""
        Determine the user's intent from this message: "{user_input}"
        
        Possible intents:
        - book_meeting: User wants to schedule a new meeting
        - check_availability: User wants to see available times
        - cancel_meeting: User wants to cancel an existing meeting
        - general_inquiry: General questions about meetings
        
        Return only the intent name.
        """
        
        response = await self.llm.ainvoke(intent_prompt)
        return response.content.strip().lower()
    
    async def _handle_meeting_booking(self, user_input: str, lead_id: Optional[str], context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle meeting booking requests"""
        try:
            # Extract meeting preferences
            preferences = await self._extract_meeting_preferences(user_input)
            
            # Check availability
            availability = await self._check_availability(preferences)
            
            if not availability:
                return {
                    "message": "I don't see any available slots for your preferred times. Let me suggest some alternative times that work well for consultations.",
                    "availability": await self._get_suggested_times(),
                    "next_action": "continue"
                }
            
            # If specific time is requested and available, book it
            if preferences.get("specific_time") and availability:
                meeting_data = await self._book_meeting(preferences, lead_id)
                
                if meeting_data:
                    return {
                        "message": f"Perfect! I've scheduled your consultation for {meeting_data['datetime']}. You'll receive a confirmation email with the meeting details and Zoho Meeting link.\n\nOur consultant will discuss franchise opportunities that match your interests and budget. Is there anything specific you'd like to focus on during the call?",
                        "meeting_data": meeting_data,
                        "meeting_booked": True,
                        "next_action": "end"
                    }
            
            # Otherwise, show availability options
            return {
                "message": "I'd be happy to schedule a consultation for you! Here are some available times. Which works best for you?",
                "availability": availability,
                "next_action": "continue"
            }
            
        except Exception as e:
            logger.error(f"Error handling meeting booking: {str(e)}")
            return {
                "message": "I encountered an issue while trying to schedule your meeting. Let me connect you with our team to book this manually."
            }
    
    async def _extract_meeting_preferences(self, user_input: str) -> Dict[str, Any]:
        """Extract meeting preferences from user input"""
        extraction_prompt = f"""
        Extract meeting preferences from: "{user_input}"
        
        Look for:
        - preferred_date: Specific date mentioned
        - preferred_time: Specific time mentioned
        - time_range: Time range or period (morning, afternoon, evening)
        - duration: How long they want the meeting
        - meeting_type: Phone, video, in-person
        - urgency: How soon they need it
        
        Return as JSON with only the fields that have values.
        """
        
        response = await self.llm.ainvoke(extraction_prompt)
        
        try:
            import json
            preferences = json.loads(response.content.strip())
            return preferences
        except:
            return {}
    
    async def _check_availability(self, preferences: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check calendar availability"""
        try:
            availability_tool = tool_registry.get_tool("check_availability")
            if availability_tool:
                # Check availability (implementation depends on tool)
                # availability = await availability_tool.arun(preferences)
                # return availability
                
                # Mock availability for now
                return await self._get_mock_availability()
            
            return []
            
        except Exception as e:
            logger.error(f"Error checking availability: {str(e)}")
            return []
    
    async def _get_mock_availability(self) -> List[Dict[str, Any]]:
        """Get mock availability data"""
        now = datetime.now()
        availability = []
        
        # Generate next 5 business days with time slots
        for i in range(1, 6):
            date = now + timedelta(days=i)
            if date.weekday() < 5:  # Monday to Friday
                for hour in [9, 11, 14, 16]:  # 9 AM, 11 AM, 2 PM, 4 PM
                    slot_time = date.replace(hour=hour, minute=0, second=0, microsecond=0)
                    availability.append({
                        "datetime": slot_time.isoformat(),
                        "display_time": slot_time.strftime("%A, %B %d at %I:%M %p"),
                        "duration": 30,
                        "available": True
                    })
        
        return availability[:10]  # Return first 10 slots
    
    async def _get_suggested_times(self) -> List[Dict[str, Any]]:
        """Get suggested meeting times"""
        return await self._get_mock_availability()
    
    async def _book_meeting(self, preferences: Dict[str, Any], lead_id: Optional[str]) -> Optional[Dict[str, Any]]:
        """Book the meeting"""
        try:
            booking_tool = tool_registry.get_tool("book_meeting")
            if booking_tool:
                meeting_data = {
                    "lead_id": lead_id,
                    "preferences": preferences,
                    "meeting_type": "consultation",
                    "duration": 30
                }
                
                # Book meeting (implementation depends on tool)
                # result = await booking_tool.arun(meeting_data)
                # return result
                
                # Mock booking for now
                return {
                    "meeting_id": "mock_meeting_123",
                    "datetime": "2024-01-15T14:00:00",
                    "duration": 30,
                    "meeting_link": "https://meeting.zoho.com/meeting/mock_meeting_123",
                    "status": "confirmed"
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error booking meeting: {str(e)}")
            return None
    
    async def _handle_availability_check(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle availability check requests"""
        availability = await self._get_mock_availability()
        
        return {
            "message": "Here are the available consultation times. Which one works best for you?",
            "availability": availability,
            "next_action": "continue"
        }
    
    async def _handle_meeting_cancellation(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle meeting cancellation requests"""
        return {
            "message": "I can help you cancel your meeting. Could you please provide your meeting confirmation number or the date/time of your scheduled consultation?",
            "next_action": "continue"
        }
    
    async def _handle_general_meeting_inquiry(self, user_input: str) -> Dict[str, Any]:
        """Handle general meeting inquiries"""
        inquiry_response = """I'd be happy to help you schedule a consultation with one of our franchise experts!

Our consultations typically include:
- Discussion of your franchise interests and goals
- Review of available opportunities in your area
- Investment requirements and financing options
- Next steps in the franchise process

Consultations are usually 30 minutes and can be conducted via phone or video call.

Would you like to schedule a consultation? I can show you available times."""
        
        return {
            "message": inquiry_response,
            "next_action": "continue"
        }
    
    async def _update_lead_after_booking(self, lead_id: str):
        """Update lead status after successful meeting booking"""
        try:
            update_tool = tool_registry.get_tool("update_lead_status")
            if update_tool:
                # Update lead status to indicate meeting scheduled
                # await update_tool.arun({
                #     "lead_id": lead_id,
                #     "status": "meeting_scheduled"
                # })
                pass
                
        except Exception as e:
            logger.error(f"Error updating lead after booking: {str(e)}")
    
    def get_booking_capabilities(self) -> List[str]:
        """Get list of booking capabilities"""
        return [
            "Schedule franchise consultations",
            "Check calendar availability",
            "Book phone and video meetings",
            "Send meeting confirmations",
            "Integrate with Zoho Meetings",
            "Cancel and reschedule meetings",
            "Update lead status after booking"
        ]
