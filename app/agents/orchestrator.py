"""
LangGraph-based Agent Orchestrator
Manages the multi-agent workflow using LangGraph StateGraph
"""

from typing import Dict, Any, Optional
import structlog

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI

from .base import AgentState, AgentRole, AgentConfig, BaseAgent, AgentFactory

logger = structlog.get_logger()


class AgentOrchestrator:
    """
    LangGraph-based orchestrator for the multi-agent system
    Manages workflow, routing, and state management
    """
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.graph: Optional[StateGraph] = None
        self.checkpointer = MemorySaver()
        self.llm = ChatOpenAI(model="gpt-4-turbo", temperature=0.1)
        self._initialize_agents()
        self._build_workflow()
        
        logger.info("Agent orchestrator initialized")
    
    def _initialize_agents(self):
        """Initialize all agents in the system"""
        agent_configs = [
            AgentConfig(
                role=AgentRole.CONVERSATION,
                name="Conversation Agent",
                description="Handle user greetings and general conversation",
                tools=["store_memory", "retrieve_memory", "validate_email", "validate_phone"]
            ),
            AgentConfig(
                role=AgentRole.DOCUMENT_INGESTION,
                name="Document Ingestion Agent", 
                description="Process and ingest documents for knowledge base",
                tools=["ingest_document", "extract_text", "store_memory"]
            ),
            AgentConfig(
                role=AgentRole.QUESTION_ANSWERING,
                name="Question Answering Agent",
                description="Answer questions based on ingested documents",
                tools=["search_documents", "retrieve_memory", "create_communication"]
            ),
            AgentConfig(
                role=AgentRole.LEAD_QUALIFICATION,
                name="Lead Qualification Agent",
                description="Qualify leads through structured questioning",
                tools=["create_lead", "update_lead_status", "get_lead", "create_communication", "validate_lead_data"]
            ),
            AgentConfig(
                role=AgentRole.MEETING_BOOKING,
                name="Meeting Booking Agent",
                description="Book meetings and manage calendar",
                tools=["book_meeting", "check_availability", "cancel_meeting", "update_lead_status"]
            )
        ]
        
        for config in agent_configs:
            agent = AgentFactory.create_agent(config)
            self.agents[config.role] = agent
    
    def _build_workflow(self):
        """Build the LangGraph workflow"""
        # Create the state graph
        workflow = StateGraph(AgentState)
        
        # Add nodes for each agent
        workflow.add_node("router", self._route_request)
        workflow.add_node("conversation", self._conversation_node)
        workflow.add_node("document_ingestion", self._document_ingestion_node)
        workflow.add_node("question_answering", self._question_answering_node)
        workflow.add_node("lead_qualification", self._lead_qualification_node)
        workflow.add_node("meeting_booking", self._meeting_booking_node)
        workflow.add_node("error_handler", self._error_handler_node)
        
        # Set entry point
        workflow.set_entry_point("router")
        
        # Add conditional edges from router
        workflow.add_conditional_edges(
            "router",
            self._determine_next_agent,
            {
                "conversation": "conversation",
                "document_ingestion": "document_ingestion", 
                "question_answering": "question_answering",
                "lead_qualification": "lead_qualification",
                "meeting_booking": "meeting_booking",
                "error": "error_handler",
                "end": END
            }
        )
        
        # Add edges from agents back to router or end
        for agent_name in ["conversation", "document_ingestion", "question_answering", 
                          "lead_qualification", "meeting_booking"]:
            workflow.add_conditional_edges(
                agent_name,
                self._determine_continuation,
                {
                    "continue": "router",
                    "end": END,
                    "error": "error_handler"
                }
            )
        
        # Error handler always goes to end
        workflow.add_edge("error_handler", END)
        
        # Compile the graph
        self.graph = workflow.compile(checkpointer=self.checkpointer)
        
        logger.info("Workflow graph compiled successfully")
    
    async def _route_request(self, state: AgentState) -> AgentState:
        """Route incoming requests to appropriate agents"""
        try:
            user_input = state.get("user_input", "")
            
            # Use LLM to classify intent
            intent_prompt = f"""
            Classify the user's intent from the following message: "{user_input}"
            
            Available intents:
            - greeting: General greetings, small talk
            - document_question: Questions about documents, brochures, franchise information
            - lead_qualification: Providing personal information, expressing interest in franchise
            - meeting_booking: Requesting meetings, scheduling appointments
            - document_upload: Uploading or processing documents
            - out_of_scope: Questions unrelated to franchising
            
            Respond with only the intent name.
            """
            
            response = await self.llm.ainvoke(intent_prompt)
            intent = response.content.strip().lower()
            
            # Update state
            state["intent"] = intent
            state["current_agent"] = "router"
            state["execution_path"] = state.get("execution_path", []) + ["router"]
            
            logger.info(f"Routed request with intent: {intent}")
            return state
            
        except Exception as e:
            logger.error(f"Error in router: {str(e)}")
            state["error"] = str(e)
            return state
    
    def _determine_next_agent(self, state: AgentState) -> str:
        """Determine which agent should handle the request"""
        intent = state.get("intent", "")
        error = state.get("error")
        
        if error:
            return "error"
        
        intent_mapping = {
            "greeting": "conversation",
            "document_question": "question_answering",
            "lead_qualification": "lead_qualification", 
            "meeting_booking": "meeting_booking",
            "document_upload": "document_ingestion",
            "out_of_scope": "conversation"
        }
        
        return intent_mapping.get(intent, "conversation")
    
    def _determine_continuation(self, state: AgentState) -> str:
        """Determine if workflow should continue or end"""
        error = state.get("error")
        next_action = state.get("next_action")
        
        if error:
            return "error"
        elif next_action == "continue":
            return "continue"
        else:
            return "end"
    
    async def _conversation_node(self, state: AgentState) -> AgentState:
        """Handle conversation agent processing"""
        agent = self.agents.get("conversation")
        if agent:
            state["current_agent"] = "conversation"
            return await agent.process_state(state)
        return state
    
    async def _document_ingestion_node(self, state: AgentState) -> AgentState:
        """Handle document ingestion agent processing"""
        agent = self.agents.get("document_ingestion")
        if agent:
            state["current_agent"] = "document_ingestion"
            return await agent.process_state(state)
        return state
    
    async def _question_answering_node(self, state: AgentState) -> AgentState:
        """Handle question answering agent processing"""
        agent = self.agents.get("question_answering")
        if agent:
            state["current_agent"] = "question_answering"
            return await agent.process_state(state)
        return state
    
    async def _lead_qualification_node(self, state: AgentState) -> AgentState:
        """Handle lead qualification agent processing"""
        agent = self.agents.get("lead_qualification")
        if agent:
            state["current_agent"] = "lead_qualification"
            return await agent.process_state(state)
        return state
    
    async def _meeting_booking_node(self, state: AgentState) -> AgentState:
        """Handle meeting booking agent processing"""
        agent = self.agents.get("meeting_booking")
        if agent:
            state["current_agent"] = "meeting_booking"
            return await agent.process_state(state)
        return state
    
    async def _error_handler_node(self, state: AgentState) -> AgentState:
        """Handle errors in the workflow"""
        error = state.get("error", "Unknown error occurred")
        logger.error(f"Workflow error: {error}")
        
        state["response"] = "I apologize, but I encountered an error processing your request. Please try again or contact support."
        state["next_action"] = "end"
        
        return state
    
    async def process_message(self, message: str, session_id: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message through the agent workflow
        """
        try:
            # Initialize state
            initial_state: AgentState = {
                "user_input": message,
                "messages": [HumanMessage(content=message)],
                "session_id": session_id,
                "intent": None,
                "next_action": None,
                "lead_id": None,
                "lead_data": None,
                "lead_status": None,
                "document_id": None,
                "document_content": None,
                "search_results": None,
                "meeting_data": None,
                "availability": None,
                "context": context or {},
                "conversation_history": [],
                "response": None,
                "error": None,
                "metadata": {},
                "current_agent": None,
                "execution_path": [],
                "retry_count": 0
            }
            
            # Execute the workflow
            config = {"configurable": {"thread_id": session_id}}
            result = await self.graph.ainvoke(initial_state, config)
            
            # Return response
            return {
                "success": True,
                "response": result.get("response", "I'm here to help with your franchise questions."),
                "intent": result.get("intent"),
                "lead_id": result.get("lead_id"),
                "next_action": result.get("next_action"),
                "execution_path": result.get("execution_path", []),
                "metadata": result.get("metadata", {})
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "response": "I apologize, but I encountered an error. Please try again."
            }
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current status of the workflow and agents"""
        return {
            "agents": {name: agent.get_status() for name, agent in self.agents.items()},
            "workflow_compiled": self.graph is not None,
            "checkpointer_enabled": self.checkpointer is not None
        }
