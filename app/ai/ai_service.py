"""AI and RAG service for intelligent responses"""
from typing import List, Dict, Any, Optional
import openai
from app.core.config.settings import settings
from app.core.logging import logger
from app.integrations.vector_db import VectorDBService

class AIService:
    """Service for AI-powered responses and document processing"""
    
    def __init__(self) -> None:
        """Initialize AI service with OpenAI and vector database"""
        openai.api_key = settings.openai_api_key
        self.vector_db = VectorDBService()

    async def generate_response(
        self, 
        message: str, 
        lead_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate AI response based on message and context
        
        Args:
            message (str): User message to respond to
            lead_context (Optional[Dict[str, Any]]): Additional context about the lead
            
        Returns:
            str: Generated response
        """
        try:
            # Get relevant context from vector database
            context = await self.vector_db.search_similar_documents(message, top_k=3)

            # Build prompt with context
            system_prompt = self._build_system_prompt(context, lead_context)

            # Generate response using OpenAI
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                max_tokens=500,
                temperature=0.7
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Failed to generate AI response: {str(e)}")
            return "Thank you for your message. A representative will get back to you shortly."

    def _build_system_prompt(
        self, 
        context: List[str], 
        lead_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build system prompt with context
        
        Args:
            context (List[str]): Relevant context from vector database
            lead_context (Optional[Dict[str, Any]]): Additional context about the lead
            
        Returns:
            str: Complete system prompt
        """
        base_prompt = """You are an AI assistant for GrowthHive, helping with franchise prospect outreach in Australia.
        You should be helpful, professional, and focused on qualifying leads for franchise opportunities.

        Key guidelines:
        - Keep responses concise and SMS-friendly
        - Ask qualifying questions about budget, location, experience
        - Provide relevant franchise information
        - Schedule meetings when appropriate
        - Use Australian English and AUD currency
        """

        if context:
            base_prompt += f"\n\nRelevant context:\n{chr(10).join(context)}"

        if lead_context:
            base_prompt += f"\n\nLead information: {lead_context}"

        return base_prompt

    async def process_document(self, file_path: str, franchisor_id: str) -> bool:
        """
        Process a document for the AI system
        
        Args:
            file_path (str): Path to the document file
            franchisor_id (str): ID of associated franchisor
            
        Returns:
            bool: True if processing was successful
        """
        try:
            # Extract text from document
            text_content = await self._extract_text(file_path)
            
            # Store in vector database
            await self.vector_db.store_document(text_content, franchisor_id, file_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}")
            return False

    async def _extract_text_from_file(self, file_path: str) -> str:
        """Extract text from PDF or other document formats
        
        Args:
            file_path (str): Path to document file
            
        Returns:
            str: Extracted text content
        """
        # Implementation for text extraction
        # This would use libraries like PyPDF2, pdfplumber, etc.
        return "Document text content would be extracted here"
