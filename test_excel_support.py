#!/usr/bin/env python3
"""
Test Excel file support in the enhanced ingestion system.
"""

import os
import sys
from pathlib import Path

# Add the ingest package to the path
sys.path.insert(0, str(Path(__file__).parent))

from ingest.file_handlers import ExcelHandler


def test_excel_support():
    """Test Excel file support."""
    print("🧪 Testing Excel File Support")
    print("=" * 40)
    
    # Check if Excel libraries are available
    try:
        import pandas as pd
        import openpyxl
        print("✅ Excel libraries available:")
        print(f"   📊 pandas: {pd.__version__}")
        print(f"   📋 openpyxl: {openpyxl.__version__}")
    except ImportError as e:
        print(f"❌ Excel libraries missing: {e}")
        print("Install with: pip install pandas openpyxl xlrd")
        return False
    
    # Test handler
    handler = ExcelHandler()
    
    print("\n📄 Excel Handler Information:")
    print(f"   Supported types: {[ft.value for ft in handler.supported_types]}")
    
    # Test file type detection
    test_files = [
        "test.xlsx",
        "test.xls", 
        "data.xlsx",
        "report.xls"
    ]
    
    print("\n🔍 File Type Detection Test:")
    for filename in test_files:
        file_path = Path(filename)
        can_handle = handler.can_handle(file_path)
        print(f"   {filename}: {'✅ Supported' if can_handle else '❌ Not supported'}")
    
    # Test MIME type detection
    mime_types = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # .xlsx
        "application/vnd.ms-excel",  # .xls
        "application/pdf",  # Should not be supported
    ]
    
    print("\n🏷️  MIME Type Detection Test:")
    for mime_type in mime_types:
        can_handle = handler.can_handle(Path("test.file"), mime_type)
        print(f"   {mime_type}: {'✅ Supported' if can_handle else '❌ Not supported'}")
    
    return True


def create_sample_excel():
    """Create a sample Excel file for testing."""
    try:
        import pandas as pd
        
        print("\n📝 Creating Sample Excel File...")
        
        # Create sample data
        data = {
            'Product': ['Widget A', 'Widget B', 'Widget C', 'Widget D'],
            'Price': [10.99, 15.50, 8.75, 22.00],
            'Quantity': [100, 75, 200, 50],
            'Revenue': [1099, 1162.50, 1750, 1100],
            'Category': ['Electronics', 'Electronics', 'Home', 'Electronics']
        }
        
        df = pd.DataFrame(data)
        
        # Save to Excel
        filename = "sample_data.xlsx"
        df.to_excel(filename, index=False, sheet_name="Sales Data")
        
        print(f"✅ Sample Excel file created: {filename}")
        print(f"   📊 Data: {len(df)} rows × {len(df.columns)} columns")
        
        return filename
        
    except Exception as e:
        print(f"❌ Failed to create sample Excel: {e}")
        return None


def main():
    """Main test function."""
    print("🔬 Excel Support Testing")
    print("=" * 30)
    
    # Test basic support
    if not test_excel_support():
        return
    
    # Create and test sample file
    sample_file = create_sample_excel()
    if sample_file:
        print("\n🎯 Excel support is ready!")
        print("📄 Supported file types: .xlsx, .xls")
        print("🚀 The system can now process Excel files with:")
        print("   📊 Multi-sheet support")
        print("   📈 Data analysis and summaries")
        print("   🔍 Intelligent content extraction")
        print("   🤖 GPT-powered analysis")
        
        # Clean up
        try:
            os.remove(sample_file)
            print("\n🗑️  Cleaned up sample file")
        except:
            pass
    
    print("\n✅ Excel support test completed!")


if __name__ == "__main__":
    main()
