#!/usr/bin/env python3
"""
<PERSON>ript to add missing _run methods to all tools
"""

import os
import re

def fix_tool_file(file_path):
    """Fix a single tool file by adding _run methods where missing"""
    print(f"Processing {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find all tool classes and their _arun methods
    tool_pattern = r'class (\w+Tool)\(BaseTool\):(.*?)(?=class|\Z)'
    arun_pattern = r'async def _arun\(self, \*\*kwargs\) -> str:'
    run_pattern = r'def _run\(self, \*\*kwargs\) -> str:'
    
    modified = False
    new_content = content
    
    for match in re.finditer(tool_pattern, content, re.DOTALL):
        tool_name = match.group(1)
        tool_body = match.group(2)
        
        # Check if this tool has _arun but not _run
        has_arun = re.search(arun_pattern, tool_body)
        has_run = re.search(run_pattern, tool_body)
        
        if has_arun and not has_run:
            print(f"  Adding _run method to {tool_name}")
            
            # Find the position to insert _run method (before _arun)
            arun_match = re.search(arun_pattern, tool_body)
            if arun_match:
                # Insert _run method before _arun
                run_method = '''    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    '''
                
                # Find the full match position in the original content
                full_match_start = match.start()
                arun_pos_in_match = arun_match.start()
                insert_pos = full_match_start + arun_pos_in_match
                
                new_content = new_content[:insert_pos] + run_method + new_content[insert_pos:]
                modified = True
    
    if modified:
        with open(file_path, 'w') as f:
            f.write(new_content)
        print(f"  ✅ Fixed {file_path}")
    else:
        print(f"  ⏭️ No changes needed for {file_path}")

def main():
    """Main function"""
    print("🔧 Fixing tool methods...")
    
    # Tool files to process
    tool_files = [
        "app/agents/tools/memory_tools.py",
        "app/agents/tools/database_tools.py", 
        "app/agents/tools/document_tools.py",
        "app/agents/tools/meeting_tools.py",
        "app/agents/tools/validation_tools.py"
    ]
    
    for file_path in tool_files:
        if os.path.exists(file_path):
            fix_tool_file(file_path)
        else:
            print(f"❌ File not found: {file_path}")
    
    print("✅ Tool method fixing complete!")

if __name__ == "__main__":
    main()
