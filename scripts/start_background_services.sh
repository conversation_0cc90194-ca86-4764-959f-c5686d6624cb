#!/bin/bash
"""
Start Background Services Script
Starts RabbitMQ, Redis, and Celery workers for GrowthHive
"""

set -e

echo "🚀 Starting GrowthHive Background Services..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start RabbitMQ and Redis services
echo "📦 Starting RabbitMQ and Redis..."
docker-compose -f docker-compose.rabbitmq.yml up -d rabbitmq redis

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check RabbitMQ health
echo "🔍 Checking RabbitMQ health..."
until docker exec growthhive-rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; do
    echo "Waiting for RabbitMQ..."
    sleep 5
done
echo "✅ RabbitMQ is ready"

# Check Redis health
echo "🔍 Checking Redis health..."
until docker exec growthhive-redis redis-cli ping > /dev/null 2>&1; do
    echo "Waiting for Redis..."
    sleep 5
done
echo "✅ Redis is ready"

# Start Celery worker
echo "👷 Starting Celery worker..."
docker-compose -f docker-compose.rabbitmq.yml up -d celery-worker

# Start Celery Flower (monitoring)
echo "🌸 Starting Celery Flower monitoring..."
docker-compose -f docker-compose.rabbitmq.yml up -d celery-flower

echo ""
echo "🎉 All background services are running!"
echo ""
echo "📊 Service URLs:"
echo "   RabbitMQ Management: http://localhost:15672 (growthhive/growthhive123)"
echo "   Celery Flower:       http://localhost:5555"
echo "   Redis:               localhost:6379"
echo ""
echo "📝 To view logs:"
echo "   docker-compose -f docker-compose.rabbitmq.yml logs -f celery-worker"
echo ""
echo "🛑 To stop services:"
echo "   docker-compose -f docker-compose.rabbitmq.yml down"
