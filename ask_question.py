#!/usr/bin/env python3
"""
Simple Q&A interface - no blinking, clean output.
Ask questions about the franchise document.
"""

import os
import subprocess


def ask_question(question):
    """Ask a question and get a clean answer."""
    try:
        # Run the docqa command
        result = subprocess.run([
            "python", "docqa.py", "ask", question, "--no-stream"
        ], capture_output=True, text=True, env={**os.environ})
        
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            return f"Error: {result.stderr.strip()}"
            
    except Exception as e:
        return f"Error: {str(e)}"


def main():
    """Simple Q&A interface."""
    # Check if document is already ingested
    if not os.path.exists("data/faiss_index"):
        print("📄 Document not found. Ingesting first...")
        
        # Ingest the document
        result = subprocess.run([
            "python", "docqa.py", "ingest", 
            "s3://openxcell-development-public/growthhive/brochure/20250701_113703_07ce376fa750.pdf",
            "--force"
        ], capture_output=True, text=True, env={**os.environ})
        
        if result.returncode != 0:
            print("❌ Failed to ingest document")
            return
        
        print("✅ Document ingested successfully")
    
    print("\n🤖 Franchise Document Q&A System")
    print("Ask questions about the Coochie HydroGreen franchise document.")
    print("Type 'quit' to exit.\n")
    
    while True:
        try:
            # Get question from user
            question = input("❓ Your question: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not question:
                continue
            
            # Get answer
            print("\n💭 Answer:")
            answer = ask_question(question)
            print(answer)
            print("\n" + "-"*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
