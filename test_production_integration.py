#!/usr/bin/env python3
"""
Test Production-Grade RAG Integration
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the project root to the path
sys.path.append('.')

async def test_production_ask_question():
    """Test the production ask_question function"""
    print("🚀 Testing Production-Grade ask_question Integration")
    print("=" * 60)
    
    try:
        # Import the production ask function
        from docqa.serve import ask_question_production
        
        # Test questions
        test_questions = [
            "What is Coochie Hydrogreen?",
            "Where is Coochie Hydrogreen located?",
            "What services does Coochie Hydrogreen provide?",
            "What are the franchise fees?",
            "What training does the business provide?"
        ]
        
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}. ❓ Question: {question}")
            
            # Test with production-grade parameters
            result = await ask_question_production(
                question=question,
                franchisor_id=franchisor_id,
                top_k=5,
                similarity_threshold=0.5,
                temperature=0.1,
                include_metadata=True,
                format='json'
            )
            
            # Parse and display result
            try:
                parsed_result = json.loads(result)
                answer = parsed_result.get('answer', 'No answer')
                sources_count = len(parsed_result.get('sources', []))
                processing_time = parsed_result.get('metadata', {}).get('processing_time', 0)
                
                print(f"   ✅ Answer ({processing_time:.2f}s, {sources_count} sources):")
                print(f"   {answer}")
                
                # Show top source if available
                sources = parsed_result.get('sources', [])
                if sources:
                    top_source = sources[0]
                    score = top_source.get('similarity_score', 0)
                    text_preview = top_source.get('text', '')[:100] + "..."
                    print(f"   📚 Top Source (Score: {score:.3f}): {text_preview}")
                
            except json.JSONDecodeError:
                print(f"   ✅ Answer: {result}")
        
        print(f"\n🎉 Production integration test completed!")
        
    except Exception as e:
        print(f"❌ Error in production integration test: {e}")
        import traceback
        traceback.print_exc()

async def test_health_check():
    """Test the health check function"""
    print("\n🔍 Testing Health Check")
    print("=" * 30)
    
    try:
        from docqa.serve import health_check
        
        health_status = health_check()
        
        print("Health Check Results:")
        print(json.dumps(health_status, indent=2))
        
        # Check if production components are healthy
        production_status = health_status.get('production_embedding_service', 'unknown')
        if production_status == 'ok':
            print("✅ Production embedding service is healthy")
        else:
            print("❌ Production embedding service has issues")
        
    except Exception as e:
        print(f"❌ Error in health check: {e}")
        import traceback
        traceback.print_exc()

async def test_direct_production_rag():
    """Test direct production RAG usage"""
    print("\n🔧 Testing Direct Production RAG Usage")
    print("=" * 40)
    
    try:
        from docqa.production_integration import production_rag
        
        # Test question
        question = "What is Coochie Hydrogreen?"
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        print(f"Question: {question}")
        
        # Answer using production RAG
        result = await production_rag.answer_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=0.5,
            top_k=5,
            temperature=0.1
        )
        
        if result['success']:
            print(f"✅ Answer: {result['answer']}")
            print(f"📊 Processing Time: {result['metadata']['processing_time']:.2f}s")
            print(f"📊 Chunks Found: {result['metadata']['chunks_found']}")
            print(f"📊 Model Used: {result['metadata']['model_used']}")
            
            if result['sources']:
                top_source = result['sources'][0]
                print(f"📚 Top Source Score: {top_source['similarity_score']:.3f}")
        else:
            print(f"❌ Error: {result['error']}")
        
    except Exception as e:
        print(f"❌ Error in direct production RAG test: {e}")
        import traceback
        traceback.print_exc()

async def test_embedding_consistency():
    """Test embedding consistency between components"""
    print("\n🔬 Testing Embedding Consistency")
    print("=" * 35)
    
    try:
        from docqa.production_integration import production_rag
        
        # Test text
        test_text = "What is Coochie Hydrogreen?"
        
        # Generate embedding with production service
        embedding1 = production_rag.embedding_service.generate_embedding(test_text)
        
        # Generate another embedding with the same text
        embedding2 = production_rag.embedding_service.generate_embedding(test_text)
        
        # Check consistency
        if len(embedding1) == len(embedding2) == 1536:
            print("✅ Embedding dimensions are consistent (1536)")
        else:
            print(f"❌ Embedding dimension mismatch: {len(embedding1)} vs {len(embedding2)}")
        
        # Check if embeddings are identical (they should be for the same text)
        import numpy as np
        similarity = np.dot(embedding1, embedding2) / (np.linalg.norm(embedding1) * np.linalg.norm(embedding2))
        
        if similarity > 0.99:
            print(f"✅ Embeddings are consistent (similarity: {similarity:.6f})")
        else:
            print(f"⚠️ Embeddings may have variance (similarity: {similarity:.6f})")
        
    except Exception as e:
        print(f"❌ Error in embedding consistency test: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all integration tests"""
    print("🚀 Production-Grade RAG Integration Tests")
    print("=" * 60)
    
    # Run tests
    await test_production_ask_question()
    await test_health_check()
    await test_direct_production_rag()
    await test_embedding_consistency()
    
    print("\n✅ All integration tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
