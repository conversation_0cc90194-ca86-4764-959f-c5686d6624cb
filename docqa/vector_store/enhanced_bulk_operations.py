"""
Enhanced Bulk Vector Operations with Async Support and Connection Pooling
High-performance batch operations for pgvector with Redis integration
"""

import time
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog
import asyncpg
from contextlib import asynccontextmanager
import uuid
import json

from ..types import DocumentChunk
from ..redis_store import get_redis_store

logger = structlog.get_logger()


class EnhancedBulkVectorOperations:
    """Enhanced bulk operations with async support and connection pooling"""
    
    def __init__(self, max_connections: int = 10, batch_size: int = 100):
        self.max_connections = max_connections
        self.batch_size = batch_size
        self.connection_pool: Optional[asyncpg.Pool] = None
        self.redis_store = None
        
        # Performance tracking
        self.performance_stats = {
            "total_batches_processed": 0,
            "total_chunks_inserted": 0,
            "total_processing_time": 0,
            "average_batch_time": 0,
            "connection_pool_hits": 0,
            "connection_pool_misses": 0
        }
    
    async def initialize(self, database_url: str):
        """Initialize connection pool and Redis"""
        if not self.connection_pool:
            self.connection_pool = await asyncpg.create_pool(
                database_url,
                min_size=2,
                max_size=self.max_connections,
                command_timeout=60,
                server_settings={
                    'jit': 'off',  # Disable JIT for better performance with many connections
                    'shared_preload_libraries': 'pg_stat_statements'
                }
            )
            
        if not self.redis_store:
            self.redis_store = await get_redis_store()
        
        logger.info("Enhanced bulk operations initialized", 
                   max_connections=self.max_connections, 
                   batch_size=self.batch_size)
    
    async def close(self):
        """Close connection pool"""
        if self.connection_pool:
            await self.connection_pool.close()
            self.connection_pool = None
    
    @asynccontextmanager
    async def get_connection(self):
        """Get connection from pool with automatic return"""
        if not self.connection_pool:
            raise RuntimeError("Connection pool not initialized")
        
        connection = await self.connection_pool.acquire()
        self.performance_stats["connection_pool_hits"] += 1
        
        try:
            yield connection
        finally:
            await self.connection_pool.release(connection)
    
    async def batch_insert_chunks(
        self,
        chunks: List[DocumentChunk],
        table_name: str,
        document_hash: str,
        source_url: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Insert chunks in optimized batches with transaction support
        
        Args:
            chunks: List of document chunks to insert
            table_name: Target table (franchisors/documents)
            document_hash: Document hash for deduplication
            source_url: Source URL of document
            metadata: Additional metadata
            
        Returns:
            Dict with insertion results
        """
        start_time = time.time()
        total_inserted = 0
        batch_count = 0
        
        try:
            # Process chunks in batches
            for i in range(0, len(chunks), self.batch_size):
                batch = chunks[i:i + self.batch_size]
                
                batch_result = await self._insert_batch(
                    batch, table_name, document_hash, source_url, metadata
                )
                
                if batch_result["success"]:
                    total_inserted += batch_result["inserted_count"]
                    batch_count += 1
                else:
                    logger.error(f"Batch insertion failed: {batch_result['error']}")
            
            processing_time = time.time() - start_time
            
            # Update performance stats
            self.performance_stats["total_batches_processed"] += batch_count
            self.performance_stats["total_chunks_inserted"] += total_inserted
            self.performance_stats["total_processing_time"] += processing_time
            
            if batch_count > 0:
                self.performance_stats["average_batch_time"] = (
                    self.performance_stats["total_processing_time"] / 
                    self.performance_stats["total_batches_processed"]
                )
            
            logger.info("Batch insertion completed", 
                       chunks_inserted=total_inserted,
                       batches=batch_count,
                       processing_time=processing_time)
            
            return {
                "success": True,
                "chunks_stored": total_inserted,
                "batches_processed": batch_count,
                "processing_time": processing_time,
                "average_batch_time": processing_time / batch_count if batch_count > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Bulk insertion failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "chunks_stored": total_inserted,
                "processing_time": time.time() - start_time
            }
    
    async def _insert_batch(
        self,
        batch: List[DocumentChunk],
        table_name: str,
        document_hash: str,
        source_url: str,
        metadata: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Insert a single batch with transaction support"""
        try:
            async with self.get_connection() as conn:
                async with conn.transaction():
                    # Prepare batch data
                    batch_data = []
                    
                    for chunk in batch:
                        # Determine target table and prepare data
                        if table_name == "franchisors":
                            row_data = (
                                str(uuid.uuid4()),  # id
                                chunk.content,      # content
                                chunk.embedding,    # embedding
                                source_url,         # source_url
                                document_hash,      # document_hash
                                json.dumps(chunk.metadata or {}),  # metadata
                                datetime.utcnow(),  # created_at
                                datetime.utcnow(),  # updated_at
                                True,               # is_active
                                False               # is_deleted
                            )
                        else:  # documents table
                            row_data = (
                                str(uuid.uuid4()),  # id
                                chunk.content,      # content
                                chunk.embedding,    # embedding
                                source_url,         # source_url
                                document_hash,      # document_hash
                                json.dumps(chunk.metadata or {}),  # metadata
                                datetime.utcnow(),  # created_at
                                datetime.utcnow(),  # updated_at
                                True,               # is_active
                                False               # is_deleted
                            )
                        
                        batch_data.append(row_data)
                    
                    # Execute batch insert
                    if table_name == "franchisors":
                        query = """
                            INSERT INTO franchisors (
                                id, content, embedding, source_url, document_hash, 
                                metadata, created_at, updated_at, is_active, is_deleted
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                        """
                    else:
                        query = """
                            INSERT INTO documents (
                                id, content, embedding, source_url, document_hash,
                                metadata, created_at, updated_at, is_active, is_deleted
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                        """
                    
                    # Use executemany for better performance
                    await conn.executemany(query, batch_data)
                    
                    return {
                        "success": True,
                        "inserted_count": len(batch_data)
                    }
                    
        except Exception as e:
            logger.error(f"Batch insert failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "inserted_count": 0
            }
    
    async def batch_upsert_chunks(
        self,
        chunks: List[DocumentChunk],
        table_name: str,
        document_hash: str,
        source_url: str,
        conflict_resolution: str = "update"
    ) -> Dict[str, Any]:
        """
        Upsert chunks with conflict resolution
        
        Args:
            chunks: Chunks to upsert
            table_name: Target table
            document_hash: Document hash
            source_url: Source URL
            conflict_resolution: "update", "ignore", or "error"
            
        Returns:
            Upsert results
        """
        start_time = time.time()
        
        try:
            # First, check for existing chunks
            existing_chunks = await self._get_existing_chunks(
                document_hash, table_name
            )
            
            existing_hashes = {
                self._generate_chunk_hash(chunk.content) 
                for chunk in existing_chunks
            }
            
            # Separate new and existing chunks
            new_chunks = []
            update_chunks = []
            
            for chunk in chunks:
                chunk_hash = self._generate_chunk_hash(chunk.content)
                
                if chunk_hash in existing_hashes:
                    if conflict_resolution == "update":
                        update_chunks.append(chunk)
                    elif conflict_resolution == "ignore":
                        continue
                    else:  # error
                        raise ValueError(f"Chunk already exists: {chunk_hash}")
                else:
                    new_chunks.append(chunk)
            
            # Insert new chunks
            insert_result = {"success": True, "chunks_stored": 0}
            if new_chunks:
                insert_result = await self.batch_insert_chunks(
                    new_chunks, table_name, document_hash, source_url
                )
            
            # Update existing chunks
            update_result = {"success": True, "chunks_updated": 0}
            if update_chunks:
                update_result = await self._batch_update_chunks(
                    update_chunks, table_name, document_hash
                )
            
            processing_time = time.time() - start_time
            
            return {
                "success": insert_result["success"] and update_result["success"],
                "chunks_inserted": insert_result.get("chunks_stored", 0),
                "chunks_updated": update_result.get("chunks_updated", 0),
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Batch upsert failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    async def _get_existing_chunks(
        self, 
        document_hash: str, 
        table_name: str
    ) -> List[DocumentChunk]:
        """Get existing chunks for document"""
        try:
            async with self.get_connection() as conn:
                query = f"""
                    SELECT id, content, embedding, metadata
                    FROM {table_name}
                    WHERE document_hash = $1 AND is_deleted = FALSE
                """
                
                rows = await conn.fetch(query, document_hash)
                
                chunks = []
                for row in rows:
                    chunk = DocumentChunk(
                        id=row["id"],
                        content=row["content"],
                        embedding=row["embedding"],
                        metadata=json.loads(row["metadata"]) if row["metadata"] else {}
                    )
                    chunks.append(chunk)
                
                return chunks
                
        except Exception as e:
            logger.error(f"Error getting existing chunks: {e}")
            return []
    
    async def _batch_update_chunks(
        self,
        chunks: List[DocumentChunk],
        table_name: str,
        document_hash: str
    ) -> Dict[str, Any]:
        """Update existing chunks in batch"""
        try:
            updated_count = 0
            
            async with self.get_connection() as conn:
                async with conn.transaction():
                    for chunk in chunks:
                        query = f"""
                            UPDATE {table_name}
                            SET content = $1, embedding = $2, metadata = $3, updated_at = $4
                            WHERE document_hash = $5 AND content = $1
                        """
                        
                        result = await conn.execute(
                            query,
                            chunk.content,
                            chunk.embedding,
                            json.dumps(chunk.metadata or {}),
                            datetime.utcnow(),
                            document_hash
                        )
                        
                        # Extract number of updated rows from result
                        if result.startswith("UPDATE"):
                            updated_count += int(result.split()[-1])
            
            return {
                "success": True,
                "chunks_updated": updated_count
            }
            
        except Exception as e:
            logger.error(f"Batch update failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "chunks_updated": 0
            }
    
    def _generate_chunk_hash(self, content: str) -> str:
        """Generate hash for chunk content"""
        import hashlib
        return hashlib.sha256(content.encode()).hexdigest()
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            **self.performance_stats,
            "connection_pool_size": self.connection_pool.get_size() if self.connection_pool else 0,
            "connection_pool_available": (
                self.connection_pool.get_size() - self.connection_pool.get_idle_size()
                if self.connection_pool else 0
            )
        }
    
    async def optimize_database(self, table_name: str):
        """Run database optimization queries"""
        try:
            async with self.get_connection() as conn:
                # Analyze table for better query planning
                await conn.execute(f"ANALYZE {table_name}")
                
                # Vacuum to reclaim space
                await conn.execute(f"VACUUM {table_name}")
                
                logger.info(f"Database optimization completed for {table_name}")
                
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")


# Global instance
_bulk_operations: Optional[EnhancedBulkVectorOperations] = None


async def get_bulk_operations() -> EnhancedBulkVectorOperations:
    """Get or create bulk operations instance"""
    global _bulk_operations
    
    if _bulk_operations is None:
        from ..config import config
        _bulk_operations = EnhancedBulkVectorOperations()
        await _bulk_operations.initialize(config.database_url)
    
    return _bulk_operations
