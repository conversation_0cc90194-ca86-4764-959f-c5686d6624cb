"""
pgvector-based vector store for document embeddings
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
import psycopg
from psycopg.rows import dict_row
import structlog
from contextlib import contextmanager

from ..config import config
from ..types import DocumentChunk, SearchResult, VectorStoreError, TableName

logger = structlog.get_logger()


def serialize_metadata(metadata: Dict[str, Any]) -> str:
    """Serialize metadata to JSON string, handling datetime objects"""
    def json_serializer(obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

    return json.dumps(metadata, default=json_serializer)


class PgVectorStore:
    """Vector store using pgvector for similarity search"""
    
    def __init__(self):
        # Convert async URL to sync URL for psycopg
        self.database_url = self._convert_async_url_to_sync(config.database_url)
        self.top_k = config.top_k
        self.similarity_threshold = config.similarity_threshold

        # Test connection
        self._test_connection()
        logger.info("PgVectorStore initialized")

    def _convert_async_url_to_sync(self, url: str) -> str:
        """Convert async database URL to sync format for psycopg"""
        if url.startswith("postgresql+asyncpg://"):
            return url.replace("postgresql+asyncpg://", "postgresql://")
        return url
    
    def _test_connection(self):
        """Test database connection and pgvector extension"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    # Check if pgvector extension exists
                    cur.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector'")
                    if not cur.fetchone():
                        logger.warning("pgvector extension not found, attempting to create")
                        cur.execute("CREATE EXTENSION IF NOT EXISTS vector")
                        conn.commit()
                    
                    # Check if embedding columns exist
                    cur.execute("""
                        SELECT column_name FROM information_schema.columns 
                        WHERE table_name = 'documents' AND column_name = 'embedding'
                    """)
                    if not cur.fetchone():
                        logger.warning("documents.embedding column not found")
                    
                    cur.execute("""
                        SELECT column_name FROM information_schema.columns 
                        WHERE table_name = 'franchisors' AND column_name = 'embedding'
                    """)
                    if not cur.fetchone():
                        logger.warning("franchisors.embedding column not found")
                        
        except Exception as e:
            logger.error("Database connection test failed", error=str(e))
            raise VectorStoreError(f"Database connection failed: {str(e)}")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper error handling"""
        conn = None
        try:
            conn = psycopg.connect(self.database_url, row_factory=dict_row)
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error("Database connection error", error=str(e))
            raise VectorStoreError(f"Database error: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def upsert_document_embedding(
        self, 
        document_id: str, 
        chunks: List[DocumentChunk],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        Upsert document chunks with embeddings
        
        Args:
            document_id: Document ID
            chunks: List of document chunks with embeddings
            metadata: Additional metadata
            
        Returns:
            True if successful
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    # First, delete existing chunks for this document
                    cur.execute(
                        "DELETE FROM document_chunks WHERE document_id = %s",
                        (document_id,)
                    )
                    
                    # Insert new chunks
                    for chunk in chunks:
                        if not chunk.embedding:
                            logger.warning("Chunk missing embedding", chunk_id=chunk.id)
                            continue
                        
                        chunk_metadata = {**(metadata or {}), **chunk.metadata}

                        cur.execute("""
                            INSERT INTO document_chunks
                            (id, document_id, text, embedding, metadata, created_at)
                            VALUES (%s, %s, %s, %s, %s, NOW())
                            ON CONFLICT (id) DO UPDATE SET
                                text = EXCLUDED.text,
                                embedding = EXCLUDED.embedding,
                                metadata = EXCLUDED.metadata,
                                updated_at = NOW()
                        """, (
                            chunk.id,
                            document_id,
                            chunk.text,
                            chunk.embedding,
                            serialize_metadata(chunk_metadata)  # Serialize with datetime handling
                        ))
                    
                    conn.commit()
                    logger.info("Document chunks upserted", 
                               document_id=document_id,
                               chunk_count=len(chunks))
                    return True
                    
        except Exception as e:
            logger.error("Failed to upsert document embedding", 
                        document_id=document_id,
                        error=str(e))
            raise VectorStoreError(f"Upsert failed: {str(e)}")
    
    def upsert_franchisor_embedding(
        self,
        franchisor_id: str,
        embedding: List[float],
        text_content: str,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        Upsert franchisor embedding
        
        Args:
            franchisor_id: Franchisor ID
            embedding: Embedding vector
            text_content: Original text content
            metadata: Additional metadata
            
        Returns:
            True if successful
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        UPDATE franchisors 
                        SET embedding = %s, 
                            updated_at = NOW()
                        WHERE id = %s
                    """, (embedding, franchisor_id))
                    
                    if cur.rowcount == 0:
                        logger.warning("Franchisor not found for embedding update", 
                                     franchisor_id=franchisor_id)
                        return False
                    
                    conn.commit()
                    logger.info("Franchisor embedding updated", 
                               franchisor_id=franchisor_id)
                    return True
                    
        except Exception as e:
            logger.error("Failed to upsert franchisor embedding",
                        franchisor_id=franchisor_id,
                        error=str(e))
            raise VectorStoreError(f"Franchisor upsert failed: {str(e)}")

    def store_franchisor_chunks(
        self,
        franchisor_id: str,
        chunks: List[DocumentChunk],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        Store franchisor chunks with embeddings in franchisor_chunks table

        Args:
            franchisor_id: Franchisor ID
            chunks: List of document chunks with embeddings
            metadata: Additional metadata

        Returns:
            True if successful
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    # Create franchisor_chunks table if it doesn't exist
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS franchisor_chunks (
                            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                            franchisor_id UUID NOT NULL,
                            text TEXT NOT NULL,
                            embedding vector(1536),
                            chunk_index INTEGER NOT NULL,
                            token_count INTEGER NOT NULL,
                            metadata JSONB DEFAULT '{}',
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                            CONSTRAINT fk_franchisor_chunks_franchisor
                                FOREIGN KEY (franchisor_id) REFERENCES franchisors(id) ON DELETE CASCADE
                        )
                    """)

                    # Create index for vector similarity search if it doesn't exist
                    try:
                        cur.execute("""
                            CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_embedding
                            ON franchisor_chunks USING ivfflat (embedding vector_cosine_ops)
                            WITH (lists = 100)
                        """)
                    except Exception as e:
                        logger.warning(f"Could not create vector index: {e}")
                        # Continue anyway - the index is for performance, not functionality

                    # Create index for franchisor_id if it doesn't exist
                    cur.execute("""
                        CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_franchisor_id
                        ON franchisor_chunks (franchisor_id)
                    """)

                    # Delete existing chunks for this franchisor
                    cur.execute(
                        "DELETE FROM franchisor_chunks WHERE franchisor_id = %s",
                        (franchisor_id,)
                    )

                    # Insert new chunks
                    for i, chunk in enumerate(chunks):
                        if not chunk.embedding:
                            logger.warning("Chunk missing embedding", chunk_id=chunk.id)
                            continue

                        chunk_metadata = {**(metadata or {}), **chunk.metadata}

                        cur.execute("""
                            INSERT INTO franchisor_chunks
                            (franchisor_id, text, embedding, chunk_index, token_count, metadata)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """, (
                            franchisor_id,
                            chunk.text,
                            chunk.embedding,
                            i,
                            chunk.token_count,
                            serialize_metadata(chunk_metadata)
                        ))

                    conn.commit()
                    logger.info("Franchisor chunks stored",
                               franchisor_id=franchisor_id,
                               chunk_count=len(chunks))
                    return True

        except Exception as e:
            logger.error("Failed to store franchisor chunks",
                        franchisor_id=franchisor_id,
                        error=str(e))
            return False

    def search_similar(
        self,
        query_embedding: List[float],
        top_k: Optional[int] = None,
        similarity_threshold: Optional[float] = None,
        table_priority: List[TableName] = None
    ) -> List[SearchResult]:
        """
        Search for similar content with table priority
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score
            table_priority: Priority order for tables ['franchisors', 'documents']
            
        Returns:
            List of search results ordered by priority and similarity
        """
        if top_k is None:
            top_k = self.top_k
        if similarity_threshold is None:
            similarity_threshold = self.similarity_threshold
        if table_priority is None:
            table_priority = ['franchisors', 'documents']
        
        all_results = []
        
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    for table_name in table_priority:
                        if table_name == 'franchisors':
                            results = self._search_franchisors(cur, query_embedding, top_k, similarity_threshold)
                        elif table_name == 'documents':
                            results = self._search_documents(cur, query_embedding, top_k, similarity_threshold)
                        else:
                            continue
                        
                        all_results.extend(results)
                        
                        # If we have enough high-quality results from higher priority table, stop
                        high_quality_results = [r for r in results if r.similarity_score >= similarity_threshold]
                        if len(high_quality_results) >= top_k:
                            logger.info("Sufficient high-quality results found",
                                       table=table_name,
                                       count=len(high_quality_results))
                            break
            
            # Sort by similarity score and return top_k
            all_results.sort(key=lambda x: x.similarity_score, reverse=True)
            final_results = all_results[:top_k]
            
            logger.info("Vector search completed",
                       query_results=len(final_results),
                       tables_searched=table_priority)
            
            return final_results
            
        except Exception as e:
            logger.error("Vector search failed", error=str(e))
            raise VectorStoreError(f"Search failed: {str(e)}")
    
    def _search_franchisors(
        self,
        cur,
        query_embedding: List[float],
        top_k: int,
        similarity_threshold: float
    ) -> List[SearchResult]:
        """Search franchisors table and franchisor chunks"""
        results = []

        # First search franchisor chunks for detailed content
        try:
            cur.execute("""
                SELECT
                    fc.id,
                    fc.franchisor_id,
                    fc.text,
                    fc.metadata,
                    f.name,
                    f.brochure_url,
                    f.region,
                    f.industry_id,
                    1 - (fc.embedding <=> %s::vector) as similarity_score
                FROM franchisor_chunks fc
                JOIN franchisors f ON fc.franchisor_id = f.id
                WHERE fc.embedding IS NOT NULL
                    AND f.is_active = true
                    AND f.is_deleted = false
                    AND (1 - (fc.embedding <=> %s::vector)) >= %s
                ORDER BY fc.embedding <=> %s::vector
                LIMIT %s
            """, (query_embedding, query_embedding, similarity_threshold, query_embedding, top_k))

            for row in cur.fetchall():
                results.append(SearchResult(
                    chunk_id=str(row['id']),
                    text=row['text'],
                    similarity_score=row['similarity_score'],
                    metadata={
                        'type': 'franchisor_chunk',
                        'franchisor_id': str(row['franchisor_id']),
                        'name': row['name'],
                        'brochure_url': row['brochure_url'],
                        'region': row['region'],
                        'industry_id': str(row['industry_id']) if row['industry_id'] else None,
                        **row['metadata']
                    },
                    table_name='franchisors'
                ))
        except Exception as e:
            logger.warning(f"Could not search franchisor chunks: {e}")
            # Continue with main franchisor search

        # If we don't have enough results from chunks, search main franchisor table
        if len(results) < top_k:
            remaining_limit = top_k - len(results)
            cur.execute("""
                SELECT
                    id,
                    name,
                    brochure_url,
                    region,
                    industry_id,
                    1 - (embedding <=> %s::vector) as similarity_score
                FROM franchisors
                WHERE embedding IS NOT NULL
                    AND is_active = true
                    AND is_deleted = false
                    AND (1 - (embedding <=> %s::vector)) >= %s
                ORDER BY embedding <=> %s::vector
                LIMIT %s
            """, (query_embedding, query_embedding, similarity_threshold, query_embedding, remaining_limit))

            for row in cur.fetchall():
                results.append(SearchResult(
                    chunk_id=str(row['id']),
                    text=f"Franchisor: {row['name']} (Region: {row['region']})",
                    similarity_score=row['similarity_score'],
                    metadata={
                        'type': 'franchisor',
                        'franchisor_id': str(row['id']),
                        'name': row['name'],
                        'brochure_url': row['brochure_url'],
                        'region': row['region'],
                        'industry_id': str(row['industry_id']) if row['industry_id'] else None
                    },
                    table_name='franchisors'
                ))

        return results
    
    def _search_documents(
        self,
        cur,
        query_embedding: List[float],
        top_k: int,
        similarity_threshold: float
    ) -> List[SearchResult]:
        """Search documents table via document_chunks"""
        cur.execute("""
            SELECT
                dc.id,
                dc.text,
                dc.metadata,
                d.name as document_name,
                d.file_type,
                1 - (dc.embedding <=> %s::vector) as similarity_score
            FROM document_chunks dc
            JOIN documents d ON dc.document_id = d.id
            WHERE dc.embedding IS NOT NULL
                AND d.is_active = true
                AND d.is_deleted = false
                AND (1 - (dc.embedding <=> %s::vector)) >= %s
            ORDER BY dc.embedding <=> %s::vector
            LIMIT %s
        """, (query_embedding, query_embedding, similarity_threshold, query_embedding, top_k))
        
        results = []
        for row in cur.fetchall():
            results.append(SearchResult(
                chunk_id=row['id'],
                text=row['text'],
                similarity_score=row['similarity_score'],
                metadata={
                    **row['metadata'],
                    'document_name': row['document_name'],
                    'file_type': row['file_type']
                },
                table_name='documents'
            ))
        
        return results

    def search_document_specific(
        self,
        document_id: str,
        query_embedding: List[float],
        top_k: Optional[int] = None,
        similarity_threshold: Optional[float] = None
    ) -> List[SearchResult]:
        """
        Search for similar content within a specific document only

        Args:
            document_id: ID of the document to search within
            query_embedding: Query embedding vector
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score

        Returns:
            List of search results from the specific document only
        """
        if top_k is None:
            top_k = self.top_k
        if similarity_threshold is None:
            similarity_threshold = self.similarity_threshold

        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT
                            dc.id,
                            dc.text,
                            dc.metadata,
                            d.name as document_name,
                            d.file_type,
                            1 - (dc.embedding <=> %s::vector) as similarity_score
                        FROM document_chunks dc
                        JOIN documents d ON dc.document_id = d.id
                        WHERE dc.document_id = %s
                            AND dc.embedding IS NOT NULL
                            AND d.is_active = true
                            AND d.is_deleted = false
                            AND (1 - (dc.embedding <=> %s::vector)) >= %s
                        ORDER BY dc.embedding <=> %s::vector
                        LIMIT %s
                    """, (query_embedding, document_id, query_embedding, similarity_threshold, query_embedding, top_k))

                    results = []
                    for row in cur.fetchall():
                        results.append(SearchResult(
                            chunk_id=row['id'],
                            text=row['text'],
                            similarity_score=row['similarity_score'],
                            metadata=row['metadata'] or {},
                            table_name='documents'
                        ))

                    logger.info("Document-specific search completed",
                               document_id=document_id,
                               results_count=len(results),
                               similarity_threshold=similarity_threshold)

                    return results

        except Exception as e:
            logger.error("Document-specific search failed",
                        document_id=document_id,
                        error=str(e))
            raise Exception(f"Document search error: {str(e)}")
