"""
Production-Grade Vector Store
Using pgvector with proper similarity calculation and metadata
"""

import os
import time
import psycopg2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import structlog
from dataclasses import dataclass

from ..config import get_config

logger = structlog.get_logger()


@dataclass
class RetrievalResult:
    """Clean retrieval result"""
    chunk_id: str
    text: str
    similarity_score: float
    metadata: Dict[str, Any]
    source_info: str


class ProductionVectorStore:
    """Production-grade vector storage with proper metadata"""
    
    def __init__(self):
        from docqa.config import get_config
        self.config = get_config()
        self.db_url = self.config.database_url.replace("postgresql+asyncpg://", "postgresql://")
    
    def clear_franchisor_embeddings(self, franchisor_id: str) -> bool:
        """Clear existing embeddings for a franchisor"""
        try:
            conn = psycopg2.connect(self.db_url)
            cur = conn.cursor()
            
            # Clear franchisor embedding
            cur.execute("""
                UPDATE franchisors 
                SET embedding = NULL, updated_at = NOW()
                WHERE id = %s
            """, (franchisor_id,))
            
            rows_updated = cur.rowcount
            conn.commit()
            
            print(f"✅ Cleared embeddings for franchisor {franchisor_id} ({rows_updated} rows updated)")
            return True
            
        except Exception as e:
            print(f"❌ Error clearing embeddings: {e}")
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()
    
    def store_franchisor_embedding(
        self, 
        franchisor_id: str, 
        text_content: str, 
        embedding: List[float],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Store franchisor embedding with proper validation"""
        try:
            # Validate embedding
            if len(embedding) != 1536:
                raise ValueError(f"Invalid embedding dimension: {len(embedding)}")
            
            conn = psycopg2.connect(self.db_url)
            cur = conn.cursor()
            
            # Store embedding as proper vector
            cur.execute("""
                UPDATE franchisors 
                SET embedding = %s::vector, updated_at = NOW()
                WHERE id = %s
            """, (embedding, franchisor_id))
            
            if cur.rowcount == 0:
                print(f"❌ No franchisor found with ID: {franchisor_id}")
                return False
            
            conn.commit()
            print(f"✅ Stored embedding for franchisor {franchisor_id}")
            return True
            
        except Exception as e:
            print(f"❌ Error storing embedding: {e}")
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()
    
    def search_similar(
        self, 
        query_embedding: List[float], 
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        franchisor_id: Optional[str] = None
    ) -> List[RetrievalResult]:
        """
        Search for similar content with proper similarity calculation
        
        Args:
            query_embedding: 1536-dim query embedding
            top_k: Number of results to return (3-10 recommended)
            similarity_threshold: Minimum cosine similarity (0.7-0.8 recommended)
            franchisor_id: Optional franchisor filter
            
        Returns:
            List of RetrievalResult objects
        """
        try:
            # Validate query embedding
            if len(query_embedding) != 1536:
                raise ValueError(f"Invalid query embedding dimension: {len(query_embedding)}")
            
            conn = psycopg2.connect(self.db_url)
            cur = conn.cursor()
            
            # Search franchisors table
            if franchisor_id:
                cur.execute("""
                    SELECT
                        id,
                        name,
                        region,
                        brochure_url,
                        1 - (embedding <=> %s::vector) as similarity_score
                    FROM franchisors
                    WHERE id = %s 
                        AND embedding IS NOT NULL
                        AND is_active = true
                        AND is_deleted = false
                        AND (1 - (embedding <=> %s::vector)) >= %s
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s
                """, (query_embedding, franchisor_id, query_embedding, similarity_threshold, query_embedding, top_k))
            else:
                cur.execute("""
                    SELECT
                        id,
                        name,
                        region,
                        brochure_url,
                        1 - (embedding <=> %s::vector) as similarity_score
                    FROM franchisors
                    WHERE embedding IS NOT NULL
                        AND is_active = true
                        AND is_deleted = false
                        AND (1 - (embedding <=> %s::vector)) >= %s
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s
                """, (query_embedding, query_embedding, similarity_threshold, query_embedding, top_k))
            
            results = []
            for row in cur.fetchall():
                result = RetrievalResult(
                    chunk_id=row[0],
                    text=f"Franchisor: {row[1]} (Region: {row[2]})",
                    similarity_score=float(row[4]),
                    metadata={
                        'type': 'franchisor',
                        'name': row[1],
                        'region': row[2],
                        'brochure_url': row[3]
                    },
                    source_info=f"Franchisor: {row[1]}"
                )
                results.append(result)
            
            print(f"✅ Found {len(results)} results with similarity >= {similarity_threshold}")
            return results
            
        except Exception as e:
            print(f"❌ Error in similarity search: {e}")
            return []
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()