"""
Production-Grade Embedding Service
Using text-embedding-3-small with proper normalization and validation
"""

import os
import time
import numpy as np
import openai
import re
from typing import List, Dict, Any, Optional
import structlog

from ..config import get_config

logger = structlog.get_logger()

class ProductionTextNormalizer:
    """Production-grade text normalization"""

    @staticmethod
    def normalize_text(text: str) -> str:
        """
        Normalize text following best practices:
        - Lowercase conversion
        - Remove extra whitespace
        - Clean special characters
        - Preserve meaningful punctuation
        """
        if not text:
            return ""

        # Convert to lowercase
        text = text.lower()

        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text)

        # Remove non-printable characters but preserve newlines and tabs
        text = re.sub(r'[^\x20-\x7E\n\t]', '', text)

        # Clean up multiple punctuation
        text = re.sub(r'[.]{2,}', '.', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)

        # Remove leading/trailing whitespace
        text = text.strip()

        return text

class ProductionEmbeddingService:
    """Production-grade embedding service with consistency guarantees"""
    
    def __init__(self):
        from docqa.vector_store import EmbeddingService
        self.embedding_service = EmbeddingService()
        self.model_name = self.embedding_service.model
        self.expected_dimension = 1536
        
        print(f"✅ Initialized embedding service with model: {self.model_name}")
        print(f"✅ Expected embedding dimension: {self.expected_dimension}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding with validation
        
        Args:
            text: Input text
            
        Returns:
            1536-dimensional embedding vector
            
        Raises:
            ValueError: If embedding dimension is incorrect
        """
        if not text or not text.strip():
            raise ValueError("Cannot generate embedding for empty text")
        
        # Normalize text before embedding
        normalized_text = ProductionTextNormalizer.normalize_text(text)
        
        # Generate embedding
        embedding = self.embedding_service.generate_embedding(normalized_text)
        
        # Validate dimension
        if len(embedding) != self.expected_dimension:
            raise ValueError(f"Invalid embedding dimension: {len(embedding)}, expected {self.expected_dimension}")
        
        # Normalize embedding for cosine similarity
        embedding_array = np.array(embedding, dtype=np.float32)
        normalized_embedding = embedding_array / np.linalg.norm(embedding_array)
        
        return normalized_embedding.tolist()
    
    def generate_batch_embeddings(self, texts: List[str], batch_size: int = 10) -> List[List[float]]:
        """Generate embeddings in batches with validation"""
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_embeddings = []
            
            for text in batch:
                try:
                    embedding = self.generate_embedding(text)
                    batch_embeddings.append(embedding)
                except Exception as e:
                    print(f"❌ Failed to generate embedding for text: {text[:50]}... Error: {e}")
                    # Skip this text
                    continue
            
            embeddings.extend(batch_embeddings)
            
            # Add small delay to avoid rate limiting
            if i + batch_size < len(texts):
                time.sleep(0.1)
        
        return embeddings