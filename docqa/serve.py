"""
Central API for DocQA system - External integration point
"""

from typing import Op<PERSON>, Dict, Any
import structlog

from .ask import QuestionAnsweringService
from .config import get_config
from .types import RAGResponse, DocQAException
from .production_integration import production_rag, ProductionRAGSystem
from .brochure_qa_system import BrochureQASystem

logger = structlog.get_logger()

# Global service instance for reuse
_qa_service: Optional[QuestionAnsweringService] = None
_production_rag: Optional[ProductionRAGSystem] = None


def get_qa_service() -> QuestionAnsweringService:
    """Get or create QA service instance (singleton pattern)"""
    global _qa_service
    
    if _qa_service is None:
        try:
            _qa_service = QuestionAnsweringService()
            logger.info("QA service initialized")
        except Exception as e:
            logger.error("Failed to initialize QA service", error=str(e))
            raise DocQAException(f"QA service initialization failed: {str(e)}")
    
    return _qa_service


async def ask_question_production(question: str, **kwargs) -> str:
    """
    Production-grade question answering using enhanced RAG system.

    This function uses the production-grade RAG implementation with:
    - Proper text normalization and chunking
    - 1536-dimensional embedding validation
    - Fallback similarity thresholds
    - Structured prompt engineering
    - Comprehensive error handling

    Args:
        question: The question to ask
        **kwargs: Additional parameters:
            - franchisor_id: Optional franchisor filter
            - top_k: Number of results to retrieve (default: 5)
            - similarity_threshold: Minimum similarity score (default: 0.5)
            - temperature: Generation temperature (default: 0.1)
            - max_tokens: Maximum response tokens (default: 600)
            - format: Response format ('text' or 'json', default: 'text')

    Returns:
        String answer to the question

    Example:
        >>> answer = await ask_question_production("What is Coochie Hydrogreen?")
        >>> print(answer)
        "Coochie Hydrogreen is a franchisor located in Australia."
    """
    try:
        # Validate input
        if not question or not question.strip():
            return "Please provide a valid question."

        # Extract parameters with production-grade defaults
        franchisor_id = kwargs.get('franchisor_id')
        top_k = kwargs.get('top_k', 5)
        similarity_threshold = kwargs.get('similarity_threshold', 0.5)
        temperature = kwargs.get('temperature', 0.1)
        max_tokens = kwargs.get('max_tokens', 600)
        response_format = kwargs.get('format', 'text')

        # Use production RAG system
        result = await production_rag.answer_question(
            question=question.strip(),
            franchisor_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )

        if not result['success']:
            return f"Error: {result.get('error', 'Unknown error occurred')}"

        # Format response
        if response_format == 'json':
            import json
            return json.dumps(result, indent=2, default=str)
        else:
            answer = result['answer']

            # Add source information if requested
            if kwargs.get('include_metadata', False) and result['sources']:
                answer += "\n\n--- Sources ---"
                for i, source in enumerate(result['sources'], 1):
                    score = source['similarity_score']
                    text_preview = source['text'][:100] + "..." if len(source['text']) > 100 else source['text']
                    answer += f"\n{i}. Score: {score:.3f} - {text_preview}"

            return answer

    except Exception as e:
        logger.error("Production question processing failed",
                    question=question[:100],
                    error=str(e))
        return f"Error processing question: {str(e)}"


def ask_question(question: str, **kwargs) -> str:
    """
    Central API method for asking questions about documents and franchisors.
    
    This is the main entry point for external systems like Kudosity integration.
    
    Args:
        question: The question to ask
        **kwargs: Additional parameters:
            - top_k: Number of results to retrieve (default: 6)
            - similarity_threshold: Minimum similarity score (default: 0.7)
            - include_metadata: Whether to include source metadata in response (default: False)
            - format: Response format ('text' or 'json', default: 'text')
    
    Returns:
        String answer to the question
        
    Raises:
        DocQAException: If question processing fails
        
    Example:
        >>> answer = ask_question("What are the franchise opportunities in Melbourne?")
        >>> print(answer)
        "Based on our franchisor information, there are several franchise opportunities in Melbourne..."
    """
    try:
        # Validate input
        if not question or not question.strip():
            return "Please provide a valid question."
        
        # Get service instance
        qa_service = get_qa_service()

        # Extract parameters
        config = get_config()
        top_k = kwargs.get('top_k', config.top_k)
        similarity_threshold = kwargs.get('similarity_threshold', config.similarity_threshold)
        include_metadata = kwargs.get('include_metadata', False)
        response_format = kwargs.get('format', 'text')
        temperature = kwargs.get('temperature', config.temperature)
        max_tokens = kwargs.get('max_tokens', config.max_tokens)

        # Process question
        response = qa_service.ask_question(
            question=question.strip(),
            top_k=top_k,
            similarity_threshold=similarity_threshold,
            include_sources=include_metadata,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        # Format response based on requested format
        if response_format == 'json':
            return _format_json_response(response, include_metadata)
        else:
            return _format_text_response(response, include_metadata)
            
    except DocQAException:
        raise
    except Exception as e:
        logger.error("Question processing failed", 
                    question=question[:100],
                    error=str(e))
        raise DocQAException(f"Failed to process question: {str(e)}")


def _format_text_response(response: RAGResponse, include_metadata: bool) -> str:
    """Format response as plain text"""
    answer = response.answer
    
    if include_metadata and response.sources:
        # Add source information
        franchisor_sources = [s for s in response.sources if s.table_name == 'franchisors']
        document_sources = [s for s in response.sources if s.table_name == 'documents']
        
        if franchisor_sources or document_sources:
            answer += "\n\n--- Sources ---"
            
            if franchisor_sources:
                answer += "\nFranchisors:"
                for i, source in enumerate(franchisor_sources, 1):
                    name = source.metadata.get('name', 'Unknown')
                    region = source.metadata.get('region', 'Unknown region')
                    answer += f"\n{i}. {name} ({region}) - Relevance: {source.similarity_score:.2f}"
            
            if document_sources:
                answer += "\nDocuments:"
                for i, source in enumerate(document_sources, 1):
                    doc_name = source.metadata.get('document_name', 'Unknown document')
                    answer += f"\n{i}. {doc_name} - Relevance: {source.similarity_score:.2f}"
    
    return answer


def _format_json_response(response: RAGResponse, include_metadata: bool) -> str:
    """Format response as JSON string"""
    import json
    
    result = {
        'answer': response.answer,
        'processing_time': response.processing_time,
        'model_used': response.model_used
    }
    
    if include_metadata and response.sources:
        result['sources'] = {
            'franchisors': [
                {
                    'name': s.metadata.get('name', 'Unknown'),
                    'region': s.metadata.get('region', 'Unknown'),
                    'similarity_score': s.similarity_score,
                    'brochure_url': s.metadata.get('brochure_url')
                }
                for s in response.sources if s.table_name == 'franchisors'
            ],
            'documents': [
                {
                    'document_name': s.metadata.get('document_name', 'Unknown'),
                    'file_type': s.metadata.get('file_type'),
                    'similarity_score': s.similarity_score
                }
                for s in response.sources if s.table_name == 'documents'
            ]
        }
        result['source_count'] = {
            'franchisors': len(result['sources']['franchisors']),
            'documents': len(result['sources']['documents'])
        }
    
    return json.dumps(result, indent=2)


def ask_question_stream(question: str, **kwargs):
    """
    Stream answer generation for real-time responses
    
    Args:
        question: The question to ask
        **kwargs: Additional parameters (same as ask_question)
        
    Yields:
        String chunks of the answer as they are generated
    """
    try:
        if not question or not question.strip():
            yield "Please provide a valid question."
            return
        
        qa_service = get_qa_service()

        config = get_config()
        top_k = kwargs.get('top_k', config.top_k)
        similarity_threshold = kwargs.get('similarity_threshold', config.similarity_threshold)
        
        for chunk in qa_service.stream_answer(
            question=question.strip(),
            top_k=top_k,
            similarity_threshold=similarity_threshold
        ):
            yield chunk
            
    except Exception as e:
        logger.error("Streaming question failed", error=str(e))
        yield f"Error: {str(e)}"


def get_question_context(question: str, **kwargs) -> Dict[str, Any]:
    """
    Get context information for a question without generating an answer
    
    Useful for debugging or understanding what information would be used
    to answer a question.
    
    Args:
        question: The question to analyze
        **kwargs: Additional parameters
        
    Returns:
        Dictionary with context information
    """
    try:
        qa_service = get_qa_service()
        top_k = kwargs.get('top_k', 3)
        
        return qa_service.get_conversation_context(question, top_k)
        
    except Exception as e:
        logger.error("Failed to get question context", error=str(e))
        return {'error': str(e)}


def health_check() -> Dict[str, Any]:
    """
    Health check for the DocQA system

    Returns:
        Dictionary with system status information
    """
    try:
        # Test basic functionality
        qa_service = get_qa_service()

        # Test embedding service
        test_embedding = qa_service.embedding_service.generate_embedding("test")
        embedding_ok = len(test_embedding) == 1536

        # Test production embedding service
        production_embedding = production_rag.embedding_service.generate_embedding("test")
        production_embedding_ok = len(production_embedding) == 1536

        # Test database connection
        with qa_service.vector_store._get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                db_ok = cur.fetchone() is not None

        config = get_config()
        return {
            'status': 'healthy',
            'embedding_service': 'ok' if embedding_ok else 'error',
            'production_embedding_service': 'ok' if production_embedding_ok else 'error',
            'database': 'ok' if db_ok else 'error',
            'config': {
                'chat_model': config.chat_model,
                'embedding_model': config.embedding_model,
                'top_k': config.top_k,
                'similarity_threshold': config.similarity_threshold
            },
            'production_config': {
                'embedding_model': production_rag.embedding_service.model_name,
                'embedding_dimension': 1536,
                'chunk_size': 400,
                'chunk_overlap': 75,
                'similarity_threshold': 0.5,
                'top_k': 5
            }
        }

    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return {
            'status': 'unhealthy',
            'error': str(e)
        }


def reset_service():
    """Reset the global service instance (useful for testing)"""
    global _qa_service
    _qa_service = None
    logger.info("QA service reset")



async def ask_brochure_question(question: str, **kwargs) -> str:
    """
    Brochure-optimized question answering for company brochures.
    
    This function uses brochure-specific processing with:
    - Section-aware chunking for marketing content
    - Lower similarity thresholds for brochure content
    - Enhanced prompts for company information
    - Metadata extraction for contact details
    
    Args:
        question: The question about the company brochure
        **kwargs: Additional parameters:
            - franchisor_id: Optional franchisor filter
            - top_k: Number of results to retrieve (default: 5)
            - similarity_threshold: Minimum similarity score (default: 0.4)
            - temperature: Generation temperature (default: 0.2)
            - max_tokens: Maximum response tokens (default: 800)
            - format: Response format ('text' or 'json', default: 'text')
    
    Returns:
        String answer about the company brochure
        
    Example:
        >>> answer = await ask_brochure_question("What services does the company provide?")
        >>> print(answer)
        "The company provides lawn care and maintenance services..."
    """
    try:
        # Validate input
        if not question or not question.strip():
            return "Please provide a valid question about the company brochure."
        
        # Extract parameters with brochure-optimized defaults
        franchisor_id = kwargs.get('franchisor_id')
        top_k = kwargs.get('top_k', 5)
        similarity_threshold = kwargs.get('similarity_threshold', 0.4)  # Lower for brochures
        temperature = kwargs.get('temperature', 0.2)  # Balanced for marketing
        max_tokens = kwargs.get('max_tokens', 800)
        response_format = kwargs.get('format', 'text')
        
        # Use brochure QA system
        from .production_integration import brochure_rag
        
        result = await brochure_rag.answer_brochure_question(
            question=question.strip(),
            franchisor_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )
        
        if not result['success']:
            return f"Error: {result.get('error', 'Unknown error occurred')}"
        
        # Format response
        if response_format == 'json':
            import json
            return json.dumps(result, indent=2, default=str)
        else:
            answer = result['answer']
            
            # Add source information if requested
            if kwargs.get('include_metadata', False) and result['sources']:
                answer += "\n\n--- Sources ---"
                for i, source in enumerate(result['sources'], 1):
                    score = source['similarity_score']
                    text_preview = source['text'][:100] + "..." if len(source['text']) > 100 else source['text']
                    answer += f"\n{i}. Score: {score:.3f} - {text_preview}"
            
            return answer
            
    except Exception as e:
        logger.error("Brochure question processing failed", 
                    question=question[:100],
                    error=str(e))
        return f"Error processing brochure question: {str(e)}"


# Convenience aliases for external use
ask = ask_question
ask_production = ask_question_production
ask_brochure = ask_brochure_question
ask_stream = ask_question_stream
