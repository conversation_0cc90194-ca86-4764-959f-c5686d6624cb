"""
PDF file handler using PyMuPDF
"""

import fitz  # PyMuPDF
from pathlib import Path
from typing import List, Dict
import structlog

from .base_handler import BaseFileHandler
from ..types import FileProcessingResult

logger = structlog.get_logger()


class PDFHandler(BaseFileHandler):
    """Handler for PDF files using PyMuPDF"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.pdf']
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this handler can process the file"""
        return file_path.suffix.lower() in self.supported_extensions
    
    def process_file(self, file_path: Path) -> FileProcessingResult:
        """Process PDF file and extract content"""
        if not self.validate_file(file_path):
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message="File validation failed"
            )

        doc = None
        try:
            logger.info("Processing PDF file", path=str(file_path))

            # Open PDF document
            doc = fitz.open(str(file_path))

            # Check if PDF is encrypted
            if doc.needs_pass:
                doc.close()
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="PDF is password protected"
                )

            # Check if PDF has pages
            if len(doc) == 0:
                doc.close()
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="PDF has no pages"
                )

            text_content = ""
            images_extracted = []
            tables_extracted = []

            # Process each page
            for page_num in range(len(doc)):
                page = doc[page_num]

                # Extract text with better formatting
                page_text = page.get_text("text")
                if page_text.strip():
                    text_content += f"\n--- Page {page_num + 1} ---\n"
                    text_content += page_text
                
                # Extract images
                image_list = page.get_images()
                for img_index, img in enumerate(image_list):
                    try:
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)
                        
                        if pix.n - pix.alpha < 4:  # GRAY or RGB
                            img_data = pix.tobytes("png")
                            img_info = {
                                'page': page_num + 1,
                                'index': img_index,
                                'width': pix.width,
                                'height': pix.height,
                                'data': img_data
                            }
                            images_extracted.append(img_info)
                        
                        pix = None  # Free memory
                        
                    except Exception as e:
                        logger.warning("Failed to extract image", 
                                     page=page_num + 1,
                                     img_index=img_index,
                                     error=str(e))
                
                # Extract tables (basic table detection)
                tables = self._extract_tables_from_page(page, page_num + 1)
                tables_extracted.extend(tables)
            
            # Clean the extracted text
            cleaned_text = self.clean_text(text_content)

            if not cleaned_text:
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="No text content extracted from PDF"
                )

            logger.info("PDF processing completed",
                       path=str(file_path),
                       pages=len(doc),
                       text_length=len(cleaned_text),
                       images_count=len(images_extracted),
                       tables_count=len(tables_extracted))

            return FileProcessingResult(
                success=True,
                text_content=cleaned_text,
                images_extracted=images_extracted,
                tables_extracted=tables_extracted
            )

        except Exception as e:
            logger.error("PDF processing failed",
                        path=str(file_path),
                        error=str(e))
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message=f"PDF processing failed: {str(e)}"
            )
        finally:
            # Ensure document is always closed
            if doc is not None:
                try:
                    doc.close()
                except:
                    pass
    
    def _extract_tables_from_page(self, page, page_num: int) -> List[Dict]:
        """Extract tables from a PDF page (basic implementation)"""
        tables = []
        
        try:
            # Get text blocks with position information
            blocks = page.get_text("dict")
            
            # Simple table detection based on text alignment
            # This is a basic implementation - for production, consider using
            # libraries like camelot-py or tabula-py
            
            potential_table_blocks = []
            
            for block in blocks.get("blocks", []):
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line.get("spans", []):
                            text = span.get("text", "").strip()
                            if text and self._looks_like_table_content(text):
                                potential_table_blocks.append({
                                    'text': text,
                                    'bbox': span.get("bbox"),
                                    'page': page_num
                                })
            
            # Group nearby blocks into tables
            if potential_table_blocks:
                table_data = self._group_table_blocks(potential_table_blocks)
                if table_data:
                    tables.append({
                        'page': page_num,
                        'type': 'table',
                        'data': table_data,
                        'row_count': len(table_data),
                        'col_count': max(len(row) for row in table_data) if table_data else 0
                    })
        
        except Exception as e:
            logger.warning("Table extraction failed", 
                          page=page_num,
                          error=str(e))
        
        return tables
    
    def _looks_like_table_content(self, text: str) -> bool:
        """Check if text looks like it could be part of a table"""
        # Simple heuristics for table detection
        indicators = [
            '\t' in text,  # Tab-separated
            '|' in text,   # Pipe-separated
            text.count(' ') > 3 and len(text) < 100,  # Multiple spaces, short text
            any(char.isdigit() for char in text) and len(text.split()) <= 5  # Numbers with few words
        ]
        
        return any(indicators)
    
    def _group_table_blocks(self, blocks: List[Dict]) -> List[List[str]]:
        """Group text blocks into table rows and columns"""
        if not blocks:
            return []
        
        # Sort blocks by vertical position (y-coordinate)
        blocks.sort(key=lambda b: b['bbox'][1])  # Sort by top y-coordinate
        
        # Group blocks into rows based on similar y-coordinates
        rows = []
        current_row = []
        current_y = None
        y_tolerance = 5  # Pixels tolerance for same row
        
        for block in blocks:
            block_y = block['bbox'][1]
            
            if current_y is None or abs(block_y - current_y) <= y_tolerance:
                current_row.append(block)
                current_y = block_y
            else:
                if current_row:
                    # Sort current row by x-coordinate and extract text
                    current_row.sort(key=lambda b: b['bbox'][0])
                    row_text = [b['text'] for b in current_row]
                    rows.append(row_text)
                
                current_row = [block]
                current_y = block_y
        
        # Add the last row
        if current_row:
            current_row.sort(key=lambda b: b['bbox'][0])
            row_text = [b['text'] for b in current_row]
            rows.append(row_text)
        
        return rows
