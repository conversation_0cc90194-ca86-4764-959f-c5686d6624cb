"""
Enhanced PDF file handler with improved text extraction, OCR, and structure preservation
"""

import re
import io
from pathlib import Path
from typing import List, Dict, Any, Optional
import structlog

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    import pytesseract
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

from .base_handler import BaseFileHandler
from ..types import FileProcessingResult

logger = structlog.get_logger()


class EnhancedPDFHandler(BaseFileHandler):
    """Enhanced PDF handler with better text extraction and OCR capabilities"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.pdf']

    def can_handle(self, file_path: Path) -> bool:
        """Check if this handler can process the given file"""
        return file_path.suffix.lower() == '.pdf'
    
    def validate_file(self, file_path: Path) -> bool:
        """Validate PDF file"""
        if not PYMUPDF_AVAILABLE:
            logger.error("PyMuPDF not available for PDF processing")
            return False
        
        try:
            doc = fitz.open(str(file_path))
            doc.close()
            return True
        except Exception as e:
            logger.error("PDF validation failed", path=str(file_path), error=str(e))
            return False
    
    def process_file(self, file_path: Path) -> FileProcessingResult:
        """Enhanced PDF processing with better text extraction and structure preservation"""
        if not self.validate_file(file_path):
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message="File validation failed"
            )

        doc = None
        try:
            logger.info("Processing PDF with enhanced extraction", path=str(file_path))

            # Open PDF document
            doc = fitz.open(str(file_path))

            # Check if PDF is encrypted
            if doc.needs_pass:
                doc.close()
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="PDF is password protected"
                )

            # Check if PDF has pages
            if len(doc) == 0:
                doc.close()
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="PDF has no pages"
                )

            text_content = ""
            images_extracted = []
            tables_extracted = []

            # Process each page with enhanced extraction
            try:
                for page_num in range(len(doc)):
                    page = doc[page_num]

                    # Enhanced text extraction with multiple methods
                    page_text = self._extract_enhanced_text(page, page_num + 1)
                    if page_text.strip():
                        text_content += f"\n=== PAGE {page_num + 1} ===\n"
                        text_content += page_text + "\n"

                    # Extract images with OCR if needed
                    try:
                        image_list = page.get_images()
                        for img_index, img in enumerate(image_list):
                            try:
                                xref = img[0]
                                pix = fitz.Pixmap(doc, xref)

                                if pix.n - pix.alpha < 4:  # GRAY or RGB
                                    img_data = pix.tobytes("png")

                                    # Try OCR on image if it might contain text
                                    ocr_text = self._extract_text_from_image(pix, page_num + 1, img_index)
                                    if ocr_text.strip():
                                        text_content += f"\n[IMAGE TEXT - Page {page_num + 1}, Image {img_index + 1}]\n"
                                        text_content += ocr_text + "\n"

                                    img_info = {
                                        'page': page_num + 1,
                                        'index': img_index,
                                        'width': pix.width,
                                        'height': pix.height,
                                        'data': img_data,
                                        'ocr_text': ocr_text
                                    }
                                    images_extracted.append(img_info)

                                pix = None  # Free memory

                            except Exception as e:
                                logger.warning("Failed to extract image",
                                             page=page_num + 1,
                                             img_index=img_index,
                                             error=str(e))
                    except Exception as e:
                        logger.warning("Failed to extract images from page",
                                     page=page_num + 1,
                                     error=str(e))

                    # Enhanced table extraction with structure preservation
                    try:
                        tables = self._extract_enhanced_tables(page, page_num + 1)
                        tables_extracted.extend(tables)

                        # Add table content to text
                        for table in tables:
                            if table.get('structured_text'):
                                text_content += f"\n[TABLE - Page {page_num + 1}]\n"
                                text_content += table['structured_text'] + "\n"
                    except Exception as e:
                        logger.warning("Failed to extract tables from page",
                                     page=page_num + 1,
                                     error=str(e))
            finally:
                # Ensure document is closed even if processing fails
                try:
                    doc.close()
                except:
                    pass
            
            # Enhanced text cleaning and normalization
            cleaned_text = self._enhanced_text_cleaning(text_content)

            if not cleaned_text:
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="No text content extracted from PDF"
                )

            logger.info("Enhanced PDF processing completed",
                       path=str(file_path),
                       pages=len(doc),
                       text_length=len(cleaned_text),
                       images_count=len(images_extracted),
                       tables_count=len(tables_extracted))

            return FileProcessingResult(
                success=True,
                text_content=cleaned_text,
                images_extracted=images_extracted,
                tables_extracted=tables_extracted
            )

        except Exception as e:
            # Ensure document is closed in case of error
            if doc:
                try:
                    doc.close()
                except:
                    pass
            logger.error("Enhanced PDF processing failed",
                        path=str(file_path),
                        error=str(e))
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message=f"PDF processing failed: {str(e)}"
            )
    
    def _extract_enhanced_text(self, page, page_num: int) -> str:
        """Extract text with improved cleaning and structure preservation"""
        try:
            # Use standard text extraction but with better processing
            text = page.get_text("text")

            if not text.strip():
                return ""

            # Apply enhanced cleaning
            cleaned_text = self._clean_extracted_text(text)

            return cleaned_text

        except Exception as e:
            logger.warning("Text extraction failed", page=page_num, error=str(e))
            return ""

    def _clean_extracted_text(self, text: str) -> str:
        """Clean and enhance extracted text"""
        if not text:
            return ""

        # Fix common PDF extraction issues
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Fix broken words across lines
        text = re.sub(r'(\w+)-\s*\n\s*(\w+)', r'\1\2', text)

        # Fix sentences split across lines
        text = re.sub(r'([a-z,])\s*\n\s*([A-Z])', r'\1 \2', text)

        # Normalize line breaks
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

        # Remove page numbers and headers/footers
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()

            # Skip empty lines
            if not line:
                cleaned_lines.append('')
                continue

            # Skip obvious page numbers
            if re.match(r'^\d+$', line) and len(line) <= 3:
                continue

            # Skip common headers/footers
            if any(pattern in line.lower() for pattern in [
                'page ', 'confidential', 'proprietary', 'copyright', '©'
            ]) and len(line) < 50:
                continue

            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines).strip()

    def _extract_text_from_image(self, pix, page_num: int, img_index: int) -> str:
        """Extract text from image using OCR"""
        if not OCR_AVAILABLE:
            return ""

        try:
            # Convert PyMuPDF pixmap to PIL Image
            img_data = pix.tobytes("png")
            pil_image = Image.open(io.BytesIO(img_data))

            # Preprocess image for better OCR
            if CV2_AVAILABLE:
                # Convert to OpenCV format
                opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

                # Enhance image for OCR
                gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)

                # Apply threshold to get better contrast
                _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

                # Convert back to PIL
                pil_image = Image.fromarray(thresh)

            # Perform OCR
            ocr_text = pytesseract.image_to_string(pil_image, lang='eng')

            # Clean OCR text
            cleaned_ocr = self._clean_ocr_text(ocr_text)

            if len(cleaned_ocr.strip()) > 10:  # Only return if substantial text found
                logger.debug("OCR extracted text from image",
                           page=page_num,
                           img_index=img_index,
                           text_length=len(cleaned_ocr))
                return cleaned_ocr

        except Exception as e:
            logger.warning("OCR failed for image",
                         page=page_num,
                         img_index=img_index,
                         error=str(e))

        return ""

    def _clean_ocr_text(self, text: str) -> str:
        """Clean OCR-extracted text"""
        if not text:
            return ""

        # Remove common OCR artifacts
        text = re.sub(r'[^\w\s\.\,\!\?\:\;\-\(\)\[\]\"\'\/\&\%\$\#\@\+\=]', ' ', text)

        # Fix common OCR mistakes
        text = re.sub(r'\b[Il]\b', 'I', text)  # Fix I/l confusion
        text = re.sub(r'\b0\b', 'O', text)     # Fix O/0 confusion in words

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove very short lines (likely artifacts)
        lines = text.split('\n')
        cleaned_lines = [line.strip() for line in lines if len(line.strip()) > 2]

        return '\n'.join(cleaned_lines).strip()

    def _extract_enhanced_tables(self, page, page_num: int) -> List[Dict[str, Any]]:
        """Extract tables with simplified but reliable approach"""
        tables = []

        try:
            # Try to find tables using PyMuPDF
            table_list = page.find_tables()

            for table_index, table in enumerate(table_list):
                try:
                    # Extract table data
                    table_data = table.extract()

                    if table_data and len(table_data) > 0:
                        # Convert to simple text format
                        structured_text = self._simple_table_format(table_data)

                        table_info = {
                            'page': page_num,
                            'index': table_index,
                            'rows': len(table_data),
                            'cols': len(table_data[0]) if table_data else 0,
                            'structured_text': structured_text
                        }
                        tables.append(table_info)

                except Exception as e:
                    logger.debug("Failed to extract table",
                                page=page_num,
                                table_index=table_index,
                                error=str(e))

        except Exception as e:
            logger.debug("Table detection failed", page=page_num, error=str(e))

        return tables

    def _simple_table_format(self, table_data: List[List[str]]) -> str:
        """Format table data as simple readable text"""
        if not table_data:
            return ""

        formatted_lines = []

        for row_idx, row in enumerate(table_data):
            # Clean and join cells with | separator
            cleaned_cells = []
            for cell in row:
                cell_text = str(cell or "").strip()
                if cell_text:
                    cleaned_cells.append(cell_text)
                else:
                    cleaned_cells.append("-")

            if cleaned_cells:
                formatted_lines.append(" | ".join(cleaned_cells))

                # Add separator after header
                if row_idx == 0:
                    formatted_lines.append("-" * 50)

        return "\n".join(formatted_lines)

    def _enhanced_text_cleaning(self, text: str) -> str:
        """Simplified but effective text cleaning"""
        if not text:
            return ""

        # Apply the same cleaning as in _clean_extracted_text
        return self._clean_extracted_text(text)

    # Removed complex text cleaning methods in favor of the simpler _clean_extracted_text method
