"""
File handler factory for automatic handler selection
"""

from pathlib import Path
from typing import Optional, List
import structlog

from .base_handler import <PERSON>FileHand<PERSON>
from .pdf_handler import <PERSON><PERSON>andler
from .docx_handler import <PERSON>x<PERSON>and<PERSON>
from .image_handler import ImageHandler
from ..types import FileProcessingResult

logger = structlog.get_logger()


class FileHandlerFactory:
    """Factory for creating and managing file handlers"""
    
    def __init__(self):
        self.handlers: List[BaseFileHandler] = [
            PDF<PERSON>and<PERSON>(),
            DocxHandler(),
            ImageHandler()
        ]
        
        logger.info("File handler factory initialized", 
                   handler_count=len(self.handlers))
    
    def get_handler(self, file_path: Path) -> Optional[BaseFileHandler]:
        """
        Get the appropriate handler for a file
        
        Args:
            file_path: Path to the file
            
        Returns:
            Handler that can process the file, or None if no handler available
        """
        for handler in self.handlers:
            if handler.can_handle(file_path):
                logger.debug("Handler found", 
                           file_path=str(file_path),
                           handler=handler.__class__.__name__)
                return handler
        
        logger.warning("No handler found for file", 
                      file_path=str(file_path),
                      extension=file_path.suffix.lower())
        return None
    
    def process_file(self, file_path: Path) -> FileProcessingResult:
        """
        Process a file using the appropriate handler
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            FileProcessingResult with processing outcome
        """
        handler = self.get_handler(file_path)
        
        if handler is None:
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message=f"No handler available for file type: {file_path.suffix}"
            )
        
        return handler.process_file(file_path)
    
    def get_supported_extensions(self) -> List[str]:
        """Get all supported file extensions"""
        extensions = []
        for handler in self.handlers:
            extensions.extend(handler.supported_extensions)
        return sorted(list(set(extensions)))
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if a file type is supported"""
        return self.get_handler(file_path) is not None
    
    def get_handler_info(self) -> dict:
        """Get information about all available handlers"""
        info = {}
        for handler in self.handlers:
            handler_name = handler.__class__.__name__
            info[handler_name] = {
                'extensions': handler.supported_extensions,
                'description': handler.__doc__ or "No description available"
            }
        return info


# Global factory instance
_factory_instance: Optional[FileHandlerFactory] = None


def get_file_handler_factory() -> FileHandlerFactory:
    """Get the global file handler factory instance"""
    global _factory_instance
    
    if _factory_instance is None:
        _factory_instance = FileHandlerFactory()
    
    return _factory_instance


def process_file(file_path: Path) -> FileProcessingResult:
    """
    Convenience function to process a file
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        FileProcessingResult with processing outcome
    """
    factory = get_file_handler_factory()
    return factory.process_file(file_path)


def get_supported_extensions() -> List[str]:
    """Get all supported file extensions"""
    factory = get_file_handler_factory()
    return factory.get_supported_extensions()


def is_supported_file(file_path: Path) -> bool:
    """Check if a file type is supported"""
    factory = get_file_handler_factory()
    return factory.is_supported(file_path)
