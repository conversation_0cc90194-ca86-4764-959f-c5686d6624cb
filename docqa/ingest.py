"""
Document ingestion pipeline with smart table routing
"""

import concurrent.futures
import hashlib
import time
import uuid
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
import requests
import structlog
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

from .config import config
from .types import (
    DocumentChunk, DocumentMetadata, IngestionResult, 
    FileProcessingResult, TableName
)
from .file_handlers import get_file_handler_factory
from .vector_store import EmbeddingService, PgVectorStore
from .chart_extract import ChartAnalyzer
from .text_processing.adaptive_chunker import AdaptiveChunker
from .cache.intelligent_cache import get_cache

logger = structlog.get_logger()


class DocumentIngestionService:
    """Service for ingesting documents with smart table routing"""
    
    def __init__(self):
        self.embedding_service = EmbeddingService()
        self.vector_store = PgVectorStore()
        self.chart_analyzer = ChartAnalyzer()

        # Initialize file handler factory
        self.file_handler_factory = get_file_handler_factory()

        # Initialize adaptive chunker for intelligent text processing
        self.adaptive_chunker = AdaptiveChunker(
            base_chunk_size=config.chunk_size,
            max_chunk_size=config.chunk_size * 2,
            min_chunk_size=config.chunk_size // 2,
            overlap_ratio=config.chunk_overlap / config.chunk_size
        )

        # Initialize intelligent cache
        self.cache = get_cache()

        # Initialize S3 client for direct file access
        try:
            self.s3_client = boto3.client('s3')
            logger.info("S3 client initialized successfully")
        except (NoCredentialsError, Exception) as e:
            logger.warning("S3 client initialization failed", error=str(e))
            self.s3_client = None

        logger.info("Document ingestion service initialized with optimizations",
                   adaptive_chunking=True,
                   intelligent_caching=True,
                   parallel_processing=getattr(config, 'enable_parallel_processing', True))
    
    def ingest_document(
        self,
        source: str,
        force_table: Optional[TableName] = None,
        translate: bool = False,
        extract_charts: bool = True,
        document_id: Optional[str] = None,
        franchisor_id: Optional[str] = None
    ) -> IngestionResult:
        """
        Ingest a document from URL or file path

        Args:
            source: S3 URL, file path, or HTTP URL
            force_table: Force ingestion to specific table
            translate: Whether to translate content to English
            extract_charts: Whether to extract and analyze charts
            document_id: Optional document ID to use (if None, generates new UUID)
            franchisor_id: Optional franchisor ID for franchisor brochure uploads

        Returns:
            IngestionResult with processing details
        """
        start_time = time.time()
        
        try:
            logger.info("Starting document ingestion", source=source)
            
            # Download or validate file
            file_path = self._prepare_file(source)
            if not file_path:
                return IngestionResult(
                    success=False,
                    document_id="",
                    chunks_created=0,
                    table_name="documents",
                    error_message="Failed to prepare file"
                )
            
            # Determine target table
            target_table = self._determine_target_table(source, force_table)
            
            # Process file content
            processing_result = self._process_file(file_path, extract_charts)
            if not processing_result.success:
                return IngestionResult(
                    success=False,
                    document_id="",
                    chunks_created=0,
                    table_name=target_table,
                    error_message=processing_result.error_message
                )
            
            # Translate if requested
            content = processing_result.text_content
            if translate:
                content = self._translate_content(content)
            
            # Create document metadata
            # Use provided document_id or generate new one
            if document_id is None:
                document_id = str(uuid.uuid4())

            metadata = DocumentMetadata(
                document_id=document_id,
                filename=file_path.name,
                file_type=file_path.suffix.lower(),
                file_size=file_path.stat().st_size,
                source_url=source if source.startswith(('http', 's3://')) else None
            )
            
            # Create chunks and embeddings
            chunks = self._create_chunks(content, metadata)
            if not chunks:
                return IngestionResult(
                    success=False,
                    document_id=document_id,
                    chunks_created=0,
                    table_name=target_table,
                    error_message="No content chunks created"
                )
            
            # Generate embeddings
            embeddings_result = self._generate_embeddings(chunks)
            if not embeddings_result:
                return IngestionResult(
                    success=False,
                    document_id=document_id,
                    chunks_created=len(chunks),
                    table_name=target_table,
                    error_message="Failed to generate embeddings"
                )
            
            # Store in appropriate table
            storage_result = self._store_embeddings(
                document_id, chunks, target_table, metadata, source
            )
            
            # Clean up temporary file
            if file_path.parent == config.temp_dir:
                try:
                    file_path.unlink()
                except:
                    pass
            
            processing_time = time.time() - start_time
            
            if storage_result:
                logger.info("Document ingestion completed",
                           document_id=document_id,
                           table=target_table,
                           chunks=len(chunks),
                           processing_time=processing_time)
                
                return IngestionResult(
                    success=True,
                    document_id=document_id,
                    chunks_created=len(chunks),
                    table_name=target_table,
                    processing_time=processing_time
                )
            else:
                return IngestionResult(
                    success=False,
                    document_id=document_id,
                    chunks_created=len(chunks),
                    table_name=target_table,
                    error_message="Failed to store embeddings"
                )
                
        except Exception as e:
            logger.error("Document ingestion failed", 
                        source=source,
                        error=str(e))
            return IngestionResult(
                success=False,
                document_id="",
                chunks_created=0,
                table_name="documents",
                error_message=f"Ingestion failed: {str(e)}"
            )

    def _process_charts_parallel(self, images: List[bytes]) -> str:
        """Process charts in parallel for better performance"""
        if not images:
            return ""

        chart_content = ""
        max_workers = getattr(config, 'max_workers', 4)

        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all chart analysis tasks
                future_to_image = {
                    executor.submit(self._analyze_single_chart, image_data): i
                    for i, image_data in enumerate(images)
                }

                # Collect results as they complete
                for future in concurrent.futures.as_completed(future_to_image):
                    try:
                        chart_analysis = future.result(timeout=30)  # 30 second timeout per chart
                        if chart_analysis and chart_analysis.get('success'):
                            chart_content += f"\n\nChart Analysis: {chart_analysis.get('description', '')}"
                            if chart_analysis.get('data_points'):
                                chart_content += f"\nData Points: {', '.join(map(str, chart_analysis['data_points']))}"
                    except concurrent.futures.TimeoutError:
                        logger.warning("Chart analysis timed out")
                    except Exception as e:
                        logger.error("Chart analysis failed", error=str(e))

        except Exception as e:
            logger.error("Parallel chart processing failed", error=str(e))
            # Fallback to sequential processing
            return self._process_charts_sequential(images)

        return chart_content

    def _analyze_single_chart(self, image_data: bytes) -> Optional[Dict[str, Any]]:
        """Analyze a single chart image"""
        try:
            return self.chart_analyzer.analyze_chart(image_data)
        except Exception as e:
            logger.error("Single chart analysis failed", error=str(e))
            return None

    def _process_charts_sequential(self, images: List[bytes]) -> str:
        """Fallback sequential chart processing"""
        chart_content = ""
        for image_data in images:
            try:
                chart_analysis = self.chart_analyzer.analyze_chart(image_data)
                if chart_analysis and chart_analysis.get('success'):
                    chart_content += f"\n\nChart Analysis: {chart_analysis.get('description', '')}"
                    if chart_analysis.get('data_points'):
                        chart_content += f"\nData Points: {', '.join(map(str, chart_analysis['data_points']))}"
            except Exception as e:
                logger.error("Chart analysis failed", error=str(e))
                continue
        return chart_content

    def _prepare_file(self, source: str) -> Optional[Path]:
        """Download or validate file from source"""
        try:
            if source.startswith(('http://', 'https://')):
                return self._download_file(source)
            elif source.startswith('s3://'):
                return self._download_s3_file(source)
            else:
                # Local file path
                file_path = Path(source)
                if file_path.exists() and file_path.is_file():
                    return file_path
                else:
                    logger.error("Local file not found", path=source)
                    return None
                    
        except Exception as e:
            logger.error("File preparation failed", source=source, error=str(e))
            return None
    
    def _download_file(self, url: str) -> Optional[Path]:
        """Download file from HTTP URL"""
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            # Determine filename
            parsed_url = urlparse(url)
            filename = Path(parsed_url.path).name
            if not filename:
                filename = f"download_{int(time.time())}"
            
            # Save to temp directory
            temp_file = config.temp_dir / filename
            
            with open(temp_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info("File downloaded", url=url, path=str(temp_file))
            return temp_file
            
        except Exception as e:
            logger.error("File download failed", url=url, error=str(e))
            return None
    
    def _download_s3_file(self, s3_url: str) -> Optional[Path]:
        """Download file from S3 URL using AWS SDK"""
        try:
            if not self.s3_client:
                logger.error("S3 client not available", s3_url=s3_url)
                return None

            # Parse S3 URL: s3://bucket/key
            parsed = urlparse(s3_url)
            bucket = parsed.netloc
            key = parsed.path.lstrip('/')

            logger.info("Downloading file from S3", bucket=bucket, key=key)

            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=Path(key).suffix)
            temp_path = Path(temp_file.name)
            temp_file.close()

            # Download file directly from S3
            self.s3_client.download_file(bucket, key, str(temp_path))

            logger.info("S3 file downloaded successfully",
                       s3_url=s3_url,
                       local_path=str(temp_path),
                       file_size=temp_path.stat().st_size)

            return temp_path

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                logger.error("S3 file not found", s3_url=s3_url, bucket=bucket, key=key)
            elif error_code == 'NoSuchBucket':
                logger.error("S3 bucket not found", s3_url=s3_url, bucket=bucket)
            else:
                logger.error("S3 client error", s3_url=s3_url, error_code=error_code, error=str(e))
            return None
        except NoCredentialsError:
            logger.error("AWS credentials not configured", s3_url=s3_url)
            return None
        except Exception as e:
            logger.error("S3 file download failed", s3_url=s3_url, error=str(e))
            return None
    
    def _determine_target_table(self, source: str, force_table: Optional[TableName]) -> TableName:
        """Determine which table to store embeddings in"""
        if force_table:
            return force_table
        
        try:
            # Check if source URL matches any franchisor brochure_url
            franchisor_id = self._find_matching_franchisor(source)
            if franchisor_id:
                logger.info("Matched franchisor brochure", 
                           source=source,
                           franchisor_id=franchisor_id)
                return "franchisors"
            
            # Default to documents table
            return "documents"
            
        except Exception as e:
            logger.warning("Failed to determine target table", 
                          source=source,
                          error=str(e))
            return "documents"
    
    def _find_matching_franchisor(self, source: str) -> Optional[str]:
        """Find franchisor with matching brochure_url"""
        try:
            with self.vector_store._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT id FROM franchisors 
                        WHERE brochure_url = %s 
                        AND is_active = true 
                        AND is_deleted = false
                    """, (source,))
                    
                    result = cur.fetchone()
                    return str(result['id']) if result else None
                    
        except Exception as e:
            logger.warning("Failed to find matching franchisor", error=str(e))
            return None
    
    def _process_file(self, file_path: Path, extract_charts: bool) -> FileProcessingResult:
        """Process file using appropriate handler"""
        # Use factory to process file
        result = self.file_handler_factory.process_file(file_path)
        
        # Extract charts if requested and available
        if extract_charts and result.success and result.images_extracted:
            try:
                for image_info in result.images_extracted:
                    if 'data' in image_info:
                        chart_analysis = self.chart_analyzer.analyze_chart(
                            image_info['data'],
                            context=f"Chart from {file_path.name}"
                        )
                        image_info['chart_analysis'] = chart_analysis
                        
                        # Add chart description to text content
                        if chart_analysis.get('description'):
                            result.text_content += f"\n\nChart Analysis: {chart_analysis['description']}"
                            
            except Exception as e:
                logger.warning("Chart extraction failed", error=str(e))
        
        return result
    
    def _translate_content(self, content: str) -> str:
        """Translate content to English using OpenAI"""
        try:
            # Simple language detection
            if self._is_likely_english(content):
                return content
            
            # Use OpenAI for translation
            response = self.embedding_service.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "Translate the following text to English. Maintain the original structure and meaning."
                    },
                    {
                        "role": "user",
                        "content": content[:4000]  # Limit for cost control
                    }
                ],
                max_tokens=2000,
                temperature=0.1
            )
            
            translated = response.choices[0].message.content
            logger.info("Content translated", 
                       original_length=len(content),
                       translated_length=len(translated))
            
            return translated
            
        except Exception as e:
            logger.warning("Translation failed, using original content", error=str(e))
            return content
    
    def _is_likely_english(self, text: str) -> bool:
        """Simple heuristic to detect if text is likely English"""
        english_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
        words = text.lower().split()[:100]  # Check first 100 words
        
        english_count = sum(1 for word in words if word in english_words)
        return english_count / len(words) > 0.1 if words else True
    
    def _create_chunks(self, content: str, metadata: DocumentMetadata) -> List[DocumentChunk]:
        """Create document chunks from content"""
        if not content:
            return []

        # Check cache first
        cache_key = f"chunks_{hashlib.sha256(content.encode()).hexdigest()[:16]}"
        cached_chunks = self.cache.get(cache_key)
        if cached_chunks:
            logger.debug("Using cached chunks", chunk_count=len(cached_chunks))

            # Convert cached dictionaries back to DocumentChunk objects if needed
            # This handles both memory and disk cache cases where chunks are stored as dicts
            if cached_chunks and isinstance(cached_chunks[0], dict):
                converted_chunks = []
                for chunk_data in cached_chunks:
                    chunk = DocumentChunk(
                        id=chunk_data['id'],
                        text=chunk_data['text'],
                        embedding=chunk_data.get('embedding'),
                        metadata=chunk_data.get('metadata', {})
                    )
                    converted_chunks.append(chunk)

                # Update cache with proper DocumentChunk objects to avoid future conversions
                self.cache.set(cache_key, converted_chunks, ttl=3600)
                return converted_chunks

            return cached_chunks

        # Use adaptive chunker for intelligent text processing
        document_type = metadata.file_type.lower().replace('.', '')
        adaptive_chunks = self.adaptive_chunker.chunk_text(content, document_type)

        chunks = []
        for i, chunk_data in enumerate(adaptive_chunks):
            chunk = DocumentChunk(
                id=chunk_data['id'],
                text=chunk_data['text'],
                metadata={
                    'document_id': metadata.document_id,
                    'chunk_index': i,
                    'filename': metadata.filename,
                    'file_type': metadata.file_type,
                    'source_url': metadata.source_url,
                    'content_type': chunk_data['metadata'].content_type,
                    'complexity_score': chunk_data['metadata'].complexity_score,
                    'token_count': chunk_data['metadata'].token_count,
                    'has_tables': chunk_data['metadata'].has_tables,
                    'has_lists': chunk_data['metadata'].has_lists,
                    'has_code': chunk_data['metadata'].has_code
                }
            )
            chunks.append(chunk)

        # Cache the chunks for future use
        self.cache.set(cache_key, chunks, ttl=3600)  # Cache for 1 hour

        logger.info("Adaptive chunks created",
                   chunk_count=len(chunks),
                   avg_complexity=sum(c.metadata.get('complexity_score', 0) for c in chunks) / len(chunks) if chunks else 0,
                   document_type=document_type)
        
        return chunks
    
    def _generate_embeddings(self, chunks: List[DocumentChunk]) -> bool:
        """Generate embeddings for chunks"""
        try:
            # Check cache for existing embeddings
            cached_embeddings = {}
            texts_to_embed = []
            chunk_indices = []

            for i, chunk in enumerate(chunks):
                cache_key = f"embedding_{hashlib.sha256(chunk.text.encode()).hexdigest()[:16]}"
                cached_embedding = self.cache.get(cache_key)

                if cached_embedding:
                    cached_embeddings[i] = cached_embedding
                    logger.debug("Using cached embedding", chunk_id=chunk.id)
                else:
                    texts_to_embed.append(chunk.text)
                    chunk_indices.append(i)

            # Generate embeddings for uncached texts
            new_embeddings = []
            if texts_to_embed:
                logger.info("Generating new embeddings",
                           total_chunks=len(chunks),
                           cached_count=len(cached_embeddings),
                           new_count=len(texts_to_embed))

                # Use optimized batch size from config
                batch_size = getattr(config, 'batch_size', 10)
                new_embeddings = self.embedding_service.generate_embeddings_batch(
                    texts_to_embed, batch_size=batch_size
                )

                if len(new_embeddings) != len(texts_to_embed):
                    logger.error("Embedding count mismatch",
                               texts=len(texts_to_embed),
                               embeddings=len(new_embeddings))
                    return False

                # Cache new embeddings
                for text, embedding in zip(texts_to_embed, new_embeddings):
                    cache_key = f"embedding_{hashlib.sha256(text.encode()).hexdigest()[:16]}"
                    self.cache.set(cache_key, embedding, ttl=7200)  # Cache for 2 hours

            # Assign all embeddings to chunks
            embedding_index = 0
            for i, chunk in enumerate(chunks):
                if i in cached_embeddings:
                    chunk.embedding = cached_embeddings[i]
                else:
                    chunk.embedding = new_embeddings[embedding_index]
                    embedding_index += 1

            logger.info("All embeddings assigned",
                       total_embeddings=len(chunks),
                       cached_count=len(cached_embeddings),
                       new_count=len(new_embeddings))
            return True
            
        except Exception as e:
            logger.error("Embedding generation failed", error=str(e))
            return False
    
    def _store_embeddings(
        self, 
        document_id: str, 
        chunks: List[DocumentChunk], 
        table_name: TableName,
        metadata: DocumentMetadata,
        source: str
    ) -> bool:
        """Store embeddings in appropriate table"""
        try:
            if table_name == "franchisors":
                return self._store_franchisor_embedding(document_id, chunks, source)
            else:
                return self._store_document_embedding(document_id, chunks, metadata)
                
        except Exception as e:
            logger.error("Embedding storage failed", error=str(e))
            return False
    
    def _store_document_embedding(
        self, 
        document_id: str, 
        chunks: List[DocumentChunk],
        metadata: DocumentMetadata
    ) -> bool:
        """Store document chunks in documents table"""
        return self.vector_store.upsert_document_embedding(
            document_id, 
            chunks, 
            metadata.__dict__
        )
    
    def _store_franchisor_embedding(
        self, 
        document_id: str, 
        chunks: List[DocumentChunk], 
        source: str
    ) -> bool:
        """Store franchisor embedding (combine all chunks)"""
        try:
            # Find the franchisor ID
            franchisor_id = self._find_matching_franchisor(source)
            if not franchisor_id:
                logger.error("No matching franchisor found", source=source)
                return False
            
            # Combine all chunk texts
            combined_text = "\n\n".join(chunk.text for chunk in chunks)
            
            # Generate single embedding for combined content
            combined_embedding = self.embedding_service.generate_embedding(combined_text)
            
            # Store in franchisors table
            return self.vector_store.upsert_franchisor_embedding(
                franchisor_id,
                combined_embedding,
                combined_text,
                {'document_id': document_id, 'source': source}
            )
            
        except Exception as e:
            logger.error("Franchisor embedding storage failed", error=str(e))
            return False
