"""
Production-Grade RAG Integration Module
Provides easy access to all production-grade components
"""

import structlog
from typing import Dict, List, Any, Optional

# Import production-grade components
from .text_processing.production_text_processor import (
    ProductionTextNormalizer,
    ProductionChunker,
    DocumentChunk
)
from .vector_store.production_embeddings import ProductionEmbeddingService
from .vector_store.production_vector_store import ProductionVectorStore, RetrievalResult
from .production_qa import ProductionQASystem

logger = structlog.get_logger()

class ProductionRAGSystem:
    """
    Production-Grade RAG System Integration
    
    This class provides a unified interface to all production-grade components:
    - Text normalization and chunking
    - Embedding generation
    - Vector storage and retrieval
    - Question answering
    
    Usage:
        rag_system = ProductionRAGSystem()
        
        # Process document
        chunks = rag_system.process_document(text_content)
        
        # Store document
        rag_system.store_document(franchisor_id, chunks)
        
        # Answer questions
        answer = await rag_system.answer_question(question, franchisor_id)
    """
    
    def __init__(self):
        self.text_normalizer = ProductionTextNormalizer()
        self.chunker = ProductionChunker(chunk_size=400, chunk_overlap=75)
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        self.qa_system = ProductionQASystem()
        
        logger.info("Production RAG System initialized")
    
    def process_document(
        self, 
        text_content: str, 
        metadata: Dict[str, Any] = None
    ) -> List[DocumentChunk]:
        """
        Process document text into chunks with embeddings
        
        Args:
            text_content: Raw document text
            metadata: Additional metadata
            
        Returns:
            List of document chunks with embeddings
        """
        # Normalize text
        normalized_text = self.text_normalizer.normalize_text(text_content)
        
        # Create chunks
        chunks = self.chunker.chunk_text(normalized_text, metadata)
        
        # Generate embeddings
        for chunk in chunks:
            chunk.embedding = self.embedding_service.generate_embedding(chunk.text)
        
        return chunks
    
    def store_document(
        self,
        franchisor_id: str,
        text_content: str,
        chunks: List[DocumentChunk] = None
    ) -> bool:
        """
        Store document in vector store
        
        Args:
            franchisor_id: Franchisor ID
            text_content: Full document text
            chunks: Optional pre-processed chunks
            
        Returns:
            True if successful
        """
        # Process document if chunks not provided
        if chunks is None:
            chunks = self.process_document(text_content)
        
        # Generate embedding for full text
        embedding = self.embedding_service.generate_embedding(text_content)
        
        # Store in vector store
        return self.vector_store.store_franchisor_embedding(
            franchisor_id=franchisor_id,
            text_content=text_content,
            embedding=embedding,
            metadata={"chunk_count": len(chunks)}
        )
    
    async def answer_question(
        self,
        question: str,
        franchisor_id: Optional[str] = None,
        similarity_threshold: float = 0.5,
        top_k: int = 5,
        temperature: float = 0.1
    ) -> Dict[str, Any]:
        """
        Answer question using production-grade RAG
        
        Args:
            question: User question
            franchisor_id: Optional franchisor filter
            similarity_threshold: Minimum similarity score
            top_k: Number of chunks to retrieve
            temperature: Generation temperature
            
        Returns:
            Dict with answer, sources, and metadata
        """
        return await self.qa_system.answer_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )

# Create singleton instance
production_rag = ProductionRAGSystem()
"""
Singleton instance of ProductionRAGSystem for easy import
"""
