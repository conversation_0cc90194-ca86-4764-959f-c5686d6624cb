#!/usr/bin/env python3
"""
Brochure-Optimized RAG System
Specialized for company brochure ingestion and question answering
"""

import asyncio
import sys
import json
import re
import time
import uuid
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path

# Add the project root to the path
sys.path.append('.')

# Import base production components
from production_rag_system import (
    DocumentChunk,
    ProductionEmbeddingService,
    ProductionVectorStore,
    RetrievalResult
)

@dataclass
class BrochureSection:
    """Represents a section in a company brochure"""
    title: str
    content: str
    section_type: str  # 'header', 'services', 'about', 'contact', 'features', 'benefits'
    page_number: Optional[int] = None
    order: Optional[int] = None

@dataclass
class BrochureMetadata:
    """Metadata extracted from company brochure"""
    company_name: Optional[str] = None
    industry: Optional[str] = None
    services: List[str] = field(default_factory=list)
    contact_info: Dict[str, str] = field(default_factory=dict)
    location: Optional[str] = None
    website: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    key_features: List[str] = field(default_factory=list)
    target_audience: Optional[str] = None

class BrochureTextNormalizer:
    """Specialized text normalization for company brochures"""
    
    @staticmethod
    def normalize_brochure_text(text: str) -> str:
        """
        Normalize brochure text while preserving marketing language
        
        - Preserve company names and proper nouns
        - Maintain service descriptions and benefits
        - Clean formatting while keeping structure
        - Preserve contact information formatting
        """
        if not text:
            return ""
        
        # Preserve original case for company names and proper nouns
        # but normalize general text
        
        # Remove excessive whitespace but preserve paragraph breaks
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        
        # Clean up bullet points and formatting
        text = re.sub(r'[•·▪▫◦‣⁃]\s*', '• ', text)
        text = re.sub(r'^\s*[-*]\s*', '• ', text, flags=re.MULTILINE)
        
        # Preserve phone numbers and emails
        text = re.sub(r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})', r'\1', text)
        text = re.sub(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', r'\1', text)
        
        # Clean up multiple punctuation but preserve emphasis
        text = re.sub(r'[!]{3,}', '!!', text)
        text = re.sub(r'[?]{3,}', '??', text)
        text = re.sub(r'[.]{3,}', '...', text)
        
        # Remove non-printable characters but preserve structure
        text = re.sub(r'[^\x20-\x7E\n\t]', '', text)
        
        return text.strip()
    
    @staticmethod
    def extract_sections(text: str) -> List[BrochureSection]:
        """Extract structured sections from brochure text"""
        sections = []
        
        # Common brochure section patterns
        section_patterns = {
            'header': [r'(?i)(company|about us|who we are|our company)', r'(?i)(welcome|introduction)'],
            'services': [r'(?i)(services|what we offer|our services|solutions)', r'(?i)(products|offerings)'],
            'about': [r'(?i)(about|company profile|our story|history)', r'(?i)(mission|vision|values)'],
            'contact': [r'(?i)(contact|reach us|get in touch|contact us)', r'(?i)(phone|email|address|location)'],
            'features': [r'(?i)(features|benefits|advantages|why choose)', r'(?i)(key features|highlights)'],
            'franchise': [r'(?i)(franchise|franchising|business opportunity)', r'(?i)(investment|fees|requirements)']
        }
        
        # Split text into potential sections
        lines = text.split('\n')
        current_section = None
        current_content = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Check if this line is a section header
            section_found = None
            for section_type, patterns in section_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line) and len(line) < 100:  # Headers are usually short
                        section_found = section_type
                        break
                if section_found:
                    break
            
            if section_found:
                # Save previous section
                if current_section and current_content:
                    sections.append(BrochureSection(
                        title=current_section,
                        content='\n'.join(current_content),
                        section_type=current_section,
                        order=len(sections)
                    ))
                
                # Start new section
                current_section = section_found
                current_content = [line]
            else:
                # Add to current section
                if current_section:
                    current_content.append(line)
                else:
                    # No section identified yet, treat as general content
                    if not sections:
                        sections.append(BrochureSection(
                            title="General Information",
                            content=line,
                            section_type="general",
                            order=0
                        ))
                    else:
                        sections[-1].content += '\n' + line
        
        # Add final section
        if current_section and current_content:
            sections.append(BrochureSection(
                title=current_section,
                content='\n'.join(current_content),
                section_type=current_section,
                order=len(sections)
            ))
        
        return sections

class BrochureMetadataExtractor:
    """Extract structured metadata from company brochures"""
    
    @staticmethod
    def extract_metadata(text: str, sections: List[BrochureSection] = None) -> BrochureMetadata:
        """Extract comprehensive metadata from brochure content"""
        metadata = BrochureMetadata()
        
        # Extract company name (usually in first few lines or headers)
        company_patterns = [
            r'(?i)^([A-Z][a-zA-Z\s&]+(?:Inc|LLC|Ltd|Corporation|Corp|Company|Co\.?))',
            r'(?i)(Welcome to|About)\s+([A-Z][a-zA-Z\s&]+)',
            r'(?i)([A-Z][a-zA-Z\s&]+)\s+(?:is|provides|offers|specializes)'
        ]
        
        for pattern in company_patterns:
            match = re.search(pattern, text[:500])  # Check first 500 chars
            if match:
                # Get the last group that exists
                groups = match.groups()
                if groups:
                    metadata.company_name = groups[-1].strip()
                    break
        
        # Extract contact information
        metadata.contact_info = {}
        
        # Phone numbers
        phone_match = re.search(r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})', text)
        if phone_match:
            metadata.phone = phone_match.group(1)
            metadata.contact_info['phone'] = phone_match.group(1)
        
        # Email addresses
        email_match = re.search(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', text)
        if email_match:
            metadata.email = email_match.group(1)
            metadata.contact_info['email'] = email_match.group(1)
        
        # Website
        website_match = re.search(r'(www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|https?://[a-zA-Z0-9.-]+)', text)
        if website_match:
            metadata.website = website_match.group(1)
            metadata.contact_info['website'] = website_match.group(1)
        
        # Location/Address
        location_patterns = [
            r'(?i)(?:located|based|address).*?([A-Z][a-zA-Z\s,]+(?:Australia|USA|Canada|UK))',
            r'(?i)([A-Z][a-zA-Z\s,]+(?:Australia|USA|Canada|UK))',
            r'(?i)(?:in|at)\s+([A-Z][a-zA-Z\s,]+)'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, text)
            if match:
                groups = match.groups()
                if groups:
                    metadata.location = groups[0].strip()
                    break
        
        # Extract services from sections
        if sections:
            services_section = next((s for s in sections if s.section_type == 'services'), None)
            if services_section:
                # Extract bullet points and service descriptions
                service_lines = re.findall(r'[•·▪▫◦‣⁃]\s*([^•·▪▫◦‣⁃\n]+)', services_section.content)
                metadata.services = [s.strip() for s in service_lines if len(s.strip()) > 5]
        
        # Extract key features
        feature_keywords = ['feature', 'benefit', 'advantage', 'why choose', 'specializ']
        metadata.key_features = []
        
        for keyword in feature_keywords:
            pattern = rf'(?i){keyword}[^.]*?([^.]+\.)'
            matches = re.findall(pattern, text)
            metadata.key_features.extend([m.strip() for m in matches])
        
        # Determine industry/type
        industry_keywords = {
            'franchise': ['franchise', 'franchising', 'business opportunity'],
            'cleaning': ['cleaning', 'maintenance', 'janitorial'],
            'food': ['restaurant', 'food', 'dining', 'cafe'],
            'retail': ['retail', 'store', 'shop', 'merchandise'],
            'service': ['service', 'consulting', 'professional'],
            'healthcare': ['health', 'medical', 'wellness', 'care'],
            'education': ['education', 'training', 'learning', 'school']
        }
        
        text_lower = text.lower()
        for industry, keywords in industry_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                metadata.industry = industry
                break
        
        return metadata

class BrochureChunker:
    """Specialized chunking for company brochures"""
    
    def __init__(self, chunk_size: int = 350, chunk_overlap: int = 50):
        """
        Initialize brochure chunker with optimized settings
        
        Args:
            chunk_size: Smaller chunks for focused brochure content
            chunk_overlap: Smaller overlap to avoid repetition
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # Brochure-specific separators (preserve structure)
        self.separators = [
            "\n\n### ",  # Section headers
            "\n\n## ",   # Main headers
            "\n\n# ",    # Title headers
            "\n\n",      # Paragraph breaks
            "\n• ",      # Bullet points
            "\n- ",      # Dash points
            ". ",        # Sentences
            "! ",        # Exclamations
            "? ",        # Questions
            "; ",        # Semicolons
            ": ",        # Colons
            " ",         # Words
            ""           # Characters
        ]
    
    def chunk_brochure(
        self, 
        text: str, 
        sections: List[BrochureSection] = None,
        metadata: BrochureMetadata = None
    ) -> List[DocumentChunk]:
        """
        Chunk brochure content with section awareness
        
        Args:
            text: Full brochure text
            sections: Extracted brochure sections
            metadata: Brochure metadata
            
        Returns:
            List of DocumentChunk objects optimized for brochures
        """
        chunks = []
        
        if sections:
            # Process each section separately to maintain context
            for section in sections:
                section_chunks = self._chunk_section(section, metadata)
                chunks.extend(section_chunks)
        else:
            # Fallback to general chunking
            general_chunks = self._chunk_text(text, metadata)
            chunks.extend(general_chunks)
        
        return chunks
    
    def _chunk_section(self, section: BrochureSection, metadata: BrochureMetadata = None) -> List[DocumentChunk]:
        """Chunk a specific brochure section"""
        section_text = f"{section.title}\n\n{section.content}"
        
        # Use recursive splitting
        text_chunks = self._recursive_split(section_text, self.chunk_size)
        
        document_chunks = []
        for i, chunk_text in enumerate(text_chunks):
            if len(chunk_text.strip()) < 10:  # Skip very short chunks
                continue
            
            chunk_metadata = {
                'section_type': section.section_type,
                'section_title': section.title,
                'section_order': section.order,
                'chunk_index': i,
                'chunk_method': 'brochure_section',
                'company_name': metadata.company_name if metadata else None,
                'industry': metadata.industry if metadata else None
            }
            
            chunk = DocumentChunk(
                id=str(uuid.uuid4()),
                text=chunk_text,
                embedding=[],  # Will be populated later
                metadata=chunk_metadata,
                token_count=self._estimate_tokens(chunk_text)
            )
            document_chunks.append(chunk)
        
        return document_chunks
    
    def _chunk_text(self, text: str, metadata: BrochureMetadata = None) -> List[DocumentChunk]:
        """Fallback general text chunking"""
        text_chunks = self._recursive_split(text, self.chunk_size)
        
        document_chunks = []
        for i, chunk_text in enumerate(text_chunks):
            if len(chunk_text.strip()) < 10:
                continue
            
            chunk_metadata = {
                'section_type': 'general',
                'chunk_index': i,
                'chunk_method': 'brochure_general',
                'company_name': metadata.company_name if metadata else None,
                'industry': metadata.industry if metadata else None
            }
            
            chunk = DocumentChunk(
                id=str(uuid.uuid4()),
                text=chunk_text,
                embedding=[],
                metadata=chunk_metadata,
                token_count=self._estimate_tokens(chunk_text)
            )
            document_chunks.append(chunk)
        
        return document_chunks
    
    def _recursive_split(self, text: str, chunk_size: int, depth: int = 0) -> List[str]:
        """Recursively split text using brochure-specific separators"""
        if depth > 8 or len(text) <= chunk_size:
            return [text] if text.strip() else []
        
        for separator in self.separators:
            if separator in text:
                splits = text.split(separator)
                if len(splits) > 1:
                    chunks = []
                    current_chunk = ""
                    
                    for split in splits:
                        if separator and split:
                            split = split + separator
                        
                        if len(current_chunk) + len(split) <= chunk_size:
                            current_chunk += split
                        else:
                            if current_chunk:
                                chunks.append(current_chunk.strip())
                            
                            if len(split) > chunk_size:
                                chunks.extend(self._recursive_split(split, chunk_size, depth + 1))
                                current_chunk = ""
                            else:
                                current_chunk = split
                    
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    
                    return [chunk for chunk in chunks if chunk.strip()]
        
        # Fallback to character splitting
        chunks = []
        for i in range(0, len(text), chunk_size - self.chunk_overlap):
            chunk = text[i:i + chunk_size]
            if chunk.strip():
                chunks.append(chunk.strip())
        
        return chunks
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count for brochure text"""
        # Brochure text tends to be more dense, so adjust estimation
        return len(text) // 3.5  # Slightly higher token density for marketing content

async def main():
    """Demonstrate brochure-optimized RAG system"""
    print("🚀 Brochure-Optimized RAG System")
    print("=" * 50)
    
    # Initialize components
    normalizer = BrochureTextNormalizer()
    extractor = BrochureMetadataExtractor()
    chunker = BrochureChunker(chunk_size=350, chunk_overlap=50)
    
    print("✅ Brochure-optimized components initialized")
    
    return {
        'normalizer': normalizer,
        'extractor': extractor,
        'chunker': chunker
    }

if __name__ == "__main__":
    asyncio.run(main())
