"""
Central ask_question() API with Redis Caching and Background Processing
High-performance document Q&A with intelligent routing and caching
"""

import asyncio
import uuid
from typing import Dict, Any, Optional
from datetime import datetime
import structlog

from .redis_store import get_redis_store, JobStatus
from .tasks import ingest_document_parallel
from .vector_store import PgVectorStore
from .serve import ask_question as original_ask_question

logger = structlog.get_logger()

# Franchisor detection removed - using hardcoded Coochie Hydrogreen
FRANCHISOR_DETECTION_AVAILABLE = False
COOCHIE_HYDROGREEN_ID = "569976f2-d845-4615-8a91-96e18086adbe"


class DocumentQAService:
    """Central service for document Q&A with caching and background processing"""
    
    def __init__(self):
        self.redis_store = None
        self.vector_store = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize service connections"""
        if not self._initialized:
            self.redis_store = await get_redis_store()
            self.vector_store = PgVectorStore()
            # await self.vector_store.connect()  # Removed: PgVectorStore has no connect method
            self._initialized = True
    
    async def ask_question(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Central ask_question() function with intelligent caching and routing

        Args:
            request: {
                "question": str,
                "source_url": Optional[str],  # Document URL for processing
                "document_id": Optional[str], # Specific document ID
                "franchisor_id": Optional[str], # Specific franchisor ID
                "top_k": Optional[int] = 5,
                "similarity_threshold": Optional[float] = 0.1,
                "force_refresh": Optional[bool] = False,
                "temperature": Optional[float] = 0.7,  # Controls randomness in generation
                "max_tokens": Optional[int] = 500,  # Maximum tokens in response
                "processing_options": Optional[Dict] = {}
            }

        Returns:
            Dict with answer and metadata
        """
        await self.initialize()

        question = request.get("question", "").strip()
        source_url = request.get("source_url")
        document_id = request.get("document_id")
        franchisor_id = request.get("franchisor_id")
        top_k = request.get("top_k", 5)
        similarity_threshold = request.get("similarity_threshold", 0.1)
        force_refresh = request.get("force_refresh", False)
        temperature = request.get("temperature", 0.7)
        max_tokens = request.get("max_tokens", 500)
        processing_options = request.get("processing_options", {})
        
        if not question:
            return {
                "success": False,
                "error": "Question is required",
                "answer": "Please provide a valid question."
            }
        
        try:
            # Step 1: Check for cached answer (unless force_refresh)
            if not force_refresh:
                cached_answer = await self._get_cached_answer(
                    question, document_id, franchisor_id
                )
                if cached_answer:
                    return cached_answer

            # Step 2: Use hardcoded Coochie Hydrogreen if no franchisor specified
            detected_franchisor_id = None
            detection_confidence = 1.0

            if not franchisor_id:
                franchisor_id = COOCHIE_HYDROGREEN_ID
                detected_franchisor_id = COOCHIE_HYDROGREEN_ID
                logger.info(
                    "Using hardcoded Coochie Hydrogreen franchisor",
                    franchisor_id=franchisor_id,
                    question=question[:100]
                )

            # Step 3: Handle document processing if source_url provided
            if source_url:
                processing_result = await self._handle_document_processing(
                    source_url, processing_options
                )

                # If document is still processing, return placeholder
                if not processing_result["ready"]:
                    return {
                        "success": True,
                        "answer": "Document is being processed. Please check back shortly.",
                        "processing_status": processing_result["status"],
                        "job_id": processing_result.get("job_id"),
                        "estimated_completion": processing_result.get("estimated_completion"),
                        "cached": False
                    }

            # Step 4: Query vector store with priority routing
            answer_result = await self._query_vector_store(
                question=question,
                document_id=document_id,
                franchisor_id=franchisor_id,
                top_k=top_k,
                similarity_threshold=similarity_threshold,
                temperature=temperature,
                max_tokens=max_tokens
            )

            # Step 5: Add Coochie Hydrogreen metadata to the result
            if "metadata" not in answer_result:
                answer_result["metadata"] = {}

            answer_result["metadata"]["franchisor_info"] = {
                "franchisor_id": franchisor_id,
                "franchisor_name": "Coochie Hydrogreen",
                "detection_method": "hardcoded_coochie_hydrogreen",
                "original_franchisor_id": request.get("franchisor_id"),
                "final_franchisor_id": franchisor_id
            }
            
            # Step 4: Cache the answer
            if answer_result["success"]:
                await self._cache_answer(
                    question, document_id, franchisor_id, answer_result
                )
            
            return answer_result
            
        except Exception as e:
            logger.error(f"Error in ask_question: {e}")
            return {
                "success": False,
                "error": str(e),
                "answer": "An error occurred while processing your question. Please try again."
            }
    
    async def _get_cached_answer(
        self, 
        question: str, 
        document_id: Optional[str], 
        franchisor_id: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """Check for cached answer"""
        try:
            # Try different cache keys based on context
            cache_keys = []
            
            if franchisor_id:
                cache_keys.append(f"franchisor:{franchisor_id}")
            if document_id:
                cache_keys.append(f"document:{document_id}")
            
            # Default to general cache
            if not cache_keys:
                cache_keys.append("general")
            
            for cache_key in cache_keys:
                cached = await self.redis_store.get_cached_answer(question, cache_key)
                if cached:
                    logger.info(f"Cache hit for question with key: {cache_key}")
                    result = cached["answer"]
                    result["cached"] = True
                    result["cache_key"] = cache_key
                    result["cached_at"] = cached["cached_at"]
                    return result
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking cache: {e}")
            return None
    
    async def _handle_document_processing(
        self, 
        source_url: str, 
        processing_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle document processing with status tracking"""
        try:
            # Check if document is already processed
            if await self.redis_store.is_document_processed(source_url):
                return {
                    "ready": True,
                    "status": "completed",
                    "message": "Document already processed"
                }
            
            # Check current job status
            job_status = await self.redis_store.get_job_status(source_url)
            
            if job_status:
                status = job_status["status"]
                
                if status == JobStatus.COMPLETED.value:
                    return {
                        "ready": True,
                        "status": "completed",
                        "message": "Document processing completed"
                    }
                elif status == JobStatus.PROCESSING.value:
                    # Get progress information
                    job_id = job_status.get("job_id")
                    progress_info = None
                    
                    if job_id:
                        progress_info = await self.redis_store.get_job_progress(job_id)
                    
                    return {
                        "ready": False,
                        "status": "processing",
                        "job_id": job_id,
                        "progress": progress_info.get("progress", 0) if progress_info else 0,
                        "message": progress_info.get("message", "Processing...") if progress_info else "Processing...",
                        "estimated_completion": self._estimate_completion_time(progress_info)
                    }
                elif status == JobStatus.FAILED.value:
                    # Retry failed job
                    logger.info(f"Retrying failed document processing: {source_url}")
                    await self._trigger_document_processing(source_url, processing_options)
                    
                    return {
                        "ready": False,
                        "status": "retrying",
                        "message": "Retrying document processing..."
                    }
            
            # No existing job, trigger new processing
            await self._trigger_document_processing(source_url, processing_options)
            
            return {
                "ready": False,
                "status": "initiated",
                "message": "Document processing initiated..."
            }
            
        except Exception as e:
            logger.error(f"Error handling document processing: {e}")
            return {
                "ready": False,
                "status": "error",
                "message": f"Error initiating processing: {str(e)}"
            }
    
    async def _trigger_document_processing(
        self, 
        source_url: str, 
        processing_options: Dict[str, Any]
    ):
        """Trigger background document processing"""
        try:
            # Set initial status
            job_id = str(uuid.uuid4())
            
            await self.redis_store.set_job_status(
                url=source_url,
                status=JobStatus.PENDING,
                job_id=job_id,
                metadata={
                    "initiated_at": datetime.utcnow().isoformat(),
                    "options": processing_options
                }
            )
            
            # Trigger Celery task
            task_result = ingest_document_parallel.delay(
                source_url=source_url,
                **processing_options
            )
            
            # Update with actual task ID
            await self.redis_store.set_job_status(
                url=source_url,
                status=JobStatus.PENDING,
                job_id=task_result.id,
                metadata={
                    "initiated_at": datetime.utcnow().isoformat(),
                    "celery_task_id": task_result.id,
                    "options": processing_options
                }
            )
            
            logger.info(f"Triggered document processing: {source_url}, task_id: {task_result.id}")
            
        except Exception as e:
            logger.error(f"Error triggering document processing: {e}")
            raise
    
    async def _query_vector_store(
        self,
        question: str,
        document_id: Optional[str],
        franchisor_id: Optional[str],
        top_k: int,
        similarity_threshold: float,
        temperature: float = 0.7,
        max_tokens: int = 500
    ) -> Dict[str, Any]:
        """Query vector store with priority routing"""
        try:
            # Use existing ask_question function with enhancements
            result = original_ask_question(
                question=question,
                top_k=top_k,
                similarity_threshold=similarity_threshold,
                include_metadata=True,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # Add context information
            context_info = {
                "query_type": "general",
                "sources_searched": ["franchisors", "documents"],
                "similarity_threshold": similarity_threshold,
                "top_k": top_k
            }
            
            if franchisor_id:
                context_info["franchisor_id"] = franchisor_id
                context_info["query_type"] = "franchisor_specific"
            
            if document_id:
                context_info["document_id"] = document_id
                context_info["query_type"] = "document_specific"
            
            return {
                "success": True,
                "answer": result,
                "context": context_info,
                "cached": False,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error querying vector store: {e}")
            return {
                "success": False,
                "error": str(e),
                "answer": "Unable to process your question at this time. Please try again later."
            }
    
    async def _cache_answer(
        self,
        question: str,
        document_id: Optional[str],
        franchisor_id: Optional[str],
        answer_result: Dict[str, Any]
    ):
        """Cache the answer with appropriate key"""
        try:
            # Determine cache key
            if franchisor_id:
                cache_key = f"franchisor:{franchisor_id}"
            elif document_id:
                cache_key = f"document:{document_id}"
            else:
                cache_key = "general"
            
            # Cache with appropriate TTL
            ttl = 7200  # 2 hours default
            
            if franchisor_id:
                ttl = 14400  # 4 hours for franchisor-specific
            elif document_id:
                ttl = 10800  # 3 hours for document-specific
            
            await self.redis_store.cache_answer(
                question=question,
                doc_id=cache_key,
                answer=answer_result,
                ttl=ttl
            )
            
            logger.info(f"Cached answer with key: {cache_key}, TTL: {ttl}")
            
        except Exception as e:
            logger.error(f"Error caching answer: {e}")
    
    def _estimate_completion_time(self, progress_info: Optional[Dict[str, Any]]) -> Optional[str]:
        """Estimate completion time based on progress"""
        if not progress_info:
            return None
        
        progress = progress_info.get("progress", 0)
        if progress <= 0:
            return "Estimating..."
        
        # Simple estimation based on progress
        if progress < 30:
            return "~5-10 minutes"
        elif progress < 70:
            return "~2-5 minutes"
        elif progress < 90:
            return "~1-2 minutes"
        else:
            return "~30 seconds"


# Global service instance
_qa_service: Optional[DocumentQAService] = None


async def ask_question(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Central ask_question() function for external use
    
    This is the main entry point for the enhanced DocQA system with:
    - Redis caching for fast repeated queries
    - Background document processing via RabbitMQ
    - Intelligent routing between franchisors and documents
    - Real-time status tracking
    
    Args:
        request: Question request dictionary
        
    Returns:
        Answer response dictionary
    """
    global _qa_service
    
    if _qa_service is None:
        _qa_service = DocumentQAService()
    
    return await _qa_service.ask_question(request)


# Synchronous wrapper for backward compatibility
def ask_question_sync(request: Dict[str, Any]) -> Dict[str, Any]:
    """Synchronous wrapper for ask_question"""
    return asyncio.run(ask_question(request))
