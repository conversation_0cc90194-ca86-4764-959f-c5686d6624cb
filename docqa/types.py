"""
Type definitions for DocQA system
"""

from typing import Dict, List, Optional, Any, Literal
from dataclasses import dataclass
from datetime import datetime
import uuid


@dataclass
class DocumentChunk:
    """Represents a chunk of document content with metadata"""
    id: str
    text: str
    embedding: Optional[List[float]] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if not self.id:
            self.id = str(uuid.uuid4())


@dataclass
class DocumentMetadata:
    """Metadata for a document"""
    document_id: str
    filename: str
    file_type: str
    file_size: int
    page_count: Optional[int] = None
    language: Optional[str] = None
    processed_at: datetime = None
    source_url: Optional[str] = None
    
    def __post_init__(self):
        if self.processed_at is None:
            self.processed_at = datetime.utcnow()


@dataclass
class SearchResult:
    """Result from vector search"""
    chunk_id: str
    text: str
    similarity_score: float
    metadata: Dict[str, Any]
    table_name: str  # 'documents' or 'franchisors'


@dataclass
class IngestionResult:
    """Result from document ingestion"""
    success: bool
    document_id: str
    chunks_created: int
    table_name: str
    error_message: Optional[str] = None
    processing_time: Optional[float] = None


@dataclass
class FileProcessingResult:
    """Result from file processing"""
    success: bool
    text_content: str
    images_extracted: List[str] = None
    tables_extracted: List[Dict] = None
    charts_extracted: List[Dict] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.images_extracted is None:
            self.images_extracted = []
        if self.tables_extracted is None:
            self.tables_extracted = []
        if self.charts_extracted is None:
            self.charts_extracted = []


@dataclass
class RAGResponse:
    """Response from RAG system"""
    answer: str
    sources: List[SearchResult]
    processing_time: float
    model_used: str
    tokens_used: Optional[int] = None


# Type aliases
FileFormat = Literal[".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png"]
TableName = Literal["documents", "franchisors"]
ProcessingStatus = Literal["pending", "processing", "completed", "failed"]


class DocQAException(Exception):
    """Base exception for DocQA system"""
    pass


class FileProcessingError(DocQAException):
    """Error during file processing"""
    pass


class VectorStoreError(DocQAException):
    """Error in vector store operations"""
    pass


class EmbeddingError(DocQAException):
    """Error during embedding generation"""
    pass


class DatabaseError(DocQAException):
    """Database operation error"""
    pass
