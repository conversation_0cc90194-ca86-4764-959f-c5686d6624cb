"""
Question answering with priority-based retrieval
"""

import time
from typing import List, Dict, Any, Optional
import openai
import structlog

from .config import get_config
from .types import SearchResult, RAGResponse
from .vector_store import EmbeddingService, PgVectorStore

logger = structlog.get_logger()


class QuestionAnsweringService:
    """Service for answering questions with priority-based retrieval"""
    
    def __init__(self):
        self.embedding_service = EmbeddingService()
        self.vector_store = PgVectorStore()
        config = get_config()
        self.openai_client = openai.OpenAI(api_key=config.openai_api_key)

        self.chat_model = config.chat_model
        self.max_tokens = config.max_tokens
        self.temperature = config.temperature
        
        logger.info("Question answering service initialized")
    
    def ask_question(
        self,
        question: str,
        top_k: Optional[int] = None,
        similarity_threshold: Optional[float] = None,
        include_sources: bool = True,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> RAGResponse:
        """
        Answer a question using RAG with priority-based retrieval
        
        Args:
            question: User question
            top_k: Number of results to retrieve
            similarity_threshold: Minimum similarity score
            include_sources: Whether to include source information
            
        Returns:
            RAGResponse with answer and sources
        """
        start_time = time.time()
        
        try:
            logger.info("Processing question", question=question[:100])
            
            # Generate question embedding
            question_embedding = self.embedding_service.generate_embedding(question)
            
            # Search with priority: franchisors first, then documents
            config = get_config()
            search_results = self.vector_store.search_similar(
                query_embedding=question_embedding,
                top_k=top_k or config.top_k,
                similarity_threshold=similarity_threshold or config.similarity_threshold,
                table_priority=['franchisors', 'documents']
            )
            
            if not search_results:
                return RAGResponse(
                    answer="I couldn't find relevant information to answer your question. Please try rephrasing or asking about a different topic.",
                    sources=[],
                    processing_time=time.time() - start_time,
                    model_used=self.chat_model
                )
            
            # Generate answer using RAG
            answer = self._generate_answer(question, search_results, temperature, max_tokens)
            
            processing_time = time.time() - start_time
            
            # Prepare response
            response = RAGResponse(
                answer=answer,
                sources=search_results if include_sources else [],
                processing_time=processing_time,
                model_used=self.chat_model
            )
            
            logger.info("Question answered",
                       question_length=len(question),
                       answer_length=len(answer),
                       sources_count=len(search_results),
                       processing_time=processing_time)
            
            return response
            
        except Exception as e:
            logger.error("Question answering failed", 
                        question=question[:100],
                        error=str(e))
            
            return RAGResponse(
                answer=f"I encountered an error while processing your question: {str(e)}",
                sources=[],
                processing_time=time.time() - start_time,
                model_used=self.chat_model
            )
    
    def _generate_answer(self, question: str, search_results: List[SearchResult], temperature: Optional[float] = None, max_tokens: Optional[int] = None) -> str:
        """Generate answer using OpenAI with retrieved context"""
        try:
            # Prepare context from search results
            context = self._prepare_context(search_results)
            
            # Create system prompt
            system_prompt = self._create_system_prompt()
            
            # Create user prompt with context
            user_prompt = self._create_user_prompt(question, context, search_results)
            
            # Call OpenAI
            response = self.openai_client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
                temperature=temperature if temperature is not None else self.temperature,
                stream=False
            )
            
            answer = response.choices[0].message.content
            
            # Log token usage if available
            if response.usage:
                logger.debug("OpenAI usage",
                           prompt_tokens=response.usage.prompt_tokens,
                           completion_tokens=response.usage.completion_tokens,
                           total_tokens=response.usage.total_tokens)
            
            return answer.strip()
            
        except Exception as e:
            logger.error("Answer generation failed", error=str(e))
            return "I apologize, but I encountered an error while generating the answer. Please try again."
    
    def _prepare_context(self, search_results: List[SearchResult]) -> str:
        """Prepare context from search results"""
        context_parts = []
        
        # Group results by table for better organization
        franchisor_results = [r for r in search_results if r.table_name == 'franchisors']
        document_results = [r for r in search_results if r.table_name == 'documents']
        
        # Add franchisor information first (higher priority)
        if franchisor_results:
            context_parts.append("=== FRANCHISOR INFORMATION ===")
            for i, result in enumerate(franchisor_results, 1):
                franchisor_info = self._format_franchisor_context(result, i)
                context_parts.append(franchisor_info)
        
        # Add document information
        if document_results:
            context_parts.append("\n=== DOCUMENT INFORMATION ===")
            for i, result in enumerate(document_results, 1):
                doc_info = self._format_document_context(result, i)
                context_parts.append(doc_info)
        
        return "\n\n".join(context_parts)
    
    def _format_franchisor_context(self, result: SearchResult, index: int) -> str:
        """Format franchisor search result for context"""
        metadata = result.metadata
        
        context = f"[FRANCHISOR {index}]"
        context += f"\nFranchisor: {metadata.get('name', 'Unknown')}"
        
        if metadata.get('region'):
            context += f"\nRegion: {metadata['region']}"
        
        if metadata.get('brochure_url'):
            context += f"\nBrochure: {metadata['brochure_url']}"
        
        context += f"\nRelevance Score: {result.similarity_score:.3f}"
        context += f"\nContent: {result.text}"
        
        return context
    
    def _format_document_context(self, result: SearchResult, index: int) -> str:
        """Format document search result for context"""
        metadata = result.metadata
        
        context = f"[DOCUMENT {index}]"
        
        if metadata.get('document_name'):
            context += f"\nDocument: {metadata['document_name']}"
        
        if metadata.get('file_type'):
            context += f"\nType: {metadata['file_type']}"
        
        context += f"\nRelevance Score: {result.similarity_score:.3f}"
        context += f"\nContent: {result.text}"
        
        return context
    
    def _create_system_prompt(self) -> str:
        """Create system prompt for the AI assistant"""
        return """You are a helpful AI assistant that answers questions based on provided context information. 

IMPORTANT GUIDELINES:
1. PRIORITIZE FRANCHISOR INFORMATION: If the context includes franchisor information, prioritize it over document information when answering questions.
2. Be accurate and only use information from the provided context.
3. If the context doesn't contain enough information to answer the question, say so clearly.
4. When referencing franchisors, include their name and region when available.
5. Provide specific details from the context when relevant.
6. If multiple franchisors are mentioned, distinguish between them clearly.
7. Be concise but comprehensive in your answers.

Remember: Franchisor information is more authoritative and should be prioritized over general document content."""
    
    def _create_user_prompt(self, question: str, context: str, search_results: List[SearchResult]) -> str:
        """Create user prompt with question and context"""
        # Count sources by type
        franchisor_count = len([r for r in search_results if r.table_name == 'franchisors'])
        document_count = len([r for r in search_results if r.table_name == 'documents'])
        
        prompt = f"Question: {question}\n\n"
        prompt += f"Context Information ({franchisor_count} franchisor(s), {document_count} document(s)):\n"
        prompt += context
        prompt += "\n\nPlease answer the question based on the provided context. "
        
        if franchisor_count > 0:
            prompt += "Pay special attention to franchisor information as it has higher priority."
        
        return prompt
    
    def get_conversation_context(self, question: str, top_k: int = 3) -> Dict[str, Any]:
        """Get context for a question without generating an answer"""
        try:
            question_embedding = self.embedding_service.generate_embedding(question)
            
            search_results = self.vector_store.search_similar(
                query_embedding=question_embedding,
                top_k=top_k,
                table_priority=['franchisors', 'documents']
            )
            
            return {
                'question': question,
                'results_count': len(search_results),
                'franchisor_results': [r for r in search_results if r.table_name == 'franchisors'],
                'document_results': [r for r in search_results if r.table_name == 'documents'],
                'context': self._prepare_context(search_results)
            }
            
        except Exception as e:
            logger.error("Failed to get conversation context", error=str(e))
            return {'error': str(e)}
    
    def stream_answer(
        self, 
        question: str, 
        top_k: Optional[int] = None,
        similarity_threshold: Optional[float] = None
    ):
        """Stream answer generation for real-time responses"""
        try:
            # Generate question embedding
            question_embedding = self.embedding_service.generate_embedding(question)
            
            # Search for relevant content
            config = get_config()
            search_results = self.vector_store.search_similar(
                query_embedding=question_embedding,
                top_k=top_k or config.top_k,
                similarity_threshold=similarity_threshold or config.similarity_threshold,
                table_priority=['franchisors', 'documents']
            )
            
            if not search_results:
                yield "I couldn't find relevant information to answer your question."
                return
            
            # Prepare context
            context = self._prepare_context(search_results)
            system_prompt = self._create_system_prompt()
            user_prompt = self._create_user_prompt(question, context, search_results)
            
            # Stream response from OpenAI
            response = self.openai_client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                stream=True
            )
            
            for chunk in response:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error("Streaming answer failed", error=str(e))
            yield f"Error: {str(e)}"
