"""
Brochure-Optimized RAG Integration Module
Provides easy access to all brochure-optimized components
"""

import structlog
from typing import Dict, List, Any, Optional

# Import brochure-optimized components
from .brochure_rag_system import (
    BrochureTextNormalizer,
    BrochureMetadataExtractor,
    BrochureChunker,
    BrochureSection,
    BrochureMetadata,
    DocumentChunk
)
from .brochure_qa_system import BrochureQASystem
# Import from the main production RAG system
import sys
sys.path.append('.')
from production_rag_system import (
    ProductionEmbeddingService,
    ProductionVectorStore,
    RetrievalResult
)

logger = structlog.get_logger()

class BrochureRAGSystem:
    """
    Brochure-Optimized RAG System Integration
    
    This class provides a unified interface to all brochure-optimized components:
    - Brochure-specific text normalization and section extraction
    - Metadata extraction for company information
    - Optimized chunking for marketing content
    - Enhanced question answering for brochure queries
    
    Usage:
        rag_system = BrochureRAGSystem()
        
        # Process brochure
        sections, metadata = rag_system.extract_brochure_structure(text)
        chunks = rag_system.process_brochure(text, sections, metadata)
        
        # Store brochure
        rag_system.store_brochure(franchisor_id, text, chunks, metadata)
        
        # Answer questions
        answer = await rag_system.answer_brochure_question(question, franchisor_id)
    """
    
    def __init__(self):
        self.text_normalizer = BrochureTextNormalizer()
        self.metadata_extractor = BrochureMetadataExtractor()
        self.chunker = BrochureChunker(chunk_size=350, chunk_overlap=50)
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        self.qa_system = BrochureQASystem()
        
        logger.info("Brochure RAG System initialized")
    
    def extract_brochure_structure(self, text: str) -> tuple[List[BrochureSection], BrochureMetadata]:
        """
        Extract structured information from brochure text
        
        Args:
            text: Raw brochure text
            
        Returns:
            Tuple of (sections, metadata)
        """
        # Normalize text
        normalized_text = self.text_normalizer.normalize_brochure_text(text)
        
        # Extract sections
        sections = self.text_normalizer.extract_sections(normalized_text)
        
        # Extract metadata
        metadata = self.metadata_extractor.extract_metadata(normalized_text, sections)
        
        return sections, metadata
    
    def process_brochure(
        self, 
        text: str, 
        sections: List[BrochureSection] = None,
        metadata: BrochureMetadata = None
    ) -> List[DocumentChunk]:
        """
        Process brochure text into optimized chunks
        
        Args:
            text: Brochure text content
            sections: Optional pre-extracted sections
            metadata: Optional pre-extracted metadata
            
        Returns:
            List of document chunks with embeddings
        """
        # Extract structure if not provided
        if sections is None or metadata is None:
            sections, metadata = self.extract_brochure_structure(text)
        
        # Create chunks
        chunks = self.chunker.chunk_brochure(text, sections, metadata)
        
        # Generate embeddings
        for chunk in chunks:
            chunk.embedding = self.embedding_service.generate_embedding(chunk.text)
        
        return chunks
    
    def store_brochure(
        self,
        franchisor_id: str,
        text: str,
        chunks: List[DocumentChunk] = None,
        metadata: BrochureMetadata = None
    ) -> bool:
        """
        Store brochure in vector store with enhanced metadata
        
        Args:
            franchisor_id: Franchisor ID
            text: Full brochure text
            chunks: Optional pre-processed chunks
            metadata: Optional brochure metadata
            
        Returns:
            True if successful
        """
        # Process brochure if chunks not provided
        if chunks is None:
            sections, extracted_metadata = self.extract_brochure_structure(text)
            chunks = self.process_brochure(text, sections, extracted_metadata)
            if metadata is None:
                metadata = extracted_metadata
        
        # Create comprehensive brochure summary
        brochure_summary = self._create_brochure_summary(text, metadata)
        
        # Generate embedding for full brochure
        embedding = self.embedding_service.generate_embedding(brochure_summary)
        
        # Enhanced metadata for storage
        storage_metadata = {
            'content_type': 'company_brochure',
            'company_name': metadata.company_name if metadata else None,
            'industry': metadata.industry if metadata else None,
            'location': metadata.location if metadata else None,
            'services_count': len(metadata.services) if metadata and metadata.services else 0,
            'contact_info': metadata.contact_info if metadata else {},
            'chunk_count': len(chunks),
            'text_length': len(text)
        }
        
        # Store in vector store
        return self.vector_store.store_franchisor_embedding(
            franchisor_id=franchisor_id,
            text_content=brochure_summary,
            embedding=embedding,
            metadata=storage_metadata
        )
    
    async def answer_brochure_question(
        self,
        question: str,
        franchisor_id: Optional[str] = None,
        similarity_threshold: float = 0.4,  # Lower for brochures
        top_k: int = 5,
        temperature: float = 0.2  # Balanced for marketing content
    ) -> Dict[str, Any]:
        """
        Answer question using brochure-optimized QA
        
        Args:
            question: User question about the brochure
            franchisor_id: Optional franchisor filter
            similarity_threshold: Lower threshold for brochure content
            top_k: Number of chunks to retrieve
            temperature: Generation temperature
            
        Returns:
            Dict with answer, sources, and metadata
        """
        return await self.qa_system.answer_brochure_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )
    
    def _create_brochure_summary(self, text: str, metadata: BrochureMetadata) -> str:
        """Create comprehensive summary for main embedding"""
        summary_parts = []
        
        # Company information
        if metadata and metadata.company_name:
            summary_parts.append(f"Company: {metadata.company_name}")
        
        if metadata and metadata.industry:
            summary_parts.append(f"Industry: {metadata.industry}")
        
        if metadata and metadata.location:
            summary_parts.append(f"Location: {metadata.location}")
        
        # Services
        if metadata and metadata.services:
            services_text = "Services: " + ", ".join(metadata.services[:5])
            summary_parts.append(services_text)
        
        # Contact information
        if metadata and metadata.contact_info:
            contact_parts = []
            if metadata.phone:
                contact_parts.append(f"Phone: {metadata.phone}")
            if metadata.email:
                contact_parts.append(f"Email: {metadata.email}")
            if metadata.website:
                contact_parts.append(f"Website: {metadata.website}")
            
            if contact_parts:
                summary_parts.append("Contact: " + ", ".join(contact_parts))
        
        # Add normalized text preview
        normalized_text = self.text_normalizer.normalize_brochure_text(text)
        text_preview = normalized_text[:800] + "..." if len(normalized_text) > 800 else normalized_text
        summary_parts.append(f"Content: {text_preview}")
        
        return "\n".join(summary_parts)

# Create singleton instance
brochure_rag = BrochureRAGSystem()
"""
Singleton instance of BrochureRAGSystem for easy import
"""

# Backward compatibility aliases
production_rag = brochure_rag  # Alias for existing code
ProductionRAGSystem = BrochureRAGSystem  # Class alias
