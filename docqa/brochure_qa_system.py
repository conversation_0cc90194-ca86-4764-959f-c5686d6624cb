#!/usr/bin/env python3
"""
Brochure-Optimized Question Answering System
Specialized prompts and retrieval for company brochure content
"""

import asyncio
import sys
import json
import time
from typing import Dict, List, Any, Optional

# Add the project root to the path
sys.path.append('.')

from production_rag_system import (
    ProductionEmbeddingService,
    ProductionVectorStore,
    RetrievalResult
)

class BrochureQASystem:
    """Specialized QA system for company brochures"""
    
    def __init__(self):
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        
        # Initialize OpenAI client for answer generation
        from docqa.config import get_config
        import openai
        
        self.config = get_config()
        self.openai_client = openai.OpenAI(api_key=self.config.openai_api_key)
        self.chat_model = self.config.chat_model
        
        print(f"✅ Brochure QA System initialized")
        print(f"   Embedding Model: {self.embedding_service.model_name}")
        print(f"   Chat Model: {self.chat_model}")
    
    async def answer_brochure_question(
        self,
        question: str,
        franchisor_id: Optional[str] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.4,  # Lower threshold for brochures
        temperature: float = 0.2,  # Slightly higher for marketing language
        max_tokens: int = 800
    ) -> Dict[str, Any]:
        """
        Answer questions about company brochures with specialized handling
        
        Args:
            question: User question about the company/brochure
            franchisor_id: Optional company filter
            top_k: Number of chunks to retrieve
            similarity_threshold: Lower threshold for brochure content
            temperature: Generation temperature (0.2 for marketing balance)
            max_tokens: Maximum response tokens
            
        Returns:
            Dict with answer, sources, and metadata
        """
        start_time = time.time()
        
        try:
            print(f"🔍 Processing brochure question: {question}")
            
            # Enhance question for better brochure retrieval
            enhanced_question = self._enhance_brochure_question(question)
            
            # Generate query embedding
            query_embedding = self.embedding_service.generate_embedding(enhanced_question)
            print(f"✅ Generated query embedding: {len(query_embedding)} dimensions")
            
            # Retrieve relevant chunks with brochure-optimized thresholds
            retrieved_chunks = await self._retrieve_brochure_content(
                query_embedding=query_embedding,
                franchisor_id=franchisor_id,
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
            
            if not retrieved_chunks:
                return {
                    'success': True,
                    'answer': "I don't have specific information about that in the company brochure. Please contact the company directly for more details.",
                    'sources': [],
                    'metadata': {
                        'processing_time': time.time() - start_time,
                        'chunks_found': 0,
                        'similarity_threshold_used': similarity_threshold,
                        'question_type': 'brochure'
                    }
                }
            
            print(f"✅ Retrieved {len(retrieved_chunks)} brochure chunks")
            
            # Rerank chunks for brochure content
            reranked_chunks = self._rerank_brochure_chunks(retrieved_chunks, question)
            print(f"✅ Reranked chunks, top score: {reranked_chunks[0].similarity_score:.4f}")
            
            # Generate answer with brochure-specific prompt
            answer = await self._generate_brochure_answer(
                question=question,
                chunks=reranked_chunks,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # Format response
            response = {
                'success': True,
                'answer': answer,
                'sources': [
                    {
                        'text': chunk.text,
                        'similarity_score': chunk.similarity_score,
                        'metadata': chunk.metadata,
                        'source_info': chunk.source_info
                    }
                    for chunk in reranked_chunks
                ],
                'metadata': {
                    'processing_time': time.time() - start_time,
                    'chunks_found': len(retrieved_chunks),
                    'chunks_used': len(reranked_chunks),
                    'similarity_threshold_used': similarity_threshold,
                    'model_used': self.chat_model,
                    'temperature': temperature,
                    'question_type': 'brochure',
                    'enhanced_question': enhanced_question
                }
            }
            
            print(f"✅ Generated brochure answer: {len(answer)} characters")
            return response
            
        except Exception as e:
            print(f"❌ Error in brochure QA system: {e}")
            import traceback
            traceback.print_exc()
            
            return {
                'success': False,
                'error': str(e),
                'answer': "An error occurred while processing your question about the company brochure.",
                'sources': [],
                'metadata': {
                    'processing_time': time.time() - start_time,
                    'error_type': type(e).__name__,
                    'question_type': 'brochure'
                }
            }
    
    def _enhance_brochure_question(self, question: str) -> str:
        """Enhance question for better brochure content retrieval"""
        
        # Map common question types to brochure-specific terms
        question_enhancements = {
            'what is': ['company', 'business', 'services', 'about'],
            'where': ['location', 'based', 'address', 'contact'],
            'how': ['process', 'work', 'services', 'contact'],
            'who': ['company', 'team', 'about', 'contact'],
            'when': ['hours', 'availability', 'contact'],
            'why': ['benefits', 'features', 'advantages', 'choose'],
            'services': ['offerings', 'solutions', 'products', 'what we do'],
            'contact': ['phone', 'email', 'address', 'reach'],
            'fees': ['cost', 'price', 'investment', 'franchise'],
            'training': ['support', 'education', 'learning', 'help'],
            'franchise': ['business opportunity', 'investment', 'requirements']
        }
        
        enhanced = question.lower()
        
        # Add relevant terms based on question content
        for key_term, related_terms in question_enhancements.items():
            if key_term in enhanced:
                # Add one relevant term to expand search
                enhanced += f" {related_terms[0]}"
                break
        
        return enhanced
    
    async def _retrieve_brochure_content(
        self,
        query_embedding: List[float],
        franchisor_id: Optional[str],
        top_k: int,
        similarity_threshold: float
    ) -> List[RetrievalResult]:
        """Retrieve brochure content with optimized thresholds"""
        
        # Brochure-optimized thresholds (lower for marketing content)
        thresholds = [similarity_threshold, 0.3, 0.2, 0.1]
        
        for threshold in thresholds:
            print(f"  Trying brochure similarity threshold: {threshold}")
            
            results = self.vector_store.search_similar(
                query_embedding=query_embedding,
                top_k=top_k,
                similarity_threshold=threshold,
                franchisor_id=franchisor_id
            )
            
            if results:
                print(f"  ✅ Found {len(results)} results with threshold {threshold}")
                return results
        
        print(f"  ❌ No brochure results found with any threshold")
        return []
    
    def _rerank_brochure_chunks(self, chunks: List[RetrievalResult], question: str) -> List[RetrievalResult]:
        """Rerank chunks with brochure-specific scoring"""
        
        # Brochure section priority weights
        section_weights = {
            'services': 1.2,    # High priority for service questions
            'about': 1.1,       # Good for company questions
            'contact': 1.3,     # High priority for contact questions
            'features': 1.1,    # Good for feature questions
            'franchise': 1.2,   # High priority for franchise questions
            'general': 1.0      # Default weight
        }
        
        # Question type bonuses
        question_lower = question.lower()
        question_bonuses = {
            'contact': ['contact', 'phone', 'email', 'address', 'reach'],
            'services': ['service', 'offer', 'do', 'provide', 'solutions'],
            'about': ['what is', 'who', 'company', 'business', 'about'],
            'franchise': ['franchise', 'investment', 'fees', 'cost', 'requirements'],
            'features': ['why', 'benefits', 'advantages', 'features', 'choose']
        }
        
        # Calculate enhanced scores
        for chunk in chunks:
            base_score = chunk.similarity_score
            
            # Apply section weight
            section_type = chunk.metadata.get('type', 'general')
            section_weight = section_weights.get(section_type, 1.0)
            
            # Apply question type bonus
            question_bonus = 1.0
            for q_type, keywords in question_bonuses.items():
                if any(keyword in question_lower for keyword in keywords):
                    if section_type == q_type:
                        question_bonus = 1.15
                    break
            
            # Calculate final score
            enhanced_score = base_score * section_weight * question_bonus
            chunk.similarity_score = enhanced_score
        
        # Sort by enhanced score
        return sorted(chunks, key=lambda x: x.similarity_score, reverse=True)
    
    async def _generate_brochure_answer(
        self,
        question: str,
        chunks: List[RetrievalResult],
        temperature: float,
        max_tokens: int
    ) -> str:
        """Generate answer with brochure-specific prompts"""
        
        # Prepare context from chunks
        context_parts = []
        for i, chunk in enumerate(chunks, 1):
            context_parts.append(f"Source {i} (Similarity: {chunk.similarity_score:.3f}):\n{chunk.text}")
        
        context = "\n\n".join(context_parts)
        
        # Brochure-specific system prompt
        system_prompt = """You are a helpful assistant answering questions about a company based on their brochure content.

INSTRUCTIONS:
1. Answer based ONLY on the provided brochure content
2. Use a professional, informative tone appropriate for business inquiries
3. If the brochure doesn't contain specific information, say "This information is not available in the company brochure. Please contact the company directly for more details."
4. For contact questions, provide any available contact information from the brochure
5. For service questions, describe what the company offers based on the brochure
6. For company questions, provide information about the business from the brochure
7. Be concise but comprehensive in your answers
8. Maintain the marketing tone present in brochures while being factual

CONTEXT FORMAT:
Each source shows similarity score and brochure content. Higher similarity scores indicate more relevant content."""

        user_prompt = f"""Company Brochure Content:
{context}

Question: {question}

Answer based on the brochure content:"""
        
        try:
            # Generate answer with OpenAI
            response = self.openai_client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens,
                stream=False
            )
            
            answer = response.choices[0].message.content.strip()
            
            # Log token usage
            if response.usage:
                print(f"  Token usage: {response.usage.total_tokens} total "
                      f"({response.usage.prompt_tokens} prompt + {response.usage.completion_tokens} completion)")
            
            return answer
            
        except Exception as e:
            print(f"❌ Error generating brochure answer: {e}")
            return "An error occurred while generating the answer from the brochure content."

async def test_brochure_qa():
    """Test the brochure QA system"""
    print("🚀 Testing Brochure QA System")
    print("=" * 50)
    
    # Initialize QA system
    qa_system = BrochureQASystem()
    
    # Brochure-specific test questions
    test_questions = [
        "What is Coochie Hydrogreen?",
        "What services does the company provide?",
        "Where is the company located?",
        "How can I contact Coochie Hydrogreen?",
        "What are the benefits of choosing this company?",
        "Is this a franchise opportunity?",
        "What makes this company different?",
        "What is the company's phone number?",
        "What areas does the company serve?",
        "What experience does the company have?"
    ]
    
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    # Test with brochure-optimized parameters
    results = []
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. ❓ Brochure Question: {question}")
        
        # Use brochure-optimized parameters
        result = await qa_system.answer_brochure_question(
            question=question,
            franchisor_id=franchisor_id,
            top_k=5,                    # Moderate retrieval
            similarity_threshold=0.4,   # Lower threshold for brochures
            temperature=0.2,            # Balanced temperature for marketing content
            max_tokens=800              # More tokens for detailed brochure answers
        )
        
        if result['success']:
            answer = result['answer']
            sources_count = len(result['sources'])
            processing_time = result['metadata']['processing_time']
            
            print(f"   ✅ Brochure Answer ({processing_time:.2f}s, {sources_count} sources):")
            print(f"   {answer}")
            
            # Show top source
            if result['sources']:
                top_source = result['sources'][0]
                print(f"   📚 Top Source (Score: {top_source['similarity_score']:.3f}):")
                print(f"   {top_source['text'][:100]}...")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        results.append(result)
        
        # Small delay between questions
        await asyncio.sleep(0.5)
    
    # Summary
    successful_answers = sum(1 for r in results if r['success'] and 'not available' not in r['answer'])
    print(f"\n📊 Brochure QA Summary: {successful_answers}/{len(test_questions)} questions answered successfully")
    
    return results

async def main():
    """Main function"""
    results = await test_brochure_qa()
    
    # Save results
    with open('brochure_qa_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print("\n✅ Brochure QA results saved to brochure_qa_results.json")

if __name__ == "__main__":
    asyncio.run(main())
