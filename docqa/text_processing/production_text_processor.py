"""
Production-Grade Text Processing
With proper normalization, chunking, and validation
"""

import re
import uuid
import numpy as np
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import structlog

logger = structlog.get_logger()

@dataclass
class DocumentChunk:
    """Clean document chunk with proper embedding"""
    id: str
    text: str
    embedding: List[float]  # Always 1536-dim for text-embedding-3-small
    metadata: Dict[str, Any]
    token_count: int
    
    def __post_init__(self):
        """Validate embedding dimensions"""
        if self.embedding and len(self.embedding) != 1536:
            raise ValueError(f"Invalid embedding dimension: {len(self.embedding)}, expected 1536")

class ProductionTextNormalizer:
    """Production-grade text normalization"""
    
    @staticmethod
    def normalize_text(text: str) -> str:
        """
        Normalize text following best practices:
        - Lowercase conversion
        - Remove extra whitespace
        - Clean special characters
        - Preserve meaningful punctuation
        """
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text)
        
        # Remove non-printable characters but preserve newlines and tabs
        text = re.sub(r'[^\x20-\x7E\n\t]', '', text)
        
        # Clean up multiple punctuation
        text = re.sub(r'[.]{2,}', '.', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        return text
    
    @staticmethod
    def is_meaningful_text(text: str, min_length: int = 10) -> bool:
        """Check if text is meaningful (not just stopwords or very short)"""
        if not text or len(text.strip()) < min_length:
            return False
        
        # Check if it's not just punctuation or numbers
        meaningful_chars = re.sub(r'[^a-zA-Z]', '', text)
        if len(meaningful_chars) < 5:
            return False
        
        return True

class ProductionChunker:
    """Production-grade chunking using RecursiveCharacterTextSplitter approach"""
    
    def __init__(self, chunk_size: int = 400, chunk_overlap: int = 75):
        """
        Initialize chunker with production settings
        
        Args:
            chunk_size: Target chunk size in tokens (300-500 recommended)
            chunk_overlap: Overlap between chunks (50-100 recommended)
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.separators = ["\n\n", "\n", ". ", "! ", "? ", "; ", ": ", " ", ""]
        
    def chunk_text(self, text: str, metadata: Dict[str, Any] = None) -> List[DocumentChunk]:
        """
        Chunk text using recursive character splitting
        
        Args:
            text: Input text to chunk
            metadata: Base metadata for chunks
            
        Returns:
            List of DocumentChunk objects
        """
        if not text or not ProductionTextNormalizer.is_meaningful_text(text):
            return []
        
        # Normalize text
        normalized_text = ProductionTextNormalizer.normalize_text(text)
        
        # Split into chunks
        chunks = self._recursive_split(normalized_text, self.chunk_size)
        
        # Create DocumentChunk objects (without embeddings initially)
        document_chunks = []
        for i, chunk_text in enumerate(chunks):
            if ProductionTextNormalizer.is_meaningful_text(chunk_text):
                chunk_metadata = {
                    **(metadata or {}),
                    'chunk_index': i,
                    'chunk_method': 'recursive_character',
                    'original_length': len(text),
                    'normalized_length': len(normalized_text)
                }
                
                # Create chunk without embedding (will be added later)
                chunk = DocumentChunk(
                    id=str(uuid.uuid4()),
                    text=chunk_text,
                    embedding=[],  # Will be populated by embedding service
                    metadata=chunk_metadata,
                    token_count=self._estimate_tokens(chunk_text)
                )
                document_chunks.append(chunk)
        
        return document_chunks
    
    def _recursive_split(self, text: str, chunk_size: int, depth: int = 0) -> List[str]:
        """Recursively split text using different separators with depth limit"""
        # Prevent excessive recursion
        if depth > 10:
            # Fall back to simple splitting if recursion gets too deep
            chunks = []
            for i in range(0, len(text), chunk_size - self.chunk_overlap):
                chunk = text[i:i + chunk_size]
                if chunk.strip():
                    chunks.append(chunk.strip())
            return chunks

        if len(text) <= chunk_size:
            return [text] if text.strip() else []

        # Try each separator
        for separator in self.separators:
            if separator in text:
                splits = text.split(separator)
                if len(splits) > 1:
                    # Process each split
                    chunks = []
                    current_chunk = ""

                    for split in splits:
                        # Add separator back (except for empty separator)
                        if separator and split:
                            split = split + separator

                        if len(current_chunk) + len(split) <= chunk_size:
                            current_chunk += split
                        else:
                            if current_chunk:
                                chunks.append(current_chunk.strip())

                            # If split is still too long, recursively split it
                            if len(split) > chunk_size:
                                chunks.extend(self._recursive_split(split, chunk_size, depth + 1))
                                current_chunk = ""
                            else:
                                current_chunk = split

                    if current_chunk:
                        chunks.append(current_chunk.strip())

                    return [chunk for chunk in chunks if chunk.strip()]

        # If no separator worked, split by character count
        chunks = []
        for i in range(0, len(text), chunk_size - self.chunk_overlap):
            chunk = text[i:i + chunk_size]
            if chunk.strip():
                chunks.append(chunk.strip())

        return chunks
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count (rough approximation)"""
        # Rough estimation: 1 token ≈ 4 characters for English
        return len(text) // 4