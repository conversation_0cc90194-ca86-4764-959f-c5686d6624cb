"""
Semantic text chunking that preserves meaning and context
"""

import re
import uuid
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
import structlog

logger = structlog.get_logger()


@dataclass
class SemanticChunk:
    """A semantically meaningful text chunk"""
    id: str
    text: str
    chunk_type: str  # paragraph, section, list, table, etc.
    importance_score: float
    token_count: int
    content_type: str = "text"
    has_tables: bool = False
    has_lists: bool = False
    has_code: bool = False
    complexity_score: float = 0.5


class SemanticChunker:
    """Intelligent chunker that preserves semantic meaning"""
    
    def __init__(self, 
                 target_chunk_size: int = 500,
                 max_chunk_size: int = 800,
                 min_chunk_size: int = 100,
                 overlap_ratio: float = 0.15):
        self.target_chunk_size = target_chunk_size
        self.max_chunk_size = max_chunk_size
        self.min_chunk_size = min_chunk_size
        self.overlap_ratio = overlap_ratio
        
        logger.info("Semantic chunker initialized",
                   target_size=target_chunk_size,
                   max_size=max_chunk_size,
                   min_size=min_chunk_size)
    
    def chunk_text(self, text: str, document_type: str = "general") -> List[Dict[str, Any]]:
        """
        Chunk text semantically while preserving meaning
        
        Args:
            text: Text to chunk
            document_type: Type of document for context
            
        Returns:
            List of semantic chunks with metadata
        """
        if not text or not text.strip():
            return []
        
        # Step 1: Parse document structure
        sections = self._parse_document_structure(text)
        
        # Step 2: Create semantic chunks
        chunks = []
        chunk_id = 0
        
        for section in sections:
            section_chunks = self._chunk_section_semantically(section, chunk_id)
            chunks.extend(section_chunks)
            chunk_id += len(section_chunks)
        
        # Step 3: Add overlap for context preservation
        chunks_with_overlap = self._add_semantic_overlap(chunks)
        
        logger.info("Semantic chunking completed",
                   original_length=len(text),
                   sections=len(sections),
                   chunks=len(chunks_with_overlap),
                   avg_chunk_size=sum(len(c['text']) for c in chunks_with_overlap) / len(chunks_with_overlap) if chunks_with_overlap else 0)
        
        return chunks_with_overlap
    
    def _parse_document_structure(self, text: str) -> List[Dict[str, Any]]:
        """Parse document into structural sections"""
        sections = []
        current_section = {
            'type': 'content',
            'title': '',
            'content': '',
            'importance': 1.0,
            'start_pos': 0
        }
        
        lines = text.split('\n')
        current_content = []
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            
            # Detect section headers
            if self._is_major_section_header(line):
                # Save previous section
                if current_content:
                    current_section['content'] = '\n'.join(current_content)
                    sections.append(current_section.copy())
                
                # Start new section
                current_section = {
                    'type': 'section',
                    'title': line,
                    'content': '',
                    'importance': 2.0,  # Higher importance for sections
                    'start_pos': line_num
                }
                current_content = []
                
            elif self._is_subsection_header(line):
                # Save previous content as subsection
                if current_content:
                    subsection = current_section.copy()
                    subsection['content'] = '\n'.join(current_content)
                    subsection['type'] = 'subsection'
                    subsection['importance'] = 1.5
                    sections.append(subsection)
                
                # Start new subsection
                current_section['title'] = line
                current_section['type'] = 'subsection'
                current_section['importance'] = 1.5
                current_content = []
                
            elif self._is_table_content(line):
                # Handle table content specially
                if current_content:
                    current_section['content'] = '\n'.join(current_content)
                    sections.append(current_section.copy())
                
                # Extract table
                table_content = self._extract_table_section(lines, line_num)
                table_section = {
                    'type': 'table',
                    'title': 'Table',
                    'content': table_content,
                    'importance': 1.8,  # Tables are important
                    'start_pos': line_num
                }
                sections.append(table_section)
                current_content = []
                
            elif self._is_list_start(line):
                # Handle lists specially
                if current_content:
                    current_section['content'] = '\n'.join(current_content)
                    sections.append(current_section.copy())
                
                # Extract list
                list_content = self._extract_list_section(lines, line_num)
                list_section = {
                    'type': 'list',
                    'title': 'List',
                    'content': list_content,
                    'importance': 1.3,
                    'start_pos': line_num
                }
                sections.append(list_section)
                current_content = []
                
            else:
                current_content.append(line)
        
        # Add final section
        if current_content:
            current_section['content'] = '\n'.join(current_content)
            sections.append(current_section)
        
        return [s for s in sections if s['content'].strip()]
    
    def _is_major_section_header(self, line: str) -> bool:
        """Detect major section headers"""
        if not line or len(line) < 3 or len(line) > 100:
            return False
        
        # Enhanced header detection
        patterns = [
            r'^#{1,2}\s+.+',  # Markdown headers
            r'^\d+\.\s+[A-Z][^.]*$',  # Numbered sections
            r'^[A-Z][A-Z\s]{5,}[A-Z]$',  # ALL CAPS headers
            r'^[A-Z][a-z\s]+[A-Z][a-z\s]*$',  # Title Case headers
        ]
        
        for pattern in patterns:
            if re.match(pattern, line):
                return True
        
        return False
    
    def _is_subsection_header(self, line: str) -> bool:
        """Detect subsection headers"""
        if not line or len(line) < 3 or len(line) > 80:
            return False
        
        patterns = [
            r'^#{3,4}\s+.+',  # Markdown subsection headers
            r'^\d+\.\d+\.?\s+[A-Z]',  # Numbered subsections
            r'^[a-z]\)\s+[A-Z]',  # Lettered subsections
        ]
        
        for pattern in patterns:
            if re.match(pattern, line):
                return True
        
        return False
    
    def _is_table_content(self, line: str) -> bool:
        """Detect table content"""
        # Look for table markers
        table_indicators = ['|', '+---', '====', '----']
        return any(indicator in line for indicator in table_indicators)
    
    def _is_list_start(self, line: str) -> bool:
        """Detect start of a list"""
        if not line:
            return False
        
        list_patterns = [
            r'^\s*[•\-\*]\s+',  # Bullet points
            r'^\s*\d+\.\s+',    # Numbered lists
            r'^\s*[a-z]\)\s+',  # Lettered lists
        ]
        
        for pattern in list_patterns:
            if re.match(pattern, line):
                return True
        
        return False
    
    def _extract_table_section(self, lines: List[str], start_idx: int) -> str:
        """Extract complete table section"""
        table_lines = []
        idx = start_idx
        
        while idx < len(lines):
            line = lines[idx]
            if self._is_table_content(line) or not line.strip():
                table_lines.append(line)
                idx += 1
            else:
                break
        
        return '\n'.join(table_lines)
    
    def _extract_list_section(self, lines: List[str], start_idx: int) -> str:
        """Extract complete list section"""
        list_lines = []
        idx = start_idx
        
        while idx < len(lines):
            line = lines[idx]
            if (self._is_list_start(line) or 
                line.startswith('  ') or  # Indented continuation
                not line.strip()):  # Empty line within list
                list_lines.append(line)
                idx += 1
            else:
                break
        
        return '\n'.join(list_lines)
    
    def _chunk_section_semantically(self, section: Dict[str, Any], start_id: int) -> List[Dict[str, Any]]:
        """Chunk a section while preserving semantic meaning"""
        content = section['content']
        section_type = section['type']
        
        if len(content) <= self.target_chunk_size:
            # Small section - keep as single chunk
            chunk_metadata = SemanticChunk(
                id=str(uuid.uuid4()),
                text=content,
                chunk_type=section_type,
                importance_score=section['importance'],
                token_count=len(content.split()),
                content_type=self._map_section_to_content_type(section_type),
                has_tables=section_type == 'table',
                has_lists=section_type == 'list',
                has_code='code' in content.lower(),
                complexity_score=min(section['importance'], 1.0)
            )

            return [{
                'id': chunk_metadata.id,
                'text': chunk_metadata.text,
                'metadata': chunk_metadata
            }]
        
        # Large section - split semantically
        if section_type == 'table':
            return self._chunk_table_content(content, section)
        elif section_type == 'list':
            return self._chunk_list_content(content, section)
        else:
            return self._chunk_text_content(content, section)
    
    def _chunk_table_content(self, content: str, section: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk table content preserving structure"""
        # For tables, try to keep complete rows together
        lines = content.split('\n')
        chunks = []
        current_chunk = []
        current_size = 0
        
        for line in lines:
            line_size = len(line)
            
            if current_size + line_size > self.target_chunk_size and current_chunk:
                # Create chunk
                chunk_text = '\n'.join(current_chunk)
                chunk_metadata = SemanticChunk(
                    id=str(uuid.uuid4()),
                    text=chunk_text,
                    chunk_type='table',
                    importance_score=section['importance'],
                    token_count=len(chunk_text.split()),
                    content_type='table',
                    has_tables=True,
                    has_lists=False,
                    has_code=False,
                    complexity_score=min(section['importance'], 1.0)
                )
                chunks.append({
                    'id': chunk_metadata.id,
                    'text': chunk_metadata.text,
                    'metadata': chunk_metadata
                })
                current_chunk = [line]
                current_size = line_size
            else:
                current_chunk.append(line)
                current_size += line_size

        # Add final chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk)
            chunk_metadata = SemanticChunk(
                id=str(uuid.uuid4()),
                text=chunk_text,
                chunk_type='table',
                importance_score=section['importance'],
                token_count=len(chunk_text.split()),
                content_type='table',
                has_tables=True,
                has_lists=False,
                has_code=False,
                complexity_score=min(section['importance'], 1.0)
            )
            chunks.append({
                'id': chunk_metadata.id,
                'text': chunk_metadata.text,
                'metadata': chunk_metadata
            })
        
        return chunks

    def _chunk_list_content(self, content: str, section: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk list content preserving list structure"""
        lines = content.split('\n')
        chunks = []
        current_chunk = []
        current_size = 0

        for line in lines:
            line_size = len(line)

            # If this is a new list item and we're over target size, create chunk
            if (self._is_list_start(line) and
                current_size + line_size > self.target_chunk_size and
                current_chunk):

                chunk_text = '\n'.join(current_chunk)
                chunk_metadata = SemanticChunk(
                    id=str(uuid.uuid4()),
                    text=chunk_text,
                    chunk_type='list',
                    importance_score=section['importance'],
                    token_count=len(chunk_text.split()),
                    content_type='list',
                    has_tables=False,
                    has_lists=True,
                    has_code=False,
                    complexity_score=min(section['importance'], 1.0)
                )
                chunks.append({
                    'id': chunk_metadata.id,
                    'text': chunk_metadata.text,
                    'metadata': chunk_metadata
                })
                current_chunk = [line]
                current_size = line_size
            else:
                current_chunk.append(line)
                current_size += line_size

        # Add final chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk)
            chunk_metadata = SemanticChunk(
                id=str(uuid.uuid4()),
                text=chunk_text,
                chunk_type='list',
                importance_score=section['importance'],
                token_count=len(chunk_text.split()),
                content_type='list',
                has_tables=False,
                has_lists=True,
                has_code=False,
                complexity_score=min(section['importance'], 1.0)
            )
            chunks.append({
                'id': chunk_metadata.id,
                'text': chunk_metadata.text,
                'metadata': chunk_metadata
            })

        return chunks

    def _chunk_text_content(self, content: str, section: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk regular text content preserving paragraph boundaries"""
        # Split by paragraphs first
        paragraphs = re.split(r'\n\s*\n', content)
        chunks = []
        current_chunk = []
        current_size = 0

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            para_size = len(paragraph)

            # If adding this paragraph would exceed target size, create chunk
            if current_size + para_size > self.target_chunk_size and current_chunk:
                chunk_text = '\n\n'.join(current_chunk)
                has_code = 'code' in chunk_text.lower() or any(code_marker in chunk_text for code_marker in ['def ', 'class ', 'function', 'import '])

                chunk_metadata = SemanticChunk(
                    id=str(uuid.uuid4()),
                    text=chunk_text,
                    chunk_type=section['type'],
                    importance_score=section['importance'],
                    token_count=len(chunk_text.split()),
                    content_type=self._map_section_to_content_type(section['type']),
                    has_tables='|' in chunk_text and chunk_text.count('|') > 2,
                    has_lists=bool(re.search(r'^\s*[-•*]\s', chunk_text, re.MULTILINE)),
                    has_code=has_code,
                    complexity_score=min(section['importance'], 1.0)
                )

                chunks.append({
                    'id': chunk_metadata.id,
                    'text': chunk_metadata.text,
                    'metadata': chunk_metadata
                })
                current_chunk = [paragraph]
                current_size = para_size
            else:
                current_chunk.append(paragraph)
                current_size += para_size

        # Add final chunk
        if current_chunk:
            chunk_text = '\n\n'.join(current_chunk)
            has_code = 'code' in chunk_text.lower() or any(code_marker in chunk_text for code_marker in ['def ', 'class ', 'function', 'import '])

            chunk_metadata = SemanticChunk(
                id=str(uuid.uuid4()),
                text=chunk_text,
                chunk_type=section['type'],
                importance_score=section['importance'],
                token_count=len(chunk_text.split()),
                content_type=self._map_section_to_content_type(section['type']),
                has_tables='|' in chunk_text and chunk_text.count('|') > 2,
                has_lists=bool(re.search(r'^\s*[-•*]\s', chunk_text, re.MULTILINE)),
                has_code=has_code,
                complexity_score=min(section['importance'], 1.0)
            )

            chunks.append({
                'id': chunk_metadata.id,
                'text': chunk_metadata.text,
                'metadata': chunk_metadata
            })

        return chunks

    def _add_semantic_overlap(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Add semantic overlap between chunks for better context"""
        if len(chunks) <= 1:
            return chunks

        enhanced_chunks = []

        for i, chunk in enumerate(chunks):
            enhanced_chunk = chunk.copy()

            # Add context from previous chunk
            if i > 0:
                prev_chunk = chunks[i - 1]
                prev_text = prev_chunk['text']

                # Take last sentence(s) from previous chunk
                prev_sentences = re.split(r'[.!?]+\s+', prev_text)
                if len(prev_sentences) > 1:
                    overlap_text = prev_sentences[-1].strip()
                    if overlap_text and len(overlap_text) > 20:
                        enhanced_chunk['text'] = f"[Previous context: {overlap_text}]\n\n{chunk['text']}"

            # Add context to next chunk
            if i < len(chunks) - 1:
                current_text = chunk['text']
                current_sentences = re.split(r'[.!?]+\s+', current_text)

                if len(current_sentences) > 1:
                    overlap_text = current_sentences[0].strip()
                    if overlap_text and len(overlap_text) > 20:
                        enhanced_chunk['metadata']['next_context'] = overlap_text

            enhanced_chunks.append(enhanced_chunk)

        return enhanced_chunks

    def _map_section_to_content_type(self, section_type: str) -> str:
        """Map section type to content type"""
        mapping = {
            'table': 'table',
            'list': 'list',
            'section': 'text',
            'subsection': 'text',
            'content': 'text'
        }
        return mapping.get(section_type, 'text')
