"""
Adaptive text chunking with intelligent sizing based on content type
"""

import re
import uuid
from typing import List, Dict, Any
from dataclasses import dataclass
import structlog

logger = structlog.get_logger()


@dataclass
class ChunkMetadata:
    """Metadata for a text chunk"""
    chunk_id: str
    content_type: str
    complexity_score: float
    token_count: int
    has_tables: bool
    has_lists: bool
    has_code: bool


class AdaptiveChunker:
    """Intelligent text chunker that adapts size based on content complexity"""
    
    def __init__(self, 
                 base_chunk_size: int = 400,
                 max_chunk_size: int = 800,
                 min_chunk_size: int = 100,
                 overlap_ratio: float = 0.1):
        self.base_chunk_size = base_chunk_size
        self.max_chunk_size = max_chunk_size
        self.min_chunk_size = min_chunk_size
        self.overlap_ratio = overlap_ratio
        
        logger.info("Adaptive chunker initialized",
                   base_size=base_chunk_size,
                   max_size=max_chunk_size,
                   min_size=min_chunk_size)
    
    def chunk_text(self, text: str, document_type: str = "general") -> List[Dict[str, Any]]:
        """
        Chunk text adaptively based on content complexity
        
        Args:
            text: Text to chunk
            document_type: Type of document (pdf, docx, etc.)
            
        Returns:
            List of chunks with metadata
        """
        if not text or not text.strip():
            return []
        
        # Analyze text complexity
        complexity = self._analyze_complexity(text)
        
        # Determine optimal chunk size
        chunk_size = self._calculate_optimal_chunk_size(complexity, document_type)
        overlap_size = int(chunk_size * self.overlap_ratio)
        
        # Split into semantic sections first
        sections = self._split_into_sections(text)
        
        chunks = []
        chunk_id = 0
        
        for section in sections:
            section_chunks = self._chunk_section(
                section, chunk_size, overlap_size, chunk_id
            )
            chunks.extend(section_chunks)
            chunk_id += len(section_chunks)
        
        logger.debug("Text chunked adaptively",
                    original_length=len(text),
                    chunk_count=len(chunks),
                    avg_chunk_size=sum(len(c['text']) for c in chunks) / len(chunks) if chunks else 0,
                    complexity_score=complexity)
        
        return chunks
    
    def _analyze_complexity(self, text: str) -> float:
        """Analyze text complexity to determine optimal chunking strategy"""
        complexity_score = 0.0
        
        # Length factor
        length_factor = min(len(text) / 10000, 1.0)  # Normalize to 0-1
        complexity_score += length_factor * 0.2
        
        # Technical content indicators
        if re.search(r'\b(function|class|def|import|SELECT|FROM|WHERE)\b', text, re.IGNORECASE):
            complexity_score += 0.3
        
        # Mathematical content
        if re.search(r'[∑∫∂∆αβγδεζηθικλμνξοπρστυφχψω]|\\[a-zA-Z]+', text):
            complexity_score += 0.2
        
        # Tables and structured data
        table_count = len(re.findall(r'\|.*\|', text))
        if table_count > 0:
            complexity_score += min(table_count / 10, 0.3)
        
        # Lists and bullet points
        list_count = len(re.findall(r'^\s*[-•*]\s', text, re.MULTILINE))
        if list_count > 0:
            complexity_score += min(list_count / 20, 0.2)
        
        # Sentence complexity (average sentence length)
        sentences = re.split(r'[.!?]+', text)
        if sentences:
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
            if avg_sentence_length > 20:
                complexity_score += 0.2
        
        return min(complexity_score, 1.0)
    
    def _calculate_optimal_chunk_size(self, complexity: float, document_type: str) -> int:
        """Calculate optimal chunk size based on complexity and document type"""
        # Base adjustment for document type
        type_multipliers = {
            'pdf': 1.2,      # PDFs often have complex layouts
            'docx': 1.0,     # Standard documents
            'txt': 0.8,      # Simple text files
            'csv': 1.5,      # Structured data
            'html': 1.1,     # Web content
            'general': 1.0
        }
        
        multiplier = type_multipliers.get(document_type, 1.0)
        
        # Adjust based on complexity
        if complexity < 0.3:
            # Simple content - larger chunks
            size = int(self.base_chunk_size * 1.3 * multiplier)
        elif complexity < 0.6:
            # Medium complexity - standard chunks
            size = int(self.base_chunk_size * multiplier)
        else:
            # High complexity - smaller chunks for better precision
            size = int(self.base_chunk_size * 0.7 * multiplier)
        
        # Ensure within bounds
        return max(self.min_chunk_size, min(size, self.max_chunk_size))
    
    def _split_into_sections(self, text: str) -> List[str]:
        """Split text into logical sections"""
        # Split by double newlines (paragraphs)
        sections = re.split(r'\n\s*\n', text)
        
        # Further split very long sections
        final_sections = []
        for section in sections:
            if len(section) > self.max_chunk_size * 2:
                # Split long sections by sentences
                sentences = re.split(r'(?<=[.!?])\s+', section)
                current_section = ""
                
                for sentence in sentences:
                    if len(current_section + sentence) > self.max_chunk_size * 1.5:
                        if current_section:
                            final_sections.append(current_section.strip())
                        current_section = sentence
                    else:
                        current_section += " " + sentence if current_section else sentence
                
                if current_section:
                    final_sections.append(current_section.strip())
            else:
                final_sections.append(section.strip())
        
        return [s for s in final_sections if s]
    
    def _chunk_section(self, section: str, chunk_size: int, overlap_size: int, start_id: int) -> List[Dict[str, Any]]:
        """Chunk a single section with overlap"""
        if len(section) <= chunk_size:
            return [{
                'id': str(uuid.uuid4()),
                'text': section,
                'metadata': self._create_chunk_metadata(section, start_id)
            }]
        
        chunks = []
        start = 0
        chunk_id = start_id
        
        while start < len(section):
            end = start + chunk_size
            
            # Try to end at a sentence boundary
            if end < len(section):
                # Look for sentence endings within the last 20% of the chunk
                search_start = max(start + int(chunk_size * 0.8), start + 1)
                sentence_end = self._find_sentence_boundary(section, search_start, end)
                if sentence_end > start:
                    end = sentence_end
            
            chunk_text = section[start:end].strip()
            if chunk_text:
                chunks.append({
                    'id': str(uuid.uuid4()),
                    'text': chunk_text,
                    'metadata': self._create_chunk_metadata(chunk_text, chunk_id)
                })
                chunk_id += 1
            
            # Move start position with overlap
            start = max(start + 1, end - overlap_size)
        
        return chunks
    
    def _find_sentence_boundary(self, text: str, start: int, end: int) -> int:
        """Find the best sentence boundary within a range"""
        # Look for sentence endings
        for i in range(end - 1, start - 1, -1):
            if text[i] in '.!?' and i + 1 < len(text) and text[i + 1].isspace():
                return i + 1
        
        # Fallback to word boundary
        for i in range(end - 1, start - 1, -1):
            if text[i].isspace():
                return i
        
        return end
    
    def _create_chunk_metadata(self, text: str, chunk_id: int) -> ChunkMetadata:
        """Create metadata for a chunk"""
        return ChunkMetadata(
            chunk_id=f"chunk_{chunk_id}",
            content_type=self._detect_content_type(text),
            complexity_score=self._analyze_complexity(text),
            token_count=len(text.split()),
            has_tables='|' in text and text.count('|') > 2,
            has_lists=bool(re.search(r'^\s*[-•*]\s', text, re.MULTILINE)),
            has_code=bool(re.search(r'\b(function|class|def|import)\b', text, re.IGNORECASE))
        )
    
    def _detect_content_type(self, text: str) -> str:
        """Detect the type of content in the text"""
        text_lower = text.lower()
        
        if any(keyword in text_lower for keyword in ['function', 'class', 'def', 'import']):
            return 'code'
        elif '|' in text and text.count('|') > 2:
            return 'table'
        elif re.search(r'^\s*[-•*]\s', text, re.MULTILINE):
            return 'list'
        elif any(keyword in text_lower for keyword in ['figure', 'chart', 'graph', 'diagram']):
            return 'visual'
        else:
            return 'text'
