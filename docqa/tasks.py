"""
Enhanced Celery Tasks for High-Performance Document Processing
Supports parallel processing, priority queues, and Redis integration
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from celery import Task
import structlog

from app.core.celery_app import celery_app
from .redis_store import get_redis_store, JobStatus
from .ingest import DocumentIngestionService
from .vector_store import PgVectorStore

logger = structlog.get_logger()


class DocQATask(Task):
    """Enhanced base task class with Redis integration"""
    
    def __init__(self):
        self.redis_store = None
        self.ingestion_service = None
        self.vector_store = None
    
    async def setup_services(self):
        """Initialize services asynchronously"""
        if not self.redis_store:
            self.redis_store = await get_redis_store()
        
        if not self.ingestion_service:
            self.ingestion_service = DocumentIngestionService()
        
        if not self.vector_store:
            self.vector_store = PgVectorStore()
            await self.vector_store.connect()
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called when task succeeds"""
        logger.info("DocQA task completed successfully", 
                   task_id=task_id, result=retval)
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when task fails"""
        logger.error("DocQA task failed", 
                    task_id=task_id, error=str(exc), traceback=str(einfo))
        
        # Update Redis status on failure
        if len(args) > 0:
            url = args[0]
            asyncio.create_task(self._update_failure_status(url, task_id, str(exc)))
    
    async def _update_failure_status(self, url: str, task_id: str, error: str):
        """Update Redis with failure status"""
        try:
            redis_store = await get_redis_store()
            await redis_store.set_job_status(
                url=url,
                status=JobStatus.FAILED,
                job_id=task_id,
                metadata={"error": error, "failed_at": datetime.utcnow().isoformat()}
            )
        except Exception as e:
            logger.error(f"Failed to update Redis failure status: {e}")


@celery_app.task(
    bind=True,
    base=DocQATask,
    name="docqa.tasks.ingest_document_parallel",
    queue="document_processing",
    max_retries=3,
    default_retry_delay=60,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60}
)
def ingest_document_parallel(
    self,
    source_url: str,
    force_table: Optional[str] = None,
    translate: bool = True,
    extract_charts: bool = True,
    priority: int = 5,
    processing_options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    High-performance parallel document ingestion task
    
    Args:
        source_url: Document URL or S3 path
        force_table: Force routing to specific table (franchisors/documents)
        translate: Enable translation for non-English content
        extract_charts: Enable chart extraction and captioning
        priority: Task priority (1-10, higher = more priority)
        processing_options: Additional processing configuration
        
    Returns:
        Dict with ingestion results
    """
    task_id = self.request.id
    
    try:
        # Run async setup and processing
        return asyncio.run(_process_document_async(
            task_id=task_id,
            source_url=source_url,
            force_table=force_table,
            translate=translate,
            extract_charts=extract_charts,
            priority=priority,
            processing_options=processing_options or {}
        ))
        
    except Exception as e:
        logger.error(f"Document ingestion failed: {e}", task_id=task_id, url=source_url)
        raise


async def _process_document_async(
    task_id: str,
    source_url: str,
    force_table: Optional[str] = None,
    translate: bool = True,
    extract_charts: bool = True,
    priority: int = 5,
    processing_options: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Async document processing with Redis integration"""
    
    redis_store = await get_redis_store()
    processing_options = processing_options or {}
    
    try:
        # Check if document already processed (deduplication)
        if await redis_store.is_document_processed(source_url):
            doc_info = await redis_store.get_document_info(source_url)
            logger.info(f"Document already processed: {source_url}")
            
            return {
                "success": True,
                "message": "Document already processed",
                "source_url": source_url,
                "document_hash": doc_info.get("document_hash"),
                "processed_at": doc_info.get("processed_at"),
                "cached": True
            }
        
        # Set initial status
        await redis_store.set_job_status(
            url=source_url,
            status=JobStatus.PROCESSING,
            job_id=task_id,
            metadata={
                "started_at": datetime.utcnow().isoformat(),
                "force_table": force_table,
                "priority": priority,
                "options": processing_options
            }
        )
        
        # Initialize progress tracking
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=0,
            message="Starting document processing..."
        )
        
        # Initialize ingestion service
        ingestion_service = DocumentIngestionService()
        
        # Progress: File download/access
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=10,
            message="Accessing document..."
        )
        
        # Progress: Content extraction
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=30,
            message="Extracting content and metadata..."
        )
        
        # Process document with enhanced options
        result = ingestion_service.ingest_document(
            source=source_url,
            force_table=force_table,
            translate=translate,
            extract_charts=extract_charts,
            **processing_options
        )
        
        if not result or not result.success:
            raise Exception(f"Ingestion failed: {result.error if result else 'Unknown error'}")
        
        # Progress: Embedding generation
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=70,
            message="Generating embeddings..."
        )
        
        # Progress: Vector storage
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=90,
            message="Storing vectors in database..."
        )
        
        # Mark as completed in Redis
        await redis_store.mark_document_processed(
            url=source_url,
            document_hash=result.document_hash
        )
        
        await redis_store.set_job_status(
            url=source_url,
            status=JobStatus.COMPLETED,
            job_id=task_id,
            metadata={
                "completed_at": datetime.utcnow().isoformat(),
                "chunks_created": result.chunks_created,
                "processing_time": result.processing_time,
                "document_hash": result.document_hash
            }
        )
        
        # Final progress update
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=100,
            message="Document processing completed successfully"
        )
        
        logger.info(f"Document ingestion completed: {source_url}", 
                   task_id=task_id, chunks=result.chunks_created)
        
        return {
            "success": True,
            "message": "Document processed successfully",
            "source_url": source_url,
            "document_hash": result.document_hash,
            "chunks_created": result.chunks_created,
            "processing_time": result.processing_time,
            "task_id": task_id,
            "cached": False
        }
        
    except Exception as e:
        # Update failure status
        await redis_store.set_job_status(
            url=source_url,
            status=JobStatus.FAILED,
            job_id=task_id,
            metadata={
                "error": str(e),
                "failed_at": datetime.utcnow().isoformat()
            }
        )
        
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=0,
            message=f"Processing failed: {str(e)}"
        )
        
        logger.error(f"Document processing failed: {e}", task_id=task_id, url=source_url)
        raise


@celery_app.task(
    bind=True,
    base=DocQATask,
    name="docqa.tasks.batch_ingest_documents",
    queue="document_processing",
    max_retries=2,
    default_retry_delay=120
)
def batch_ingest_documents(
    self,
    document_urls: List[str],
    batch_options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Batch process multiple documents in parallel
    
    Args:
        document_urls: List of document URLs to process
        batch_options: Batch processing configuration
        
    Returns:
        Dict with batch processing results
    """
    task_id = self.request.id
    batch_options = batch_options or {}
    
    try:
        return asyncio.run(_process_batch_async(
            task_id=task_id,
            document_urls=document_urls,
            batch_options=batch_options
        ))
        
    except Exception as e:
        logger.error(f"Batch ingestion failed: {e}", task_id=task_id)
        raise


async def _process_batch_async(
    task_id: str,
    document_urls: List[str],
    batch_options: Dict[str, Any]
) -> Dict[str, Any]:
    """Async batch document processing"""
    
    redis_store = await get_redis_store()
    
    try:
        total_docs = len(document_urls)
        completed = 0
        failed = 0
        results = []
        
        # Set initial batch status
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=0,
            message=f"Starting batch processing of {total_docs} documents..."
        )
        
        # Process documents with controlled concurrency
        max_concurrent = batch_options.get("max_concurrent", 3)
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_doc(url: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    # Create subtask for individual document
                    subtask_result = ingest_document_parallel.delay(
                        source_url=url,
                        **batch_options.get("document_options", {})
                    )
                    
                    # Wait for completion (with timeout)
                    timeout = batch_options.get("timeout_per_doc", 300)  # 5 minutes
                    result = subtask_result.get(timeout=timeout)
                    
                    return {"url": url, "success": True, "result": result}
                    
                except Exception as e:
                    logger.error(f"Failed to process document in batch: {url}, error: {e}")
                    return {"url": url, "success": False, "error": str(e)}
        
        # Process all documents
        tasks = [process_single_doc(url) for url in document_urls]
        
        # Process with progress updates
        for i, coro in enumerate(asyncio.as_completed(tasks)):
            result = await coro
            results.append(result)
            
            if result["success"]:
                completed += 1
            else:
                failed += 1
            
            # Update progress
            progress = int((i + 1) / total_docs * 100)
            await redis_store.set_job_progress(
                job_id=task_id,
                progress=progress,
                message=f"Processed {i + 1}/{total_docs} documents (✓{completed} ✗{failed})"
            )
        
        # Final status
        success_rate = (completed / total_docs) * 100 if total_docs > 0 else 0
        
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=100,
            message=f"Batch completed: {completed}/{total_docs} successful ({success_rate:.1f}%)"
        )
        
        logger.info("Batch processing completed", 
                   task_id=task_id, total=total_docs, completed=completed, failed=failed)
        
        return {
            "success": True,
            "message": "Batch processing completed",
            "total_documents": total_docs,
            "completed": completed,
            "failed": failed,
            "success_rate": success_rate,
            "results": results,
            "task_id": task_id
        }
        
    except Exception as e:
        await redis_store.set_job_progress(
            job_id=task_id,
            progress=0,
            message=f"Batch processing failed: {str(e)}"
        )
        
        logger.error(f"Batch processing failed: {e}", task_id=task_id)
        raise


# Task routing and priority configuration
def route_task_by_priority(name, args, kwargs, options, task=None, **kwds):
    """Route tasks to appropriate queues based on priority"""
    priority = kwargs.get('priority', 5)
    
    if priority >= 8:
        return {'queue': 'high_priority'}
    elif priority >= 6:
        return {'queue': 'medium_priority'}
    else:
        return {'queue': 'low_priority'}


# Register task routing
celery_app.conf.task_routes = {
    'docqa.tasks.ingest_document_parallel': route_task_by_priority,
    'docqa.tasks.batch_ingest_documents': {'queue': 'batch_processing'},
}
