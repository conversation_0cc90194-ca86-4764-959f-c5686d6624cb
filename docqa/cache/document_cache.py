"""
Document Caching and Duplicate Detection System

This module implements intelligent document caching and duplicate detection to improve
efficiency by skipping already processed documents. Features include:
- Hash-based document fingerprinting
- Persistent cache storage
- Content-based duplicate detection
- Cache invalidation and management
- Performance metrics and monitoring
"""

import hashlib
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
import structlog
import sqlite3
from dataclasses import dataclass, asdict
from threading import Lock

from ..config import config
from ..types import IngestionResult

logger = structlog.get_logger()


@dataclass
class CacheEntry:
    """Represents a cache entry for a processed document"""
    document_hash: str
    document_id: str
    source_url: str
    filename: str
    file_size: int
    file_type: str
    table_name: str
    chunks_created: int
    processing_time: float
    created_at: float
    last_accessed: float
    access_count: int
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """Create from dictionary"""
        return cls(**data)


class DocumentCache:
    """
    Intelligent document cache with hash-based duplicate detection
    """
    
    def __init__(self, cache_dir: Optional[str] = None):
        """
        Initialize document cache
        
        Args:
            cache_dir: Directory for cache storage (uses config default if None)
        """
        self.cache_dir = Path(cache_dir or config.cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache database file
        self.db_path = self.cache_dir / "document_cache.db"
        
        # In-memory cache for fast access
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.cache_lock = Lock()
        
        # Performance metrics
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'cache_saves': 0,
            'cache_invalidations': 0
        }
        
        # Initialize database
        self._init_database()
        
        # Load cache into memory
        self._load_cache()
        
        logger.info("Document cache initialized", 
                   cache_dir=str(self.cache_dir),
                   entries_loaded=len(self.memory_cache))
    
    def _init_database(self):
        """Initialize SQLite database for persistent cache storage"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS document_cache (
                        document_hash TEXT PRIMARY KEY,
                        document_id TEXT NOT NULL,
                        source_url TEXT NOT NULL,
                        filename TEXT NOT NULL,
                        file_size INTEGER NOT NULL,
                        file_type TEXT NOT NULL,
                        table_name TEXT NOT NULL,
                        chunks_created INTEGER NOT NULL,
                        processing_time REAL NOT NULL,
                        created_at REAL NOT NULL,
                        last_accessed REAL NOT NULL,
                        access_count INTEGER NOT NULL,
                        metadata TEXT
                    )
                """)
                
                # Create indexes for better performance
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_document_id 
                    ON document_cache(document_id)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_source_url 
                    ON document_cache(source_url)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_created_at 
                    ON document_cache(created_at)
                """)
                
                conn.commit()
                
        except Exception as e:
            logger.error("Failed to initialize cache database", error=str(e))
            raise
    
    def _load_cache(self):
        """Load cache entries from database into memory"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT * FROM document_cache")
                
                for row in cursor:
                    metadata = json.loads(row['metadata']) if row['metadata'] else {}
                    
                    entry = CacheEntry(
                        document_hash=row['document_hash'],
                        document_id=row['document_id'],
                        source_url=row['source_url'],
                        filename=row['filename'],
                        file_size=row['file_size'],
                        file_type=row['file_type'],
                        table_name=row['table_name'],
                        chunks_created=row['chunks_created'],
                        processing_time=row['processing_time'],
                        created_at=row['created_at'],
                        last_accessed=row['last_accessed'],
                        access_count=row['access_count'],
                        metadata=metadata
                    )
                    
                    self.memory_cache[entry.document_hash] = entry
                    
        except Exception as e:
            logger.error("Failed to load cache from database", error=str(e))
    
    def generate_document_hash(self, file_path: Path) -> str:
        """
        Generate hash for document content
        
        Args:
            file_path: Path to the document file
            
        Returns:
            SHA-256 hash of the document content
        """
        try:
            hasher = hashlib.sha256()
            
            # Include file metadata in hash for better uniqueness
            stat = file_path.stat()
            hasher.update(f"{file_path.name}:{stat.st_size}:{stat.st_mtime}".encode())
            
            # Hash file content in chunks to handle large files
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hasher.update(chunk)
            
            return hasher.hexdigest()
            
        except Exception as e:
            logger.error("Failed to generate document hash", 
                        path=str(file_path), error=str(e))
            return ""
    
    def generate_content_hash(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate hash for text content
        
        Args:
            content: Text content to hash
            metadata: Optional metadata to include in hash
            
        Returns:
            SHA-256 hash of the content
        """
        try:
            hasher = hashlib.sha256()
            hasher.update(content.encode('utf-8'))
            
            if metadata:
                # Sort metadata keys for consistent hashing
                sorted_metadata = json.dumps(metadata, sort_keys=True)
                hasher.update(sorted_metadata.encode('utf-8'))
            
            return hasher.hexdigest()
            
        except Exception as e:
            logger.error("Failed to generate content hash", error=str(e))
            return ""
    
    def is_document_cached(self, file_path: Path) -> Optional[CacheEntry]:
        """
        Check if document is already cached
        
        Args:
            file_path: Path to the document file
            
        Returns:
            CacheEntry if found, None otherwise
        """
        doc_hash = self.generate_document_hash(file_path)
        if not doc_hash:
            return None
        
        with self.cache_lock:
            entry = self.memory_cache.get(doc_hash)
            
            if entry:
                # Update access statistics
                entry.last_accessed = time.time()
                entry.access_count += 1
                self.stats['cache_hits'] += 1
                
                # Update database
                self._update_access_stats(entry)
                
                logger.info("Cache hit", 
                           document_hash=doc_hash,
                           document_id=entry.document_id,
                           access_count=entry.access_count)
                
                return entry
            else:
                self.stats['cache_misses'] += 1
                logger.debug("Cache miss", document_hash=doc_hash)
                return None
    
    def cache_document(
        self,
        file_path: Path,
        document_id: str,
        source_url: str,
        table_name: str,
        ingestion_result: IngestionResult,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Cache a processed document
        
        Args:
            file_path: Path to the document file
            document_id: Document identifier
            source_url: Source URL or path
            table_name: Target table name
            ingestion_result: Result of document processing
            metadata: Optional additional metadata
            
        Returns:
            True if cached successfully
        """
        try:
            doc_hash = self.generate_document_hash(file_path)
            if not doc_hash:
                return False
            
            current_time = time.time()
            stat = file_path.stat()
            
            entry = CacheEntry(
                document_hash=doc_hash,
                document_id=document_id,
                source_url=source_url,
                filename=file_path.name,
                file_size=stat.st_size,
                file_type=file_path.suffix.lower(),
                table_name=table_name,
                chunks_created=ingestion_result.chunks_created,
                processing_time=ingestion_result.processing_time or 0.0,
                created_at=current_time,
                last_accessed=current_time,
                access_count=1,
                metadata=metadata or {}
            )
            
            with self.cache_lock:
                # Store in memory cache
                self.memory_cache[doc_hash] = entry
                
                # Store in database
                self._save_to_database(entry)
                
                self.stats['cache_saves'] += 1
            
            logger.info("Document cached", 
                       document_hash=doc_hash,
                       document_id=document_id,
                       chunks_created=entry.chunks_created)
            
            return True
            
        except Exception as e:
            logger.error("Failed to cache document", 
                        document_id=document_id, error=str(e))
            return False
    
    def _save_to_database(self, entry: CacheEntry):
        """Save cache entry to database"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO document_cache 
                    (document_hash, document_id, source_url, filename, file_size, 
                     file_type, table_name, chunks_created, processing_time, 
                     created_at, last_accessed, access_count, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    entry.document_hash,
                    entry.document_id,
                    entry.source_url,
                    entry.filename,
                    entry.file_size,
                    entry.file_type,
                    entry.table_name,
                    entry.chunks_created,
                    entry.processing_time,
                    entry.created_at,
                    entry.last_accessed,
                    entry.access_count,
                    json.dumps(entry.metadata) if entry.metadata else None
                ))
                conn.commit()
                
        except Exception as e:
            logger.error("Failed to save cache entry to database", error=str(e))
    
    def _update_access_stats(self, entry: CacheEntry):
        """Update access statistics in database"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute("""
                    UPDATE document_cache 
                    SET last_accessed = ?, access_count = ?
                    WHERE document_hash = ?
                """, (entry.last_accessed, entry.access_count, entry.document_hash))
                conn.commit()
                
        except Exception as e:
            logger.debug("Failed to update access stats", error=str(e))
    
    def invalidate_document(self, document_id: str) -> bool:
        """
        Invalidate cached document by document ID
        
        Args:
            document_id: Document identifier to invalidate
            
        Returns:
            True if invalidated successfully
        """
        try:
            with self.cache_lock:
                # Find and remove from memory cache
                to_remove = []
                for doc_hash, entry in self.memory_cache.items():
                    if entry.document_id == document_id:
                        to_remove.append(doc_hash)
                
                for doc_hash in to_remove:
                    del self.memory_cache[doc_hash]
                
                # Remove from database
                with sqlite3.connect(str(self.db_path)) as conn:
                    conn.execute(
                        "DELETE FROM document_cache WHERE document_id = ?",
                        (document_id,)
                    )
                    conn.commit()
                
                self.stats['cache_invalidations'] += len(to_remove)
            
            logger.info("Document cache invalidated", 
                       document_id=document_id,
                       entries_removed=len(to_remove))
            
            return len(to_remove) > 0
            
        except Exception as e:
            logger.error("Failed to invalidate document cache", 
                        document_id=document_id, error=str(e))
            return False
    
    def cleanup_old_entries(self, max_age_days: int = 30) -> int:
        """
        Clean up old cache entries
        
        Args:
            max_age_days: Maximum age in days for cache entries
            
        Returns:
            Number of entries removed
        """
        try:
            cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
            
            with self.cache_lock:
                # Find old entries
                to_remove = []
                for doc_hash, entry in self.memory_cache.items():
                    if entry.last_accessed < cutoff_time:
                        to_remove.append(doc_hash)
                
                # Remove from memory cache
                for doc_hash in to_remove:
                    del self.memory_cache[doc_hash]
                
                # Remove from database
                with sqlite3.connect(str(self.db_path)) as conn:
                    conn.execute(
                        "DELETE FROM document_cache WHERE last_accessed < ?",
                        (cutoff_time,)
                    )
                    conn.commit()
            
            logger.info("Cache cleanup completed", 
                       entries_removed=len(to_remove),
                       max_age_days=max_age_days)
            
            return len(to_remove)
            
        except Exception as e:
            logger.error("Failed to cleanup cache", error=str(e))
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        with self.cache_lock:
            total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
            hit_rate = (self.stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'total_entries': len(self.memory_cache),
                'cache_hits': self.stats['cache_hits'],
                'cache_misses': self.stats['cache_misses'],
                'cache_saves': self.stats['cache_saves'],
                'cache_invalidations': self.stats['cache_invalidations'],
                'hit_rate_percent': round(hit_rate, 2),
                'total_requests': total_requests,
                'database_size_mb': round(self.db_path.stat().st_size / (1024 * 1024), 2) if self.db_path.exists() else 0
            }
    
    def get_cached_documents(self) -> List[Dict[str, Any]]:
        """Get list of all cached documents"""
        with self.cache_lock:
            return [entry.to_dict() for entry in self.memory_cache.values()]
    
    def find_similar_documents(self, file_path: Path, similarity_threshold: float = 0.8) -> List[CacheEntry]:
        """
        Find similar documents based on content similarity
        
        Args:
            file_path: Path to the document to compare
            similarity_threshold: Minimum similarity threshold (0.0 to 1.0)
            
        Returns:
            List of similar cached documents
        """
        # This is a placeholder for content-based similarity
        # In a full implementation, you would use embeddings or other similarity measures
        similar_docs = []
        
        try:
            target_size = file_path.stat().st_size
            target_name = file_path.name.lower()
            
            with self.cache_lock:
                for entry in self.memory_cache.values():
                    # Simple similarity based on file size and name
                    size_similarity = 1.0 - abs(entry.file_size - target_size) / max(entry.file_size, target_size)
                    name_similarity = 1.0 if entry.filename.lower() == target_name else 0.5
                    
                    overall_similarity = (size_similarity + name_similarity) / 2
                    
                    if overall_similarity >= similarity_threshold:
                        similar_docs.append(entry)
            
            return sorted(similar_docs, key=lambda x: x.last_accessed, reverse=True)
            
        except Exception as e:
            logger.error("Failed to find similar documents", error=str(e))
            return []
