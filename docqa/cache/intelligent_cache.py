"""
Intelligent caching system for DocQA processing optimization
"""

import hashlib
import json
import time
from typing import Any, Dict, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import structlog

logger = structlog.get_logger()


@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: float
    accessed_at: float
    access_count: int
    size_bytes: int
    ttl: Optional[float] = None


class IntelligentCache:
    """
    Multi-level intelligent cache with LRU eviction and smart prefetching
    """
    
    def __init__(self, 
                 max_memory_mb: int = 512,
                 default_ttl: int = 3600,
                 enable_persistence: bool = True,
                 cache_dir: str = "cache"):
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.enable_persistence = enable_persistence
        self.cache_dir = Path(cache_dir)
        
        # In-memory cache
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._current_memory_usage = 0
        
        # Cache statistics
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'memory_usage': 0
        }
        
        # Create cache directory
        if self.enable_persistence:
            self.cache_dir.mkdir(exist_ok=True)
        
        logger.info("Intelligent cache initialized",
                   max_memory_mb=max_memory_mb,
                   default_ttl=default_ttl,
                   persistence=enable_persistence)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache with intelligent access tracking"""
        cache_key = self._hash_key(key)
        
        # Check memory cache first
        if cache_key in self._memory_cache:
            entry = self._memory_cache[cache_key]
            
            # Check TTL
            if self._is_expired(entry):
                self._remove_from_memory(cache_key)
                self._stats['misses'] += 1
                return None
            
            # Update access metadata
            entry.accessed_at = time.time()
            entry.access_count += 1
            self._stats['hits'] += 1
            
            logger.debug("Cache hit (memory)", key=key, access_count=entry.access_count)
            return entry.value
        
        # Check persistent cache
        if self.enable_persistence:
            persistent_value = self._get_from_disk(cache_key)
            if persistent_value is not None:
                # Load back into memory cache
                self.set(key, persistent_value, promote_to_memory=True)
                self._stats['hits'] += 1
                logger.debug("Cache hit (disk)", key=key)
                return persistent_value
        
        self._stats['misses'] += 1
        logger.debug("Cache miss", key=key)
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, promote_to_memory: bool = False) -> bool:
        """Set value in cache with intelligent storage strategy"""
        cache_key = self._hash_key(key)
        
        # Calculate value size
        value_size = self._calculate_size(value)
        
        # Create cache entry
        entry = CacheEntry(
            key=cache_key,
            value=value,
            created_at=time.time(),
            accessed_at=time.time(),
            access_count=1,
            size_bytes=value_size,
            ttl=ttl or self.default_ttl
        )
        
        # Decide storage strategy
        if value_size > self.max_memory_bytes * 0.1:  # Large items go to disk
            if self.enable_persistence:
                success = self._save_to_disk(cache_key, value, entry)
                logger.debug("Large item cached to disk", key=key, size_mb=value_size/1024/1024)
                return success
            else:
                logger.warning("Item too large for memory cache", key=key, size_mb=value_size/1024/1024)
                return False
        
        # Store in memory
        if self._current_memory_usage + value_size > self.max_memory_bytes:
            self._evict_lru()
        
        self._memory_cache[cache_key] = entry
        self._current_memory_usage += value_size
        self._stats['memory_usage'] = self._current_memory_usage
        
        # Also save to disk for persistence
        if self.enable_persistence and not promote_to_memory:
            self._save_to_disk(cache_key, value, entry)
        
        logger.debug("Item cached to memory", key=key, size_bytes=value_size)
        return True
    
    def invalidate(self, key: str) -> bool:
        """Remove item from cache"""
        cache_key = self._hash_key(key)
        
        # Remove from memory
        if cache_key in self._memory_cache:
            self._remove_from_memory(cache_key)
        
        # Remove from disk
        if self.enable_persistence:
            self._remove_from_disk(cache_key)
        
        logger.debug("Cache entry invalidated", key=key)
        return True
    
    def clear(self) -> None:
        """Clear all cache entries"""
        self._memory_cache.clear()
        self._current_memory_usage = 0
        
        if self.enable_persistence:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
        
        logger.info("Cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self._stats['hits'] + self._stats['misses']
        hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            **self._stats,
            'hit_rate': hit_rate,
            'memory_usage_mb': self._current_memory_usage / 1024 / 1024,
            'memory_entries': len(self._memory_cache)
        }
    
    def _hash_key(self, key: str) -> str:
        """Create hash of cache key"""
        return hashlib.sha256(key.encode()).hexdigest()[:16]
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """Check if cache entry is expired"""
        if entry.ttl is None:
            return False
        return time.time() - entry.created_at > entry.ttl
    
    def _calculate_size(self, value: Any) -> int:
        """Estimate size of value in bytes"""
        try:
            if isinstance(value, (str, bytes)):
                return len(value.encode() if isinstance(value, str) else value)
            elif isinstance(value, (list, dict)):
                return len(json.dumps(value, default=str).encode())
            else:
                return len(str(value).encode())
        except Exception:
            return 1024  # Default estimate
    
    def _evict_lru(self) -> None:
        """Evict least recently used items to make space"""
        if not self._memory_cache:
            return
        
        # Sort by access time (oldest first)
        sorted_entries = sorted(
            self._memory_cache.items(),
            key=lambda x: x[1].accessed_at
        )
        
        # Evict oldest 25% of entries
        evict_count = max(1, len(sorted_entries) // 4)
        
        for i in range(evict_count):
            cache_key, entry = sorted_entries[i]
            self._remove_from_memory(cache_key)
            self._stats['evictions'] += 1
            
            logger.debug("LRU eviction", 
                        key=entry.key,
                        age_seconds=time.time() - entry.created_at,
                        access_count=entry.access_count)
    
    def _remove_from_memory(self, cache_key: str) -> None:
        """Remove entry from memory cache"""
        if cache_key in self._memory_cache:
            entry = self._memory_cache.pop(cache_key)
            self._current_memory_usage -= entry.size_bytes
            self._stats['memory_usage'] = self._current_memory_usage
    
    def _save_to_disk(self, cache_key: str, value: Any, entry: CacheEntry) -> bool:
        """Save cache entry to disk"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.cache"
            cache_data = {
                'value': value,
                'metadata': asdict(entry)
            }
            
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, default=str)
            
            return True
        except Exception as e:
            logger.error("Failed to save cache to disk", key=cache_key, error=str(e))
            return False
    
    def _get_from_disk(self, cache_key: str) -> Optional[Any]:
        """Load cache entry from disk"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.cache"
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            
            # Check TTL
            metadata = cache_data['metadata']
            if metadata.get('ttl') and time.time() - metadata['created_at'] > metadata['ttl']:
                cache_file.unlink()  # Remove expired file
                return None
            
            return cache_data['value']
        except Exception as e:
            logger.error("Failed to load cache from disk", key=cache_key, error=str(e))
            return None
    
    def _remove_from_disk(self, cache_key: str) -> None:
        """Remove cache file from disk"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.cache"
            if cache_file.exists():
                cache_file.unlink()
        except Exception as e:
            logger.error("Failed to remove cache file", key=cache_key, error=str(e))


# Global cache instance
_global_cache = None


def get_cache() -> IntelligentCache:
    """Get global cache instance"""
    global _global_cache
    if _global_cache is None:
        _global_cache = IntelligentCache()
    return _global_cache
