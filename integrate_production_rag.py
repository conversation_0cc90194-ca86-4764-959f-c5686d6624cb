#!/usr/bin/env python3
"""
Integrate Production-Grade RAG System into Existing Codebase
"""

import os
import sys
import shutil
from pathlib import Path
import importlib
import inspect
import asyncio
from typing import Dict, List, Any, Optional

# Add the project root to the path
sys.path.append('.')

def create_backup(file_path: str) -> bool:
    """Create backup of original file"""
    try:
        backup_path = f"{file_path}.bak"
        shutil.copy2(file_path, backup_path)
        print(f"✅ Created backup: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create backup for {file_path}: {e}")
        return False

def update_embedding_service():
    """Update embedding service with production-grade implementation"""
    print("\n🔄 Updating Embedding Service...")
    
    # Source and target paths
    source_file = "production_rag_system.py"
    target_dir = "docqa/vector_store"
    target_file = f"{target_dir}/production_embeddings.py"
    
    try:
        # Create backup of original file
        original_file = f"{target_dir}/embeddings.py"
        if os.path.exists(original_file):
            create_backup(original_file)
        
        # Extract ProductionEmbeddingService class from production_rag_system.py
        with open(source_file, 'r') as f:
            source_code = f.read()
        
        # Find the class definition
        start_marker = "class ProductionEmbeddingService:"
        end_marker = "class ProductionVectorStore:"
        
        start_idx = source_code.find(start_marker)
        end_idx = source_code.find(end_marker)
        
        if start_idx == -1 or end_idx == -1:
            print("❌ Could not find ProductionEmbeddingService class in source file")
            return False
        
        # Extract the class code
        class_code = source_code[start_idx:end_idx].strip()
        
        # Create the new file
        os.makedirs(os.path.dirname(target_file), exist_ok=True)
        
        with open(target_file, 'w') as f:
            f.write("""\"\"\"
Production-Grade Embedding Service
Using text-embedding-3-small with proper normalization and validation
\"\"\"

import os
import time
import numpy as np
import openai
import re
from typing import List, Dict, Any, Optional
import structlog

from ..config import get_config

logger = structlog.get_logger()

""")
            f.write(class_code)
        
        print(f"✅ Created {target_file}")
        
        # Update __init__.py to expose the new class
        init_file = f"{target_dir}/__init__.py"
        
        if os.path.exists(init_file):
            with open(init_file, 'r') as f:
                init_content = f.read()
            
            # Add import if not already present
            if "from .production_embeddings import ProductionEmbeddingService" not in init_content:
                with open(init_file, 'a') as f:
                    f.write("\n# Production-grade implementation\n")
                    f.write("from .production_embeddings import ProductionEmbeddingService\n")
                print(f"✅ Updated {init_file}")
        
        return True
    
    except Exception as e:
        print(f"❌ Failed to update embedding service: {e}")
        return False

def update_vector_store():
    """Update vector store with production-grade implementation"""
    print("\n🔄 Updating Vector Store...")
    
    # Source and target paths
    source_file = "production_rag_system.py"
    target_dir = "docqa/vector_store"
    target_file = f"{target_dir}/production_vector_store.py"
    
    try:
        # Create backup of original file
        original_file = f"{target_dir}/pgvector_store.py"
        if os.path.exists(original_file):
            create_backup(original_file)
        
        # Extract ProductionVectorStore class from production_rag_system.py
        with open(source_file, 'r') as f:
            source_code = f.read()
        
        # Find the class definition
        start_marker = "class ProductionVectorStore:"
        end_marker = "async def main():"
        
        start_idx = source_code.find(start_marker)
        end_idx = source_code.find(end_marker)
        
        if start_idx == -1 or end_idx == -1:
            print("❌ Could not find ProductionVectorStore class in source file")
            return False
        
        # Extract the class code
        class_code = source_code[start_idx:end_idx].strip()
        
        # Create the new file
        os.makedirs(os.path.dirname(target_file), exist_ok=True)
        
        with open(target_file, 'w') as f:
            f.write("""\"\"\"
Production-Grade Vector Store
Using pgvector with proper similarity calculation and metadata
\"\"\"

import os
import time
import psycopg2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import structlog
from dataclasses import dataclass

from ..config import get_config

logger = structlog.get_logger()

""")
            # Add RetrievalResult class
            retrieval_result_code = """
@dataclass
class RetrievalResult:
    \"\"\"Clean retrieval result\"\"\"
    chunk_id: str
    text: str
    similarity_score: float
    metadata: Dict[str, Any]
    source_info: str
"""
            f.write(retrieval_result_code)
            f.write("\n\n")
            f.write(class_code)
        
        print(f"✅ Created {target_file}")
        
        # Update __init__.py to expose the new class
        init_file = f"{target_dir}/__init__.py"
        
        if os.path.exists(init_file):
            with open(init_file, 'r') as f:
                init_content = f.read()
            
            # Add import if not already present
            if "from .production_vector_store import ProductionVectorStore, RetrievalResult" not in init_content:
                with open(init_file, 'a') as f:
                    f.write("\n# Production-grade implementation\n")
                    f.write("from .production_vector_store import ProductionVectorStore, RetrievalResult\n")
                print(f"✅ Updated {init_file}")
        
        return True
    
    except Exception as e:
        print(f"❌ Failed to update vector store: {e}")
        return False

def update_text_processing():
    """Update text processing with production-grade implementation"""
    print("\n🔄 Updating Text Processing...")
    
    # Source and target paths
    source_file = "production_rag_system.py"
    target_dir = "docqa/text_processing"
    target_file = f"{target_dir}/production_text_processor.py"
    
    try:
        # Create directory if it doesn't exist
        os.makedirs(target_dir, exist_ok=True)
        
        # Create backup of original file if it exists
        original_file = f"{target_dir}/text_processor.py"
        if os.path.exists(original_file):
            create_backup(original_file)
        
        # Extract ProductionTextNormalizer and ProductionChunker classes
        with open(source_file, 'r') as f:
            source_code = f.read()
        
        # Find the class definitions
        normalizer_start = "class ProductionTextNormalizer:"
        normalizer_end = "class ProductionChunker:"
        chunker_end = "class ProductionEmbeddingService:"
        
        normalizer_start_idx = source_code.find(normalizer_start)
        normalizer_end_idx = source_code.find(normalizer_end)
        chunker_end_idx = source_code.find(chunker_end)
        
        if normalizer_start_idx == -1 or normalizer_end_idx == -1 or chunker_end_idx == -1:
            print("❌ Could not find text processing classes in source file")
            return False
        
        # Extract the class code
        normalizer_code = source_code[normalizer_start_idx:normalizer_end_idx].strip()
        chunker_code = source_code[normalizer_end_idx:chunker_end_idx].strip()
        
        # Create the new file
        with open(target_file, 'w') as f:
            f.write("""\"\"\"
Production-Grade Text Processing
With proper normalization, chunking, and validation
\"\"\"

import re
import uuid
import numpy as np
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import structlog

logger = structlog.get_logger()

@dataclass
class DocumentChunk:
    \"\"\"Clean document chunk with proper embedding\"\"\"
    id: str
    text: str
    embedding: List[float]  # Always 1536-dim for text-embedding-3-small
    metadata: Dict[str, Any]
    token_count: int
    
    def __post_init__(self):
        \"\"\"Validate embedding dimensions\"\"\"
        if self.embedding and len(self.embedding) != 1536:
            raise ValueError(f"Invalid embedding dimension: {len(self.embedding)}, expected 1536")

""")
            f.write(normalizer_code)
            f.write("\n\n")
            f.write(chunker_code)
        
        print(f"✅ Created {target_file}")
        
        # Create or update __init__.py
        init_file = f"{target_dir}/__init__.py"
        
        if not os.path.exists(init_file):
            with open(init_file, 'w') as f:
                f.write("""\"\"\"
Text processing module for document chunking and normalization
\"\"\"

from .production_text_processor import ProductionTextNormalizer, ProductionChunker, DocumentChunk

__all__ = ["ProductionTextNormalizer", "ProductionChunker", "DocumentChunk"]
""")
        else:
            with open(init_file, 'r') as f:
                init_content = f.read()
            
            # Add import if not already present
            if "from .production_text_processor import" not in init_content:
                with open(init_file, 'a') as f:
                    f.write("\n# Production-grade implementation\n")
                    f.write("from .production_text_processor import ProductionTextNormalizer, ProductionChunker, DocumentChunk\n")
            
            print(f"✅ Updated {init_file}")
        
        return True
    
    except Exception as e:
        print(f"❌ Failed to update text processing: {e}")
        return False

def update_qa_system():
    """Update QA system with production-grade implementation"""
    print("\n🔄 Updating QA System...")
    
    # Source and target paths
    source_file = "production_qa_system.py"
    target_dir = "docqa"
    target_file = f"{target_dir}/production_qa.py"
    
    try:
        # Create backup of original file
        original_file = f"{target_dir}/ask.py"
        if os.path.exists(original_file):
            create_backup(original_file)
        
        # Copy the entire file
        shutil.copy2(source_file, target_file)
        print(f"✅ Created {target_file}")
        
        # Update central_api.py to use the production QA system
        central_api_file = f"{target_dir}/central_api.py"
        
        if os.path.exists(central_api_file):
            create_backup(central_api_file)
            
            # Add import to central_api.py
            with open(central_api_file, 'r') as f:
                content = f.read()
            
            # Add import if not already present
            if "from .production_qa import ProductionQASystem" not in content:
                import_idx = content.find("import ")
                if import_idx != -1:
                    # Find the end of the import section
                    lines = content.split("\n")
                    import_end_idx = 0
                    for i, line in enumerate(lines):
                        if line.startswith("import ") or line.startswith("from "):
                            import_end_idx = i
                    
                    # Insert our import after the last import
                    lines.insert(import_end_idx + 1, "from .production_qa import ProductionQASystem")
                    
                    # Update the content
                    content = "\n".join(lines)
                    
                    # Write back to file
                    with open(central_api_file, 'w') as f:
                        f.write(content)
                    
                    print(f"✅ Updated imports in {central_api_file}")
        
        return True
    
    except Exception as e:
        print(f"❌ Failed to update QA system: {e}")
        return False

def create_integration_module():
    """Create integration module to use production-grade components"""
    print("\n🔄 Creating Integration Module...")
    
    target_dir = "docqa"
    target_file = f"{target_dir}/production_integration.py"
    
    try:
        with open(target_file, 'w') as f:
            f.write("""\"\"\"
Production-Grade RAG Integration Module
Provides easy access to all production-grade components
\"\"\"

import structlog
from typing import Dict, List, Any, Optional

# Import production-grade components
from .text_processing.production_text_processor import (
    ProductionTextNormalizer,
    ProductionChunker,
    DocumentChunk
)
from .vector_store.production_embeddings import ProductionEmbeddingService
from .vector_store.production_vector_store import ProductionVectorStore, RetrievalResult
from .production_qa import ProductionQASystem

logger = structlog.get_logger()

class ProductionRAGSystem:
    \"\"\"
    Production-Grade RAG System Integration
    
    This class provides a unified interface to all production-grade components:
    - Text normalization and chunking
    - Embedding generation
    - Vector storage and retrieval
    - Question answering
    
    Usage:
        rag_system = ProductionRAGSystem()
        
        # Process document
        chunks = rag_system.process_document(text_content)
        
        # Store document
        rag_system.store_document(franchisor_id, chunks)
        
        # Answer questions
        answer = await rag_system.answer_question(question, franchisor_id)
    \"\"\"
    
    def __init__(self):
        self.text_normalizer = ProductionTextNormalizer()
        self.chunker = ProductionChunker(chunk_size=400, chunk_overlap=75)
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        self.qa_system = ProductionQASystem()
        
        logger.info("Production RAG System initialized")
    
    def process_document(
        self, 
        text_content: str, 
        metadata: Dict[str, Any] = None
    ) -> List[DocumentChunk]:
        \"\"\"
        Process document text into chunks with embeddings
        
        Args:
            text_content: Raw document text
            metadata: Additional metadata
            
        Returns:
            List of document chunks with embeddings
        \"\"\"
        # Normalize text
        normalized_text = self.text_normalizer.normalize_text(text_content)
        
        # Create chunks
        chunks = self.chunker.chunk_text(normalized_text, metadata)
        
        # Generate embeddings
        for chunk in chunks:
            chunk.embedding = self.embedding_service.generate_embedding(chunk.text)
        
        return chunks
    
    def store_document(
        self,
        franchisor_id: str,
        text_content: str,
        chunks: List[DocumentChunk] = None
    ) -> bool:
        \"\"\"
        Store document in vector store
        
        Args:
            franchisor_id: Franchisor ID
            text_content: Full document text
            chunks: Optional pre-processed chunks
            
        Returns:
            True if successful
        \"\"\"
        # Process document if chunks not provided
        if chunks is None:
            chunks = self.process_document(text_content)
        
        # Generate embedding for full text
        embedding = self.embedding_service.generate_embedding(text_content)
        
        # Store in vector store
        return self.vector_store.store_franchisor_embedding(
            franchisor_id=franchisor_id,
            text_content=text_content,
            embedding=embedding,
            metadata={"chunk_count": len(chunks)}
        )
    
    async def answer_question(
        self,
        question: str,
        franchisor_id: Optional[str] = None,
        similarity_threshold: float = 0.5,
        top_k: int = 5,
        temperature: float = 0.1
    ) -> Dict[str, Any]:
        \"\"\"
        Answer question using production-grade RAG
        
        Args:
            question: User question
            franchisor_id: Optional franchisor filter
            similarity_threshold: Minimum similarity score
            top_k: Number of chunks to retrieve
            temperature: Generation temperature
            
        Returns:
            Dict with answer, sources, and metadata
        \"\"\"
        return await self.qa_system.answer_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )

# Create singleton instance
production_rag = ProductionRAGSystem()
\"\"\"
Singleton instance of ProductionRAGSystem for easy import
\"\"\"
""")
        
        print(f"✅ Created {target_file}")
        
        # Update __init__.py to expose the integration module
        init_file = f"{target_dir}/__init__.py"
        
        if os.path.exists(init_file):
            with open(init_file, 'r') as f:
                init_content = f.read()
            
            # Add import if not already present
            if "from .production_integration import production_rag" not in init_content:
                with open(init_file, 'a') as f:
                    f.write("\n# Production-grade RAG system\n")
                    f.write("from .production_integration import production_rag, ProductionRAGSystem\n")
                    f.write("\n__all__ = [\"ask_question\", \"production_rag\", \"ProductionRAGSystem\"]\n")
                print(f"✅ Updated {init_file}")
        
        return True
    
    except Exception as e:
        print(f"❌ Failed to create integration module: {e}")
        return False

def create_usage_examples():
    """Create usage examples for the production-grade RAG system"""
    print("\n🔄 Creating Usage Examples...")
    
    example_file = "production_rag_examples.py"
    
    try:
        with open(example_file, 'w') as f:
            f.write("""#!/usr/bin/env python3
\"\"\"
Production-Grade RAG System Usage Examples
\"\"\"

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.append('.')

async def example_document_processing():
    \"\"\"Example of document processing with production-grade RAG\"\"\"
    from docqa.production_integration import production_rag
    
    print("🚀 Example: Document Processing")
    print("=" * 50)
    
    # Sample document
    document_path = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    # Read document
    with open(document_path, 'rb') as f:
        pdf_content = f.read()
    
    # Process PDF
    from PyPDF2 import PdfReader
    from io import BytesIO
    
    reader = PdfReader(BytesIO(pdf_content))
    text_content = ""
    
    # Extract text from each page
    for i, page in enumerate(reader.pages):
        page_text = page.extract_text()
        if page_text:
            text_content += f"\\n\\n--- Page {i+1} ---\\n\\n{page_text}"
    
    print(f"✅ Extracted {len(text_content)} characters from PDF")
    
    # Process document
    chunks = production_rag.process_document(
        text_content=text_content,
        metadata={
            'source': Path(document_path).name,
            'franchisor_id': franchisor_id
        }
    )
    
    print(f"✅ Created {len(chunks)} chunks with embeddings")
    
    # Store document
    result = production_rag.store_document(
        franchisor_id=franchisor_id,
        text_content=text_content,
        chunks=chunks
    )
    
    print(f"✅ Document stored: {result}")
    
    return chunks

async def example_question_answering():
    \"\"\"Example of question answering with production-grade RAG\"\"\"
    from docqa.production_integration import production_rag
    
    print("\\n🚀 Example: Question Answering")
    print("=" * 50)
    
    # Sample questions
    questions = [
        "What is Coochie Hydrogreen?",
        "Where is Coochie Hydrogreen located?",
        "What services does Coochie Hydrogreen provide?",
        "What are the franchise fees?"
    ]
    
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    for i, question in enumerate(questions, 1):
        print(f"\\n{i}. Question: {question}")
        
        # Answer question
        result = await production_rag.answer_question(
            question=question,
            franchisor_id=franchisor_id,
            similarity_threshold=0.5,
            top_k=5,
            temperature=0.1
        )
        
        if result['success']:
            print(f"   Answer: {result['answer']}")
            
            if result['sources']:
                print(f"   Top Source: {result['sources'][0]['text'][:100]}...")
                print(f"   Similarity: {result['sources'][0]['similarity_score']:.4f}")
        else:
            print(f"   Error: {result['error']}")

async def example_direct_api_usage():
    \"\"\"Example of direct API usage with production-grade components\"\"\"
    print("\\n🚀 Example: Direct API Usage")
    print("=" * 50)
    
    # Import individual components
    from docqa.text_processing.production_text_processor import ProductionTextNormalizer, ProductionChunker
    from docqa.vector_store.production_embeddings import ProductionEmbeddingService
    from docqa.vector_store.production_vector_store import ProductionVectorStore
    
    # Initialize components
    normalizer = ProductionTextNormalizer()
    chunker = ProductionChunker(chunk_size=400, chunk_overlap=75)
    embedding_service = ProductionEmbeddingService()
    vector_store = ProductionVectorStore()
    
    # Sample text
    text = \"\"\"
    Coochie Hydrogreen is a franchisor based in Australia.
    The company specializes in lawn care and maintenance services.
    \"\"\"
    
    # Normalize text
    normalized_text = normalizer.normalize_text(text)
    print(f"✅ Normalized text: {normalized_text[:50]}...")
    
    # Create chunks
    chunks = chunker.chunk_text(normalized_text)
    print(f"✅ Created {len(chunks)} chunks")
    
    # Generate embeddings
    for chunk in chunks:
        chunk.embedding = embedding_service.generate_embedding(chunk.text)
    print(f"✅ Generated embeddings")
    
    # Generate query embedding
    query = "What is Coochie Hydrogreen?"
    query_embedding = embedding_service.generate_embedding(query)
    print(f"✅ Generated query embedding")
    
    # Search for similar content
    results = vector_store.search_similar(
        query_embedding=query_embedding,
        top_k=3,
        similarity_threshold=0.5
    )
    
    print(f"✅ Found {len(results)} similar results")
    for i, result in enumerate(results, 1):
        print(f"   {i}. Score: {result.similarity_score:.4f} - {result.text[:50]}...")

async def main():
    \"\"\"Run all examples\"\"\"
    print("🚀 Production-Grade RAG System Examples")
    print("=" * 60)
    
    # Run examples
    await example_document_processing()
    await example_question_answering()
    await example_direct_api_usage()
    
    print("\\n✅ All examples completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
""")
        
        print(f"✅ Created {example_file}")
        return True
    
    except Exception as e:
        print(f"❌ Failed to create usage examples: {e}")
        return False

async def main():
    """Main function to integrate production-grade RAG system"""
    print("🚀 Integrating Production-Grade RAG System")
    print("=" * 60)
    
    # Update components
    update_embedding_service()
    update_vector_store()
    update_text_processing()
    update_qa_system()
    create_integration_module()
    create_usage_examples()
    
    print("\n🎉 Integration completed!")
    print("You can now use the production-grade RAG system in your codebase.")
    print("See production_rag_examples.py for usage examples.")

if __name__ == "__main__":
    asyncio.run(main())
