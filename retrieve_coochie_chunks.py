#!/usr/bin/env python3
"""
Retrieve actual document chunks from Coochie Hydrogreen document
and analyze them for specific questions
"""

import asyncio
import sys
import json
from typing import Dict, List, Any, Optional

# Add the project root to the path
sys.path.append('.')

async def retrieve_document_chunks(question: str, top_k: int = 5, similarity_threshold: float = 0.01):
    """Retrieve document chunks for a specific question"""
    try:
        from docqa.vector_store import PgVectorStore, EmbeddingService
        
        # Initialize services
        vector_store = PgVectorStore()
        embedding_service = EmbeddingService()
        
        # Generate embedding for the question
        question_embedding = embedding_service.generate_embedding(question)
        
        # Franchisor ID for Coochie Hydrogreen
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Search for relevant chunks
        search_results = vector_store.search_similar(
            query_embedding=question_embedding,
            top_k=top_k,
            similarity_threshold=similarity_threshold,
            table_priority=['franchisors']
        )
        
        return search_results
        
    except Exception as e:
        print(f"Error retrieving chunks: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


async def analyze_questions():
    """Analyze each question with retrieved chunks"""
    questions = [
        "What training does the business provide?",
        "Describe business history.",
        "What is FRANCHISEE APPROVAL PROCESS?",
        "What are the franchisee fees?",
        "What are the franchisee marketing fees?",
        "What are the specialised Coochie Hydrogreen franchise?"
    ]
    
    print("🔍 Retrieving and analyzing document chunks for each question...")
    print("=" * 80)
    
    all_results = {}
    
    for i, question in enumerate(questions, 1):
        print(f"\n📝 Question {i}: {question}")
        
        # Retrieve chunks with optimal parameters
        chunks = await retrieve_document_chunks(
            question=question,
            top_k=10,  # Get more chunks to analyze
            similarity_threshold=0.01  # Very low threshold to find any relevant content
        )
        
        if chunks:
            print(f"   Found {len(chunks)} relevant chunks")
            
            # Display top 5 chunks with scores
            print("\n   Top chunks:")
            for j, chunk in enumerate(chunks[:5], 1):
                similarity = chunk.get('similarity_score', 0)
                content = chunk.get('content', chunk.get('text', ''))
                content_preview = content[:150].replace('\n', ' ')
                
                print(f"   {j}. Score: {similarity:.4f} - {content_preview}...")
            
            # Store results
            all_results[question] = {
                'chunks': chunks[:5],  # Store top 5 chunks
                'found': len(chunks) > 0
            }
        else:
            print("   ❌ No relevant chunks found")
            all_results[question] = {
                'chunks': [],
                'found': False
            }
    
    # Save results to file for analysis
    with open('coochie_chunks_analysis.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print("\n" + "=" * 80)
    print("✅ Analysis complete! Results saved to coochie_chunks_analysis.json")
    
    return all_results


async def extract_full_content():
    """Extract the full content of the document"""
    try:
        from docqa.vector_store import PgVectorStore
        
        vector_store = PgVectorStore()
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        # Get all content with dummy embedding (will return all chunks)
        search_results = vector_store.search_similar(
            query_embedding=[0.0] * 1536,  # Dummy embedding
            top_k=100,  # Get all chunks
            similarity_threshold=0.0,  # Get everything
            table_priority=['franchisors']
        )
        
        if search_results:
            print(f"\n📄 Found {len(search_results)} total chunks in the document")
            
            # Combine all content
            full_content = ""
            for result in search_results:
                content = result.get('content', result.get('text', ''))
                full_content += content + "\n\n"
            
            # Save full content to file
            with open('coochie_full_content.txt', 'w') as f:
                f.write(full_content)
            
            print(f"   Full content saved to coochie_full_content.txt ({len(full_content)} characters)")
            print(f"   Preview: {full_content[:200]}...")
            
            return full_content
        else:
            print("   ❌ No content found in the document")
            return ""
            
    except Exception as e:
        print(f"Error extracting full content: {str(e)}")
        import traceback
        traceback.print_exc()
        return ""


async def main():
    """Main function"""
    print("🚀 Starting comprehensive document analysis...")
    
    # Extract full content first
    full_content = await extract_full_content()
    
    # Then analyze specific questions
    results = await analyze_questions()
    
    print("\n🎯 Analysis completed!")
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
