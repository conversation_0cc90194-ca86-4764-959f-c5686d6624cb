#!/usr/bin/env python3
"""
Production-grade document ingestion system with chart extraction and vector storage.

This is the main CLI interface for the document ingestion system.
Supports multiple file formats, chart extraction, OCR, language detection,
translation, and vector storage.

Usage:
    python ingest.py <path-or-s3-url> [--translate] [--extract-charts]
    python ingest.py --help

Examples:
    python ingest.py document.pdf --extract-charts
    python ingest.py s3://bucket/document.docx --translate --target-language en
    python ingest.py folder/ --recursive --extract-charts
"""

import argparse
import sys
import time
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError
from pathlib import Path
from typing import List

# Add the ingest package to the path
sys.path.insert(0, str(Path(__file__).parent))

from ingest.core import (
    DocumentChunk,
    IngestionConfig,
    LanguageCode,
    ProcessingResult,
)
from ingest.file_loader import FileLoader
from ingest.vector_store import VectorStore
from ingest.chart_extract import ChartExtractorImpl
from ingest.file_handlers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Excel<PERSON><PERSON><PERSON>,
    H<PERSON><PERSON><PERSON><PERSON><PERSON>,
    ImageHandler,
    <PERSON><PERSON><PERSON><PERSON>,
    PPTXHandler,
    TextHandler,
    WordHandler,
    ZipHandler,
)
from ingest.gpt_processor import GPTContentProcessor
from ingest.ocr import LanguageProcessorImpl, OCRProcessorImpl
from ingest.utils import get_logger, setup_logging


class DocumentIngestionSystem:
    """
    Main document ingestion system orchestrating all components.
    
    Handles:
    - File loading and validation
    - Content extraction using appropriate handlers
    - Chart detection and captioning
    - Language detection and translation
    - Text chunking and vector embedding
    - Concurrent processing with timeouts
    """
    
    def __init__(self, config: IngestionConfig):
        """
        Initialize the ingestion system.
        
        Args:
            config: Ingestion configuration
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # Initialize components with maximum OpenAI integration
        self.file_loader = FileLoader(config)
        self.chart_extractor = ChartExtractorImpl(
            openai_api_key=config.openai_api_key,
            vision_model=config.openai_vision_model
        ) if config.extract_charts else None
        self.language_processor = LanguageProcessorImpl(
            openai_api_key=config.openai_api_key,
            translation_model=config.openai_model
        ) if config.translate else None
        self.ocr_processor = OCRProcessorImpl()

        # Initialize GPT content processor for maximum OpenAI usage
        self.gpt_processor = GPTContentProcessor(
            api_key=config.openai_api_key,
            model=config.openai_model
        ) if config.use_gpt_for_text_analysis else None

        # Initialize file handlers
        self.handlers = {
            'pdf': PDFHandler(),
            'word': WordHandler(),
            'pptx': PPTXHandler(),
            'text': TextHandler(),
            'csv': CSVHandler(),
            'excel': ExcelHandler(),
            'html': HTMLHandler(),
            'image': ImageHandler(),
            'zip': ZipHandler(),
        }

        # Initialize vector store with maximum OpenAI integration
        index_path = Path.home() / ".docqa" / "index"
        self.vector_store = VectorStore(
            index_path=index_path,
            openai_api_key=config.openai_api_key,
            embedding_model=config.openai_embedding_model,
            gpt_model=config.openai_model
        )
        
        self.logger.info(
            "Document ingestion system initialized",
            config=config.model_dump(),
            handlers_available=list(self.handlers.keys()),
            chart_extraction=config.extract_charts,
            translation=config.translate,
        )
    
    def ingest_document(self, source: str) -> ProcessingResult:
        """
        Ingest a single document.
        
        Args:
            source: File path or S3 URL
            
        Returns:
            Processing result
        """
        start_time = time.time()
        
        try:
            self.logger.info(
                "Starting document ingestion",
                source=source,
                config=self.config.model_dump(),
            )
            
            # Load file
            file_path = self.file_loader.load_file(source)
            
            try:
                # Determine file type and select handler
                file_type = self.file_loader.get_file_type(file_path)
                handler = self._get_handler_for_file_type(file_type)
                
                if not handler:
                    raise ValueError(f"No handler available for file type: {file_type}")
                
                # Extract content with timeout
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(handler.extract_content, file_path, self.config)
                    
                    try:
                        result = future.result(timeout=self.config.timeout_seconds)
                    except TimeoutError:
                        raise TimeoutError(f"Content extraction timed out after {self.config.timeout_seconds} seconds")
                
                if not result.success:
                    return result
                
                # Process chunks
                processed_chunks = self._process_chunks(result.chunks, str(file_path))
                
                # Update result
                result.chunks = processed_chunks
                result.processing_time = time.time() - start_time
                
                # Store in vector database
                if processed_chunks:
                    self._store_vectors(processed_chunks)
                
                self.logger.info(
                    "Document ingestion completed successfully",
                    source=source,
                    chunks_created=len(processed_chunks),
                    processing_time=result.processing_time,
                )
                
                return result
                
            finally:
                # Clean up temporary files
                if self.file_loader.is_s3_url(source):
                    self.file_loader.cleanup_temp_file(file_path)
        
        except Exception as e:
            processing_time = time.time() - start_time
            
            self.logger.error(
                "Document ingestion failed",
                source=source,
                error=str(e),
                processing_time=processing_time,
            )
            
            return ProcessingResult(
                success=False,
                file_path=source,
                error_message=str(e),
                processing_time=processing_time,
            )
    
    def ingest_multiple(self, sources: List[str]) -> List[ProcessingResult]:
        """
        Ingest multiple documents concurrently.
        
        Args:
            sources: List of file paths or S3 URLs
            
        Returns:
            List of processing results
        """
        self.logger.info(
            "Starting batch ingestion",
            total_files=len(sources),
            concurrent_workers=self.config.concurrent_workers,
        )
        
        results = []
        
        with ThreadPoolExecutor(max_workers=self.config.concurrent_workers) as executor:
            # Submit all tasks
            futures = {
                executor.submit(self.ingest_document, source): source
                for source in sources
            }
            
            # Collect results
            for future in futures:
                source = futures[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    self.logger.error(
                        "Failed to process document in batch",
                        source=source,
                        error=str(e),
                    )
                    results.append(ProcessingResult(
                        success=False,
                        file_path=source,
                        error_message=str(e),
                    ))
        
        successful = sum(1 for r in results if r.success)
        self.logger.info(
            "Batch ingestion completed",
            total_files=len(sources),
            successful=successful,
            failed=len(sources) - successful,
        )
        
        return results
    
    def _get_handler_for_file_type(self, file_type):
        """Get appropriate handler for file type."""
        from ingest.core import FileType
        
        if file_type == FileType.PDF:
            return self.handlers['pdf']
        elif file_type in [FileType.DOCX, FileType.DOC]:
            return self.handlers['word']
        elif file_type in [FileType.PPTX, FileType.PPT]:
            return self.handlers['pptx']
        elif file_type in [FileType.TXT, FileType.MD]:
            return self.handlers['text']
        elif file_type == FileType.CSV:
            return self.handlers['csv']
        elif file_type in [FileType.XLSX, FileType.XLS]:
            return self.handlers['excel']
        elif file_type in [FileType.HTML, FileType.HTM]:
            return self.handlers['html']
        elif file_type in [FileType.JPG, FileType.JPEG, FileType.PNG, FileType.WEBP, FileType.GIF]:
            return self.handlers['image']
        elif file_type == FileType.ZIP:
            return self.handlers['zip']
        
        return None
    
    def _process_chunks(self, chunks: List[DocumentChunk], source_file: str) -> List[DocumentChunk]:
        """Process chunks with maximum OpenAI integration for analysis, translation, and enhancement."""
        processed_chunks = []

        for chunk in chunks:
            try:
                # Detect language using GPT if available, fallback to traditional method
                if self.language_processor:
                    chunk.language = self.language_processor.detect_language(chunk.text)

                # Translate if needed using OpenAI GPT
                if (self.config.translate and
                    self.language_processor and
                    chunk.language != self.config.target_language):

                    translated_text = self.language_processor.translate_text(
                        chunk.text,
                        self.config.target_language,
                        chunk.language,
                    )
                    chunk.text = translated_text
                    chunk.language = self.config.target_language

                # Enhance chunk with GPT analysis if enabled
                if self.config.use_gpt_for_text_analysis and self.gpt_processor:
                    chunk = self.gpt_processor.enhance_chunk(chunk)

                # Generate summary using GPT if enabled
                if self.config.use_gpt_for_summarization and self.gpt_processor:
                    summary = self.gpt_processor.generate_summary(chunk.text, max_length=100)
                    chunk.metadata['gpt_summary'] = summary

                # Classify content using GPT if enabled
                if self.config.use_gpt_for_classification and self.gpt_processor:
                    classification = self.gpt_processor.classify_content(chunk.text)
                    chunk.metadata['gpt_classification'] = classification

                # Generate metadata using GPT if enabled
                if self.config.use_gpt_for_metadata and self.gpt_processor:
                    metadata = self.gpt_processor.generate_metadata(chunk.text, source_file)
                    chunk.metadata['gpt_metadata'] = metadata

                # Extract charts using GPT Vision if enabled
                if (self.config.extract_charts and
                    self.chart_extractor and
                    '[IMAGE' in chunk.text):

                    self.logger.debug(
                        "Chart extraction with GPT Vision",
                        chunk_id=chunk.id,
                        source=source_file,
                    )

                processed_chunks.append(chunk)

            except Exception as e:
                self.logger.warning(
                    "Failed to process chunk with GPT",
                    chunk_id=chunk.id,
                    error=str(e),
                )
                # Add chunk without processing
                processed_chunks.append(chunk)

        return processed_chunks
    
    def _store_vectors(self, chunks: List[DocumentChunk]) -> None:
        """Store document chunks as vectors."""
        try:
            # Generate embeddings
            vector_entries = self.vector_store.embed_chunks(chunks)
            
            # Store in FAISS
            self.vector_store.store_in_faiss(vector_entries)
            
            self.logger.info(
                "Vectors stored successfully",
                chunks_processed=len(chunks),
                vectors_created=len(vector_entries),
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to store vectors",
                error=str(e),
                chunks_count=len(chunks),
            )


def create_config_from_args(args) -> IngestionConfig:
    """Create ingestion configuration from command line arguments with maximum OpenAI integration."""
    target_language = LanguageCode.ENGLISH
    if args.target_language:
        try:
            target_language = LanguageCode(args.target_language.lower())
        except ValueError:
            print(f"Warning: Unknown target language '{args.target_language}', using English")

    # Get OpenAI API key from environment
    import os
    openai_api_key = os.getenv('OPENAI_API_KEY')
    if not openai_api_key:
        print("Warning: OPENAI_API_KEY not found in environment variables")

    return IngestionConfig(
        # OpenAI Configuration
        openai_api_key=openai_api_key,
        openai_model=args.openai_model,
        openai_vision_model=args.openai_model,  # Use same model for vision
        openai_embedding_model=args.embedding_model,

        # AI Features (maximum OpenAI usage)
        extract_charts=args.extract_charts,
        translate=args.translate,
        target_language=target_language,
        use_gpt_for_text_analysis=args.use_gpt_analysis,
        use_gpt_for_summarization=args.use_gpt_summarization,
        use_gpt_for_classification=args.use_gpt_classification,
        use_gpt_for_metadata=args.use_gpt_metadata,
        use_gpt_for_quality_check=True,
        use_gpt_for_content_enhancement=True,

        # Vector Configuration
        generate_embeddings=True,
        embedding_batch_size=100,
        vector_similarity_threshold=0.7,
        use_semantic_search=True,
        store_vectors=True,

        # Processing Parameters
        chunk_size=args.chunk_size,
        chunk_overlap=args.chunk_overlap,
        max_file_size_mb=args.max_file_size,
        timeout_seconds=args.timeout,
        concurrent_workers=args.workers,
    )


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Production-grade document ingestion system",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )
    
    parser.add_argument(
        "source",
        help="File path, S3 URL, or directory to ingest",
    )
    
    parser.add_argument(
        "--extract-charts",
        action="store_true",
        help="Enable chart detection and AI captioning using GPT Vision",
    )

    parser.add_argument(
        "--translate",
        action="store_true",
        help="Enable automatic translation using OpenAI GPT",
    )

    parser.add_argument(
        "--target-language",
        default="en",
        help="Target language for translation (default: en)",
    )

    parser.add_argument(
        "--use-gpt-analysis",
        action="store_true",
        default=True,
        help="Enable GPT-powered content analysis (default: True)",
    )

    parser.add_argument(
        "--use-gpt-summarization",
        action="store_true",
        default=True,
        help="Enable GPT-powered summarization (default: True)",
    )

    parser.add_argument(
        "--use-gpt-classification",
        action="store_true",
        default=True,
        help="Enable GPT-powered content classification (default: True)",
    )

    parser.add_argument(
        "--use-gpt-metadata",
        action="store_true",
        default=True,
        help="Enable GPT-powered metadata generation (default: True)",
    )

    parser.add_argument(
        "--openai-model",
        default="gpt-4o",
        help="OpenAI GPT model to use (default: gpt-4o)",
    )

    parser.add_argument(
        "--embedding-model",
        default="text-embedding-3-small",
        help="OpenAI embedding model to use (default: text-embedding-3-small)",
    )
    
    parser.add_argument(
        "--chunk-size",
        type=int,
        default=400,
        help="Maximum tokens per chunk (default: 400)",
    )
    
    parser.add_argument(
        "--chunk-overlap",
        type=int,
        default=50,
        help="Token overlap between chunks (default: 50)",
    )
    
    parser.add_argument(
        "--max-file-size",
        type=int,
        default=100,
        help="Maximum file size in MB (default: 100)",
    )
    
    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="Processing timeout in seconds (default: 30)",
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=4,
        help="Number of concurrent workers (default: 4)",
    )
    
    parser.add_argument(
        "--recursive",
        action="store_true",
        help="Process directories recursively",
    )
    
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level (default: INFO)",
    )
    
    parser.add_argument(
        "--log-json",
        action="store_true",
        help="Output logs in JSON format",
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(
        level=args.log_level,
        format_json=args.log_json,
    )
    
    logger = get_logger(__name__)
    
    try:
        # Create configuration
        config = create_config_from_args(args)
        
        # Initialize ingestion system
        system = DocumentIngestionSystem(config)
        
        # Determine sources to process
        source_path = Path(args.source)
        sources = []
        
        if args.source.startswith('s3://'):
            sources = [args.source]
        elif source_path.is_file():
            sources = [str(source_path)]
        elif source_path.is_dir():
            if args.recursive:
                # Find all supported files recursively
                for pattern in ['*.pdf', '*.docx', '*.doc', '*.pptx', '*.ppt', 
                               '*.txt', '*.md', '*.csv', '*.html', '*.htm',
                               '*.jpg', '*.jpeg', '*.png', '*.webp', '*.gif', '*.zip']:
                    sources.extend(str(p) for p in source_path.rglob(pattern))
            else:
                # Find files in current directory only
                for pattern in ['*.pdf', '*.docx', '*.doc', '*.pptx', '*.ppt',
                               '*.txt', '*.md', '*.csv', '*.html', '*.htm', 
                               '*.jpg', '*.jpeg', '*.png', '*.webp', '*.gif', '*.zip']:
                    sources.extend(str(p) for p in source_path.glob(pattern))
        else:
            logger.error("Source not found or not supported", source=args.source)
            sys.exit(1)
        
        if not sources:
            logger.error("No supported files found", source=args.source)
            sys.exit(1)
        
        logger.info(
            "Starting ingestion",
            total_sources=len(sources),
            config=config.model_dump(),
        )
        
        # Process documents
        if len(sources) == 1:
            result = system.ingest_document(sources[0])
            if result.success:
                print(f"✅ Successfully processed: {result.file_path}")
                print(f"   Chunks created: {len(result.chunks)}")
                print(f"   Processing time: {result.processing_time:.2f}s")
            else:
                print(f"❌ Failed to process: {result.file_path}")
                print(f"   Error: {result.error_message}")
                sys.exit(1)
        else:
            results = system.ingest_multiple(sources)
            
            successful = [r for r in results if r.success]
            failed = [r for r in results if not r.success]
            
            print("\n📊 Batch Processing Results:")
            print(f"   Total files: {len(sources)}")
            print(f"   ✅ Successful: {len(successful)}")
            print(f"   ❌ Failed: {len(failed)}")
            
            if failed:
                print("\n❌ Failed files:")
                for result in failed:
                    print(f"   {result.file_path}: {result.error_message}")
            
            total_chunks = sum(len(r.chunks) for r in successful)
            print(f"\n📄 Total chunks created: {total_chunks}")
            
            # Show vector store stats
            stats = system.vector_store.get_stats()
            print(f"🔍 Vector store: {stats['total_vectors']} vectors indexed")
        
    except KeyboardInterrupt:
        logger.info("Ingestion interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error("Ingestion failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
