#!/usr/bin/env python3
"""
Production-Grade RAG System Implementation
Following best practices for vector storage and retrieval
"""

import asyncio
import sys
import json
import re
import time
import uuid
import numpy as np
import psycopg2
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# Add the project root to the path
sys.path.append('.')

@dataclass
class DocumentChunk:
    """Clean document chunk with proper embedding"""
    id: str
    text: str
    embedding: List[float]  # Always 1536-dim for text-embedding-3-small
    metadata: Dict[str, Any]
    token_count: int
    
    def __post_init__(self):
        """Validate embedding dimensions"""
        if self.embedding and len(self.embedding) != 1536:
            raise ValueError(f"Invalid embedding dimension: {len(self.embedding)}, expected 1536")

@dataclass
class RetrievalResult:
    """Clean retrieval result"""
    chunk_id: str
    text: str
    similarity_score: float
    metadata: Dict[str, Any]
    source_info: str

class ProductionTextNormalizer:
    """Production-grade text normalization"""
    
    @staticmethod
    def normalize_text(text: str) -> str:
        """
        Normalize text following best practices:
        - Lowercase conversion
        - Remove extra whitespace
        - Clean special characters
        - Preserve meaningful punctuation
        """
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text)
        
        # Remove non-printable characters but preserve newlines and tabs
        text = re.sub(r'[^\x20-\x7E\n\t]', '', text)
        
        # Clean up multiple punctuation
        text = re.sub(r'[.]{2,}', '.', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        return text
    
    @staticmethod
    def is_meaningful_text(text: str, min_length: int = 10) -> bool:
        """Check if text is meaningful (not just stopwords or very short)"""
        if not text or len(text.strip()) < min_length:
            return False
        
        # Check if it's not just punctuation or numbers
        meaningful_chars = re.sub(r'[^a-zA-Z]', '', text)
        if len(meaningful_chars) < 5:
            return False
        
        return True

class ProductionChunker:
    """Production-grade chunking using RecursiveCharacterTextSplitter approach"""
    
    def __init__(self, chunk_size: int = 400, chunk_overlap: int = 75):
        """
        Initialize chunker with production settings
        
        Args:
            chunk_size: Target chunk size in tokens (300-500 recommended)
            chunk_overlap: Overlap between chunks (50-100 recommended)
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.separators = ["\n\n", "\n", ". ", "! ", "? ", "; ", ": ", " ", ""]
        
    def chunk_text(self, text: str, metadata: Dict[str, Any] = None) -> List[DocumentChunk]:
        """
        Chunk text using recursive character splitting
        
        Args:
            text: Input text to chunk
            metadata: Base metadata for chunks
            
        Returns:
            List of DocumentChunk objects
        """
        if not text or not ProductionTextNormalizer.is_meaningful_text(text):
            return []
        
        # Normalize text
        normalized_text = ProductionTextNormalizer.normalize_text(text)
        
        # Split into chunks
        chunks = self._recursive_split(normalized_text, self.chunk_size)
        
        # Create DocumentChunk objects (without embeddings initially)
        document_chunks = []
        for i, chunk_text in enumerate(chunks):
            if ProductionTextNormalizer.is_meaningful_text(chunk_text):
                chunk_metadata = {
                    **(metadata or {}),
                    'chunk_index': i,
                    'chunk_method': 'recursive_character',
                    'original_length': len(text),
                    'normalized_length': len(normalized_text)
                }
                
                # Create chunk without embedding (will be added later)
                chunk = DocumentChunk(
                    id=str(uuid.uuid4()),
                    text=chunk_text,
                    embedding=[],  # Will be populated by embedding service
                    metadata=chunk_metadata,
                    token_count=self._estimate_tokens(chunk_text)
                )
                document_chunks.append(chunk)
        
        return document_chunks
    
    def _recursive_split(self, text: str, chunk_size: int, depth: int = 0) -> List[str]:
        """Recursively split text using different separators with depth limit"""
        # Prevent excessive recursion
        if depth > 10:
            # Fall back to simple splitting if recursion gets too deep
            chunks = []
            for i in range(0, len(text), chunk_size - self.chunk_overlap):
                chunk = text[i:i + chunk_size]
                if chunk.strip():
                    chunks.append(chunk.strip())
            return chunks

        if len(text) <= chunk_size:
            return [text] if text.strip() else []

        # Try each separator
        for separator in self.separators:
            if separator in text:
                splits = text.split(separator)
                if len(splits) > 1:
                    # Process each split
                    chunks = []
                    current_chunk = ""

                    for split in splits:
                        # Add separator back (except for empty separator)
                        if separator and split:
                            split = split + separator

                        if len(current_chunk) + len(split) <= chunk_size:
                            current_chunk += split
                        else:
                            if current_chunk:
                                chunks.append(current_chunk.strip())

                            # If split is still too long, recursively split it
                            if len(split) > chunk_size:
                                chunks.extend(self._recursive_split(split, chunk_size, depth + 1))
                                current_chunk = ""
                            else:
                                current_chunk = split

                    if current_chunk:
                        chunks.append(current_chunk.strip())

                    return [chunk for chunk in chunks if chunk.strip()]

        # If no separator worked, split by character count
        chunks = []
        for i in range(0, len(text), chunk_size - self.chunk_overlap):
            chunk = text[i:i + chunk_size]
            if chunk.strip():
                chunks.append(chunk.strip())

        return chunks
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count (rough approximation)"""
        # Rough estimation: 1 token ≈ 4 characters for English
        return len(text) // 4

class ProductionEmbeddingService:
    """Production-grade embedding service with consistency guarantees"""
    
    def __init__(self):
        from docqa.vector_store import EmbeddingService
        self.embedding_service = EmbeddingService()
        self.model_name = self.embedding_service.model
        self.expected_dimension = 1536
        
        print(f"✅ Initialized embedding service with model: {self.model_name}")
        print(f"✅ Expected embedding dimension: {self.expected_dimension}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding with validation
        
        Args:
            text: Input text
            
        Returns:
            1536-dimensional embedding vector
            
        Raises:
            ValueError: If embedding dimension is incorrect
        """
        if not text or not text.strip():
            raise ValueError("Cannot generate embedding for empty text")
        
        # Normalize text before embedding
        normalized_text = ProductionTextNormalizer.normalize_text(text)
        
        # Generate embedding
        embedding = self.embedding_service.generate_embedding(normalized_text)
        
        # Validate dimension
        if len(embedding) != self.expected_dimension:
            raise ValueError(f"Invalid embedding dimension: {len(embedding)}, expected {self.expected_dimension}")
        
        # Normalize embedding for cosine similarity
        embedding_array = np.array(embedding, dtype=np.float32)
        normalized_embedding = embedding_array / np.linalg.norm(embedding_array)
        
        return normalized_embedding.tolist()
    
    def generate_batch_embeddings(self, texts: List[str], batch_size: int = 10) -> List[List[float]]:
        """Generate embeddings in batches with validation"""
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_embeddings = []
            
            for text in batch:
                try:
                    embedding = self.generate_embedding(text)
                    batch_embeddings.append(embedding)
                except Exception as e:
                    print(f"❌ Failed to generate embedding for text: {text[:50]}... Error: {e}")
                    # Skip this text
                    continue
            
            embeddings.extend(batch_embeddings)
            
            # Add small delay to avoid rate limiting
            if i + batch_size < len(texts):
                time.sleep(0.1)
        
        return embeddings

class ProductionVectorStore:
    """Production-grade vector storage with proper metadata"""
    
    def __init__(self):
        from docqa.config import get_config
        self.config = get_config()
        self.db_url = self.config.database_url.replace("postgresql+asyncpg://", "postgresql://")
    
    def clear_franchisor_embeddings(self, franchisor_id: str) -> bool:
        """Clear existing embeddings for a franchisor"""
        try:
            conn = psycopg2.connect(self.db_url)
            cur = conn.cursor()
            
            # Clear franchisor embedding
            cur.execute("""
                UPDATE franchisors 
                SET embedding = NULL, updated_at = NOW()
                WHERE id = %s
            """, (franchisor_id,))
            
            rows_updated = cur.rowcount
            conn.commit()
            
            print(f"✅ Cleared embeddings for franchisor {franchisor_id} ({rows_updated} rows updated)")
            return True
            
        except Exception as e:
            print(f"❌ Error clearing embeddings: {e}")
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()
    
    def store_franchisor_embedding(
        self, 
        franchisor_id: str, 
        text_content: str, 
        embedding: List[float],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Store franchisor embedding with proper validation"""
        try:
            # Validate embedding
            if len(embedding) != 1536:
                raise ValueError(f"Invalid embedding dimension: {len(embedding)}")
            
            conn = psycopg2.connect(self.db_url)
            cur = conn.cursor()
            
            # Store embedding as proper vector
            cur.execute("""
                UPDATE franchisors 
                SET embedding = %s::vector, updated_at = NOW()
                WHERE id = %s
            """, (embedding, franchisor_id))
            
            if cur.rowcount == 0:
                print(f"❌ No franchisor found with ID: {franchisor_id}")
                return False
            
            conn.commit()
            print(f"✅ Stored embedding for franchisor {franchisor_id}")
            return True
            
        except Exception as e:
            print(f"❌ Error storing embedding: {e}")
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def store_franchisor_chunks(
        self,
        franchisor_id: str,
        chunks: List,  # List of DocumentChunk objects
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Store franchisor chunks with embeddings in franchisor_chunks table"""
        try:
            import psycopg2
            conn = psycopg2.connect(self.db_url)
            cur = conn.cursor()

            # Create franchisor_chunks table if it doesn't exist
            cur.execute("""
                CREATE TABLE IF NOT EXISTS franchisor_chunks (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    franchisor_id UUID NOT NULL,
                    text TEXT NOT NULL,
                    embedding vector(1536),
                    chunk_index INTEGER NOT NULL,
                    token_count INTEGER NOT NULL,
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    CONSTRAINT fk_franchisor_chunks_franchisor
                        FOREIGN KEY (franchisor_id) REFERENCES franchisors(id) ON DELETE CASCADE
                )
            """)

            # Create indexes if they don't exist
            try:
                cur.execute("""
                    CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_embedding
                    ON franchisor_chunks USING ivfflat (embedding vector_cosine_ops)
                    WITH (lists = 100)
                """)
            except Exception as e:
                print(f"⚠️  Could not create vector index: {e}")
                # Continue anyway - the index is for performance, not functionality

            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_franchisor_id
                ON franchisor_chunks (franchisor_id)
            """)

            # Delete existing chunks for this franchisor
            cur.execute(
                "DELETE FROM franchisor_chunks WHERE franchisor_id = %s",
                (franchisor_id,)
            )

            # Insert new chunks
            for i, chunk in enumerate(chunks):
                if not chunk.embedding:
                    print(f"⚠️  Chunk missing embedding, skipping: {chunk.id}")
                    continue

                # Validate embedding dimension
                if len(chunk.embedding) != 1536:
                    print(f"❌ Invalid embedding dimension for chunk {chunk.id}: {len(chunk.embedding)}")
                    continue

                chunk_metadata = {**(metadata or {}), **chunk.metadata}

                cur.execute("""
                    INSERT INTO franchisor_chunks
                    (franchisor_id, text, embedding, chunk_index, token_count, metadata)
                    VALUES (%s, %s, %s::vector, %s, %s, %s)
                """, (
                    franchisor_id,
                    chunk.text,
                    chunk.embedding,
                    i,
                    chunk.token_count,
                    json.dumps(chunk_metadata)  # Serialize metadata to JSON
                ))

            conn.commit()
            print(f"✅ Stored {len(chunks)} franchisor chunks for {franchisor_id}")
            return True

        except Exception as e:
            print(f"❌ Error storing franchisor chunks: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def search_similar(
        self, 
        query_embedding: List[float], 
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        franchisor_id: Optional[str] = None
    ) -> List[RetrievalResult]:
        """
        Search for similar content with proper similarity calculation
        
        Args:
            query_embedding: 1536-dim query embedding
            top_k: Number of results to return (3-10 recommended)
            similarity_threshold: Minimum cosine similarity (0.7-0.8 recommended)
            franchisor_id: Optional franchisor filter
            
        Returns:
            List of RetrievalResult objects
        """
        try:
            # Validate query embedding
            if len(query_embedding) != 1536:
                raise ValueError(f"Invalid query embedding dimension: {len(query_embedding)}")
            
            conn = psycopg2.connect(self.db_url)
            cur = conn.cursor()
            
            # Search franchisors table
            if franchisor_id:
                cur.execute("""
                    SELECT
                        id,
                        name,
                        region,
                        brochure_url,
                        1 - (embedding <=> %s::vector) as similarity_score
                    FROM franchisors
                    WHERE id = %s 
                        AND embedding IS NOT NULL
                        AND is_active = true
                        AND is_deleted = false
                        AND (1 - (embedding <=> %s::vector)) >= %s
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s
                """, (query_embedding, franchisor_id, query_embedding, similarity_threshold, query_embedding, top_k))
            else:
                cur.execute("""
                    SELECT
                        id,
                        name,
                        region,
                        brochure_url,
                        1 - (embedding <=> %s::vector) as similarity_score
                    FROM franchisors
                    WHERE embedding IS NOT NULL
                        AND is_active = true
                        AND is_deleted = false
                        AND (1 - (embedding <=> %s::vector)) >= %s
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s
                """, (query_embedding, query_embedding, similarity_threshold, query_embedding, top_k))
            
            results = []
            for row in cur.fetchall():
                result = RetrievalResult(
                    chunk_id=row[0],
                    text=f"Franchisor: {row[1]} (Region: {row[2]})",
                    similarity_score=float(row[4]),
                    metadata={
                        'type': 'franchisor',
                        'name': row[1],
                        'region': row[2],
                        'brochure_url': row[3]
                    },
                    source_info=f"Franchisor: {row[1]}"
                )
                results.append(result)
            
            print(f"✅ Found {len(results)} results with similarity >= {similarity_threshold}")
            return results
            
        except Exception as e:
            print(f"❌ Error in similarity search: {e}")
            return []
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

async def main():
    """Main function to demonstrate the production RAG system"""
    print("🚀 Production-Grade RAG System")
    print("=" * 50)
    
    # Initialize services
    normalizer = ProductionTextNormalizer()
    chunker = ProductionChunker(chunk_size=400, chunk_overlap=75)
    embedding_service = ProductionEmbeddingService()
    vector_store = ProductionVectorStore()
    
    print("✅ All services initialized successfully")
    
    return {
        'normalizer': normalizer,
        'chunker': chunker,
        'embedding_service': embedding_service,
        'vector_store': vector_store
    }

if __name__ == "__main__":
    asyncio.run(main())
