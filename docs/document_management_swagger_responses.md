# Document Management API - Swagger Response Examples

This document provides comprehensive sample responses for all Document Management API endpoints for Swagger documentation.

## 📋 **1. GET /api/documents/ - List Documents**

### ✅ **200 Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Documents Retrieved",
    "description": "Successfully retrieved 2 documents"
  },
  "data": {
    "items": [
      {
        "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
        "name": "GrowthHive Signed Agreement",
        "description": "Official signed agreement document for GrowthHive project",
        "file_type": "application/pdf",
        "file_size": "7203206",
        "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
        "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
        "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
        "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
        "is_active": true,
        "is_deleted": false,
        "created_at": "2025-06-27T16:23:20.742000+00:00",
        "updated_at": "2025-06-27T16:28:01.371000+00:00",
        "deleted_at": null
      }
    ],
    "total_count": 2,
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 2,
      "pages": 1
    }
  },
  "error_code": null
}
```

### ❌ **401 Unauthorized Response**
```json
{
  "success": false,
  "message": {
    "title": "Authentication Required",
    "description": "Valid authentication token is required"
  },
  "data": null,
  "error_code": "UNAUTHORIZED"
}
```

### ❌ **500 Internal Server Error Response**
```json
{
  "success": false,
  "message": {
    "title": "Error Retrieving Documents",
    "description": "Database connection failed"
  },
  "data": null,
  "error_code": "DATABASE_ERROR"
}
```

---

## 📄 **2. GET /api/documents/{document_id} - Get Document by ID**

### ✅ **200 Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Document Retrieved",
    "description": "Document retrieved successfully"
  },
  "data": {
    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
    "name": "GrowthHive Signed Agreement",
    "description": "Official signed agreement document for GrowthHive project",
    "file_type": "application/pdf",
    "file_size": "7203206",
    "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-06-27T16:23:20.742000+00:00",
    "updated_at": "2025-06-27T16:28:01.371000+00:00",
    "deleted_at": null
  },
  "error_code": null
}
```

### ❌ **404 Not Found Response**
```json
{
  "success": false,
  "message": {
    "title": "Document Not Found",
    "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
  },
  "data": null,
  "error_code": "NOT_FOUND"
}
```

---

## ➕ **3. POST /api/documents/ - Create Document**

### ✅ **201 Created Response**
```json
{
  "success": true,
  "message": {
    "title": "Document Created",
    "description": "Document created successfully"
  },
  "data": {
    "id": "4b938d2c-c958-4e05-9a69-ed99fa4d340c",
    "name": "Test Document",
    "description": "A test document for the Document Management system",
    "file_type": "application/pdf",
    "file_size": "1024",
    "file_path": "test/path/document.pdf",
    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/test/path/document.pdf",
    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-06-27T16:23:00.800000+00:00",
    "updated_at": "2025-06-27T16:23:00.800000+00:00",
    "deleted_at": null
  },
  "error_code": null
}
```

### ❌ **400 Validation Error Response**
```json
{
  "success": false,
  "message": {
    "title": "Validation Error",
    "description": "Document name is required"
  },
  "data": null,
  "error_code": "VALIDATION_ERROR"
}
```

---

## 📤 **4. POST /api/documents/upload - Upload Document File**

### ✅ **201 Upload Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Document Uploaded",
    "description": "Document uploaded successfully"
  },
  "data": {
    "id": "ed8b831d-b082-4eea-962e-65082f2b1ef3",
    "name": "Franchisor Agreement Document",
    "description": "Agreement document linked to specific franchisor",
    "file_type": "application/pdf",
    "file_size": "7203206",
    "file_path": "growthhive/document/20250627_162709_bdb21ea1656a.pdf",
    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162709_bdb21ea1656a.pdf",
    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-06-27T16:27:10.859000+00:00",
    "updated_at": "2025-06-27T16:27:10.859000+00:00",
    "deleted_at": null
  },
  "error_code": null
}
```

### ❌ **400 Invalid File Type Response**
```json
{
  "success": false,
  "message": {
    "title": "Upload Failed",
    "description": "File type not allowed. Allowed types: .pdf, .png, .docx, .jpg, .doc, .jpeg"
  },
  "data": null,
  "error_code": "UPLOAD_ERROR"
}
```

### ❌ **400 No File Provided Response**
```json
{
  "success": false,
  "message": {
    "title": "Invalid File",
    "description": "No file provided"
  },
  "data": null,
  "error_code": "VALIDATION_ERROR"
}
```

---

## 📤📤 **5. POST /api/documents/bulk-upload - Bulk Upload Documents**

### ✅ **201 All Files Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Bulk Upload Completed",
    "description": "Uploaded 2 documents, 0 failed"
  },
  "data": {
    "uploaded_documents": [
      {
        "id": "doc1-uuid",
        "name": "document1.pdf",
        "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162709_doc1.pdf",
        "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f"
      },
      {
        "id": "doc2-uuid",
        "name": "document2.pdf",
        "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162710_doc2.pdf",
        "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f"
      }
    ],
    "failed_uploads": [],
    "total_uploaded": 2,
    "total_failed": 0
  },
  "error_code": null
}
```

### ⚠️ **201 Partial Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Bulk Upload Completed",
    "description": "Uploaded 1 documents, 1 failed"
  },
  "data": {
    "uploaded_documents": [
      {
        "id": "doc1-uuid",
        "name": "document1.pdf",
        "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162709_doc1.pdf"
      }
    ],
    "failed_uploads": [
      {
        "filename": "invalid_file.txt",
        "error": "File type not allowed. Allowed types: .pdf, .png, .docx, .jpg, .doc, .jpeg"
      }
    ],
    "total_uploaded": 1,
    "total_failed": 1
  },
  "error_code": null
}
```

---

## ✏️ **6. PUT /api/documents/{document_id} - Update Document**

### ✅ **200 Update Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Document Updated",
    "description": "Document updated successfully"
  },
  "data": {
    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
    "name": "Updated Document Name",
    "description": "Updated to associate with franchisor",
    "file_type": "application/pdf",
    "file_size": "7203206",
    "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-06-27T16:23:20.742000+00:00",
    "updated_at": "2025-06-27T16:28:01.371000+00:00",
    "deleted_at": null
  },
  "error_code": null
}
```

---

## 🗑️ **7. DELETE /api/documents/{document_id} - Delete Document**

### ✅ **200 Delete Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Document Deleted",
    "description": "Document deleted successfully"
  },
  "data": {
    "document_id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
    "deleted": true
  },
  "error_code": null
}
```

---

## 🗑️🗑️ **8. POST /api/documents/bulk-delete - Bulk Delete Documents**

### ✅ **200 Bulk Delete Success Response**
```json
{
  "success": true,
  "message": {
    "title": "Bulk Delete Completed",
    "description": "Successfully deleted 3 documents"
  },
  "data": {
    "deleted_count": 3,
    "requested_count": 3
  },
  "error_code": null
}
```

### ❌ **400 Validation Error Response**
```json
{
  "success": false,
  "message": {
    "title": "Validation Error",
    "description": "Document IDs list cannot be empty"
  },
  "data": null,
  "error_code": "VALIDATION_ERROR"
}
```

---

## 🔄 **9. PATCH /api/documents/{document_id}/status - Update Document Status**

### ✅ **200 Activate Document Response**
```json
{
  "success": true,
  "message": {
    "title": "Document Status Updated",
    "description": "Document activated successfully"
  },
  "data": {
    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
    "name": "GrowthHive Signed Agreement",
    "description": "Official signed agreement document for GrowthHive project",
    "file_type": "application/pdf",
    "file_size": "7203206",
    "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-06-27T16:23:20.742000+00:00",
    "updated_at": "2025-06-27T16:30:01.371000+00:00",
    "deleted_at": null
  },
  "error_code": null
}
```

### ✅ **200 Deactivate Document Response**
```json
{
  "success": true,
  "message": {
    "title": "Document Status Updated",
    "description": "Document deactivated successfully"
  },
  "data": {
    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
    "name": "GrowthHive Signed Agreement",
    "is_active": false,
    "updated_at": "2025-06-27T16:30:01.371000+00:00"
  },
  "error_code": null
}
```

---

## 🔧 **Common Error Responses for All Endpoints**

### ❌ **401 Unauthorized (All Endpoints)**
```json
{
  "success": false,
  "message": {
    "title": "Authentication Required",
    "description": "Valid authentication token is required"
  },
  "data": null,
  "error_code": "UNAUTHORIZED"
}
```

### ❌ **404 Not Found (Get, Update, Delete, Status endpoints)**
```json
{
  "success": false,
  "message": {
    "title": "Document Not Found",
    "description": "Document with ID {document_id} not found"
  },
  "data": null,
  "error_code": "NOT_FOUND"
}
```

### ❌ **500 Internal Server Error (All Endpoints)**
```json
{
  "success": false,
  "message": {
    "title": "Error {Operation} Document",
    "description": "Database error occurred"
  },
  "data": null,
  "error_code": "DATABASE_ERROR"
}
```

---

## 📝 **Request Body Examples**

### **Create Document Request**
```json
{
  "name": "Test Document",
  "description": "A test document for the Document Management system",
  "file_path": "test/path/document.pdf",
  "file_type": "application/pdf",
  "file_size": "1024",
  "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
  "is_active": true
}
```

### **Update Document Request**
```json
{
  "name": "Updated Document Name",
  "description": "Updated description",
  "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
  "is_active": true
}
```

### **Bulk Delete Request**
```json
{
  "document_ids": [
    "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
    "4b938d2c-c958-4e05-9a69-ed99fa4d340c",
    "ed8b831d-b082-4eea-962e-65082f2b1ef3"
  ]
}
```

### **Update Status Request**
```json
{
  "is_active": false
}
```

---

## 🔍 **Query Parameters Examples**

### **List Documents with Filters**
```
GET /api/documents/?skip=0&limit=10&search=agreement&franchisor_id=a6fc7a0e-f1ee-431f-a006-90452457cd1f&file_type=pdf&is_active=true
```

### **Upload Document Form Data**
```
POST /api/documents/upload
Content-Type: multipart/form-data

file: [PDF file]
name: "Agreement Document"
description: "Official agreement"
franchisor_id: "a6fc7a0e-f1ee-431f-a006-90452457cd1f"
is_active: true
```

---

## 📊 **Field Descriptions**

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Unique document identifier |
| `name` | String | Document name (required) |
| `description` | String | Document description (optional) |
| `file_type` | String | MIME type of the file |
| `file_size` | String | File size in bytes |
| `file_path` | String | S3 storage path |
| `file_url` | String | Full S3 URL for download |
| `user_id` | UUID | ID of user who created the document |
| `franchisor_id` | UUID | Associated franchisor ID (optional) |
| `is_active` | Boolean | Whether document is active |
| `is_deleted` | Boolean | Whether document is soft deleted |
| `created_at` | DateTime | Creation timestamp |
| `updated_at` | DateTime | Last update timestamp |
| `deleted_at` | DateTime | Deletion timestamp (null if not deleted) |

---

## 🎯 **Status Codes Summary**

| Endpoint | Success | Error Codes |
|----------|---------|-------------|
| GET /documents/ | 200 | 401, 500 |
| GET /documents/{id} | 200 | 401, 404, 500 |
| POST /documents/ | 201 | 400, 401, 500 |
| POST /documents/upload | 201 | 400, 401, 500 |
| POST /documents/bulk-upload | 201 | 400, 401, 500 |
| PUT /documents/{id} | 200 | 400, 401, 404, 500 |
| DELETE /documents/{id} | 200 | 401, 404, 500 |
| POST /documents/bulk-delete | 200 | 400, 401, 500 |
| PATCH /documents/{id}/status | 200 | 400, 401, 404, 500 |

---

## 🚀 **Usage Tips**

1. **Authentication**: All endpoints require a valid JWT token in the Authorization header
2. **File Types**: Supported file types are .pdf, .png, .docx, .jpg, .doc, .jpeg
3. **Franchisor Mapping**: Use `franchisor_id` to associate documents with specific franchisors
4. **Soft Delete**: Documents are soft deleted (marked as deleted, not physically removed)
5. **S3 URLs**: Full download URLs are provided in responses for frontend consumption
6. **Pagination**: Use `skip` and `limit` parameters for efficient data loading
7. **Search**: Search works across document names and descriptions
8. **Filtering**: Multiple filters can be combined for precise results
