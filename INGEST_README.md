# Document Ingestion System

A production-grade, modular Python system for ingesting documents with chart extraction, OCR, language detection, translation, and vector storage capabilities.

## 🎯 Features

### Core Capabilities
- **Multi-format Support**: PDF, DOCX, DOC, PPTX, PPT, CSV, TXT, MD, HTML, images (JPG, PNG, WEBP, GIF), ZIP archives
- **Chart Extraction**: AI-powered chart detection and captioning using GPT-4 Vision
- **OCR Processing**: Text extraction from images using pytesseract with preprocessing
- **Language Detection**: Automatic language detection using langdetect
- **Translation**: AI-powered translation using OpenAI GPT models
- **Vector Storage**: Document embedding and FAISS-based similarity search
- **Concurrent Processing**: Multi-threaded processing with configurable timeouts

### File Type Handlers
- **PDF**: PyMuPDF for text and image extraction
- **Word Documents**: python-docx for DOCX files (DOC requires conversion)
- **PowerPoint**: python-pptx for PPTX files (PPT requires conversion)
- **Images**: OpenCV + pytesseract for OCR and chart detection
- **CSV**: Automatic delimiter detection and structured data processing
- **Text/Markdown**: Encoding detection and structure-aware parsing
- **HTML**: Clean content extraction using readability-lxml
- **ZIP**: Recursive extraction and processing of archive contents

### AI-Powered Features
- **Chart Captioning**: GPT-4 Vision API for detailed chart descriptions
- **Language Translation**: GPT-4 for high-quality document translation
- **Vector Embeddings**: OpenAI text-embedding-3-small for semantic search

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r ingest_requirements.txt

# Set up environment variables
export OPENAI_API_KEY="your-openai-api-key"
export AWS_ACCESS_KEY_ID="your-aws-key"  # For S3 support
export AWS_SECRET_ACCESS_KEY="your-aws-secret"
```

### Basic Usage

```bash
# Ingest a single document
python ingest.py document.pdf

# Ingest with chart extraction
python ingest.py document.pdf --extract-charts

# Ingest with translation
python ingest.py document.pdf --translate --target-language en

# Ingest from S3
python ingest.py s3://bucket/document.docx --extract-charts

# Process directory recursively
python ingest.py documents/ --recursive --extract-charts

# Batch processing with custom settings
python ingest.py documents/ --recursive --workers 8 --chunk-size 500
```

### Python API

```python
from ingest import DocumentIngestionSystem, IngestionConfig
from ingest.core import LanguageCode

# Create configuration
config = IngestionConfig(
    extract_charts=True,
    translate=True,
    target_language=LanguageCode.ENGLISH,
    chunk_size=400,
    chunk_overlap=50,
)

# Initialize system
system = DocumentIngestionSystem(config)

# Process single document
result = system.ingest_document("document.pdf")

if result.success:
    print(f"Created {len(result.chunks)} chunks")
    for chunk in result.chunks:
        print(f"- {chunk.text[:100]}...")
else:
    print(f"Error: {result.error_message}")

# Process multiple documents
sources = ["doc1.pdf", "doc2.docx", "doc3.pptx"]
results = system.ingest_multiple(sources)

successful = [r for r in results if r.success]
print(f"Successfully processed {len(successful)} documents")
```

## 📊 Output Format

Each document is processed into `DocumentChunk` objects with the following structure:

```json
{
  "id": "uuid-string",
  "text": "extracted text content",
  "source": "filename.pdf",
  "file_type": "pdf",
  "language": "en",
  "start_page": 1,
  "end_page": 1,
  "chunk_index": 0,
  "token_count": 150,
  "chart_captions": [
    {
      "chart_type": "bar",
      "description": "Quarterly sales showing 20% growth",
      "data_insights": ["Q4 highest sales", "Upward trend"],
      "confidence_score": 0.85
    }
  ],
  "metadata": {
    "processing_time": 1.23,
    "extraction_method": "pymupdf"
  }
}
```

## 🔧 Configuration

### Command Line Options

```bash
python ingest.py --help

Options:
  --extract-charts      Enable chart detection and AI captioning
  --translate          Enable automatic translation
  --target-language    Target language for translation (default: en)
  --chunk-size         Maximum tokens per chunk (default: 400)
  --chunk-overlap      Token overlap between chunks (default: 50)
  --max-file-size      Maximum file size in MB (default: 100)
  --timeout            Processing timeout in seconds (default: 30)
  --workers            Number of concurrent workers (default: 4)
  --recursive          Process directories recursively
  --log-level          Logging level (DEBUG, INFO, WARNING, ERROR)
  --log-json           Output logs in JSON format
```

### Environment Variables

```bash
# Required for AI features
OPENAI_API_KEY="your-openai-api-key"
OPENAI_VISION_MODEL="gpt-4o"  # Optional, defaults to gpt-4o

# Required for S3 support
AWS_ACCESS_KEY_ID="your-aws-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret"
AWS_DEFAULT_REGION="us-east-1"

# Optional OCR configuration
TESSERACT_CMD="/usr/bin/tesseract"  # Path to tesseract executable
```

## 🏗️ Architecture

The system follows a modular architecture with clear separation of concerns:

```
ingest/
├── core/                 # Core models and interfaces
│   ├── models.py        # Pydantic data models
│   ├── interfaces.py    # Abstract base classes
│   └── exceptions.py    # Custom exceptions
├── file_handlers/       # File type specific processors
│   ├── pdf_handler.py   # PDF processing
│   ├── word_handler.py  # Word document processing
│   ├── image_handler.py # Image and OCR processing
│   └── ...
├── chart_extract/       # Chart detection and captioning
│   ├── chart_detector.py    # Computer vision detection
│   ├── vision_captioner.py  # AI-powered captioning
│   └── chart_extractor_impl.py
├── ocr/                 # OCR and language processing
│   ├── ocr_processor_impl.py
│   ├── language_processor_impl.py
│   └── text_chunker_impl.py
├── utils/               # Utilities and helpers
│   ├── logger.py        # Structured logging
│   └── file_validator.py
├── file_loader.py       # File loading and validation
├── vector_store.py      # Vector embeddings and storage
└── ingest.py           # Main CLI interface
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Install test dependencies
pip install pytest pytest-cov pytest-mock

# Run all tests
pytest

# Run with coverage
pytest --cov=ingest --cov-report=html

# Run specific test modules
pytest tests/test_file_handlers.py
pytest tests/test_vector_store.py
pytest tests/test_core_models.py
```

The test suite includes:
- Unit tests for all file handlers
- Core model validation tests
- Vector storage functionality tests
- Mock-based tests for external APIs
- Integration tests for end-to-end workflows

## 🐳 Docker Deployment

```dockerfile
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-eng \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY ingest_requirements.txt .
RUN pip install --no-cache-dir -r ingest_requirements.txt

# Copy application code
COPY ingest/ ./ingest/
COPY ingest.py .

# Create data directory for vector storage
RUN mkdir -p /app/data

# Set environment variables
ENV PYTHONPATH=/app
ENV FAISS_INDEX_PATH=/app/data/faiss_index

# Expose port for potential web interface
EXPOSE 8000

# Default command
CMD ["python", "ingest.py", "--help"]
```

Build and run:

```bash
# Build image
docker build -t document-ingestion .

# Run with environment variables
docker run -e OPENAI_API_KEY="your-key" \
           -v $(pwd)/documents:/app/documents \
           -v $(pwd)/data:/app/data \
           document-ingestion \
           python ingest.py /app/documents --recursive --extract-charts
```

## 📈 Performance

### Benchmarks
- **PDF Processing**: ~2-5 pages/second (text extraction)
- **Chart Detection**: ~1-3 seconds per chart (with AI captioning)
- **OCR Processing**: ~0.5-2 seconds per image
- **Vector Embedding**: ~100-500 chunks/second (batch processing)
- **Concurrent Processing**: Scales linearly with worker count

### Optimization Tips
1. **Batch Processing**: Use multiple workers for large document sets
2. **Chunk Size**: Optimize chunk size based on your use case (200-800 tokens)
3. **Chart Extraction**: Disable if not needed to improve speed
4. **File Size Limits**: Set appropriate limits to prevent memory issues
5. **Vector Storage**: Use SSD storage for FAISS indices

## 🔒 Security

- **Input Validation**: Comprehensive file validation and sanitization
- **Timeout Protection**: Configurable timeouts prevent hanging processes
- **Memory Management**: Streaming processing for large files
- **Path Traversal Protection**: Safe file extraction from archives
- **API Key Security**: Environment variable based configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes following the coding standards
4. Add tests for new functionality
5. Run the test suite (`pytest`)
6. Format code (`black . && isort .`)
7. Commit changes (`git commit -m 'Add amazing feature'`)
8. Push to branch (`git push origin feature/amazing-feature`)
9. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Check the documentation
- Review the test suite for usage examples

## 🔮 Roadmap

- [ ] Support for additional file formats (RTF, ODT, etc.)
- [ ] Advanced table extraction and processing
- [ ] Real-time document processing API
- [ ] Integration with more vector databases
- [ ] Advanced chart type detection
- [ ] Batch translation optimization
- [ ] Web-based management interface
- [ ] Kubernetes deployment manifests
