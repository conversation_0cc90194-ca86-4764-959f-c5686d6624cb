#!/usr/bin/env python3
"""
Demonstration of the Enhanced RAG-powered Universal QnA System

This script demonstrates the RAG (Retrieval-Augmented Generation) capabilities
including vector storage, document reuse, and cross-document search.
"""

import os


def demo_rag_capabilities():
    """Demonstrate RAG system capabilities."""
    print("🚀 Enhanced Universal QnA System with RAG Demonstration")
    print("=" * 70)
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        return False
    
    print(f"✅ OpenAI API Key found: {api_key[:10]}...")
    
    print("\n🎯 RAG System Features:")
    features = [
        "📚 **Vector Storage**: Persistent storage of document embeddings",
        "♻️  **Document Reuse**: Automatic detection of previously processed documents",
        "🔍 **Semantic Search**: Search across all stored documents",
        "🧠 **RAG Answers**: Answers using information from multiple documents",
        "📊 **Cross-Document Analysis**: Compare and analyze multiple documents",
        "💾 **Persistent Memory**: Information persists across sessions",
        "🖼️  **Image Analysis**: GPT-4 Vision analysis stored with documents"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n📋 Demo Test Cases:")
    print("=" * 40)
    
    # Test cases to demonstrate RAG
    test_cases = [
        {
            "step": 1,
            "description": "Process first document (PDF franchise brochure)",
            "command": 'python3 qna_rag.py "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/brochure/20250701_113703_07ce376fa750.pdf" "What is this document about?"',
            "expected": "Document processed and stored in vector database"
        },
        {
            "step": 2,
            "description": "Process second document (Excel graphs)",
            "command": 'python3 qna_rag.py "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/graphs+for+Xcell+to+look+at.xlsx" "What data is in this Excel file?"',
            "expected": "Second document processed and stored"
        },
        {
            "step": 3,
            "description": "Re-process first document (should use cache)",
            "command": 'python3 qna_rag.py "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/brochure/20250701_113703_07ce376fa750.pdf" "What are the costs?"',
            "expected": "Document reused from cache (faster processing)"
        },
        {
            "step": 4,
            "description": "Cross-document search",
            "command": 'python3 qna_rag.py --search "financial data and costs"',
            "expected": "Search results from both documents"
        },
        {
            "step": 5,
            "description": "System statistics",
            "command": 'python3 qna_rag.py --stats',
            "expected": "Shows stored documents and vector counts"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 Step {test_case['step']}: {test_case['description']}")
        print(f"🔧 Command: {test_case['command']}")
        print(f"📊 Expected: {test_case['expected']}")
        print("-" * 50)
    
    return True


def show_usage_examples():
    """Show usage examples for the RAG system."""
    print("\n💡 RAG System Usage Examples:")
    print("=" * 40)
    
    examples = [
        {
            "category": "📄 Document Processing",
            "examples": [
                'python3 qna_rag.py "document.pdf" "What is this about?"',
                'python3 qna_rag.py "data.xlsx" "Analyze the data"',
                'python3 qna_rag.py "presentation.pptx"  # Interactive mode'
            ]
        },
        {
            "category": "🔍 Cross-Document Search",
            "examples": [
                'python3 qna_rag.py --search "financial projections"',
                'python3 qna_rag.py --search "business model"',
                'python3 qna_rag.py --search "charts and graphs"'
            ]
        },
        {
            "category": "📊 System Management",
            "examples": [
                'python3 qna_rag.py --stats  # Show system statistics',
                '# Interactive commands:',
                '# "stats" - Show statistics',
                '# "search: query" - Search all documents',
                '# "quit" - Exit'
            ]
        },
        {
            "category": "🧠 RAG-Enhanced Questions",
            "examples": [
                '"Compare the financial data across all documents"',
                '"What visual elements are common in the documents?"',
                '"Find all mentions of costs and pricing"',
                '"Summarize key insights from all processed documents"'
            ]
        }
    ]
    
    for example_group in examples:
        print(f"\n{example_group['category']}:")
        for example in example_group['examples']:
            print(f"   {example}")


def show_rag_benefits():
    """Show benefits of the RAG system."""
    print("\n🎯 RAG System Benefits:")
    print("=" * 30)
    
    benefits = [
        {
            "benefit": "⚡ **Performance**",
            "description": "Repeated documents load instantly from cache"
        },
        {
            "benefit": "🧠 **Intelligence**", 
            "description": "Answers use information from multiple documents"
        },
        {
            "benefit": "🔍 **Discovery**",
            "description": "Find relevant information across all processed documents"
        },
        {
            "benefit": "💾 **Persistence**",
            "description": "Document knowledge persists across sessions"
        },
        {
            "benefit": "📊 **Analytics**",
            "description": "Compare and analyze multiple documents together"
        },
        {
            "benefit": "🎯 **Relevance**",
            "description": "Semantic search finds contextually relevant content"
        }
    ]
    
    for benefit in benefits:
        print(f"\n{benefit['benefit']}")
        print(f"   {benefit['description']}")


def main():
    """Main demonstration function."""
    print("🚀 RAG-Enhanced Universal QnA System")
    print("=" * 50)
    
    # Demo capabilities
    if demo_rag_capabilities():
        show_usage_examples()
        show_rag_benefits()
        
        print("\n✨ Ready to Test!")
        print("🎯 Try processing the same document twice to see caching in action")
        print("🔍 Use cross-document search to find information across all files")
        print("🧠 Ask questions that require information from multiple documents")
        
        print("\n🚀 Start with:")
        print('   python3 qna_rag.py "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/brochure/20250701_113703_07ce376fa750.pdf"')
    else:
        print("\n❌ Demo setup failed!")


if __name__ == "__main__":
    main()
